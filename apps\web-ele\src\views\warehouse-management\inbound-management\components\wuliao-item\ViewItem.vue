<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from './types/index.ts';

import { ref, watch } from 'vue';

import StockedInventoryPopover from './components/StockedInventoryPopover.vue';
import ViewMaterialItem from './components/ViewMaterialItem.vue';
import { initWarehouseData } from './utils';

const props = defineProps({
  materialItemData: {
    type: Object as PropType<MaterialItem.MaterialInfo>,
    default: () => ({}),
  },
  inBoundDocId: {
    type: String,
    default: '',
  },
});

// 合并后每个仓库有效的数据（过滤出物料信息中，仓库在可入库仓库列表中的数据）
const warehouseItemList = ref<MaterialItem.WarehouseItemDataType[]>([]);

watch(
  () => props.materialItemData,
  async (newVal: MaterialItem.MaterialInfo) => {
    const { ItemList } = initWarehouseData(newVal.itemList);
    warehouseItemList.value = ItemList;
  },
  { immediate: true },
);
</script>

<template>
  <template v-for="item in warehouseItemList" :key="item.warehouseId">
    <ViewMaterialItem :warehouse-item-data="item">
      <template #inventory-wrapper>
        <StockedInventoryPopover
          :doc-id="inBoundDocId"
          :material-id="materialItemData.materialId"
          :warehouse-id="item.warehouseId"
          class="ml-2"
        />
      </template>
    </ViewMaterialItem>
  </template>
</template>
