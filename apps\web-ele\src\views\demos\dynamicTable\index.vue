<script lang="ts" setup>
import type { OnActionClickParams, VxeGridProps } from '@girant/adapter';

import { ref } from 'vue';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElButton } from 'element-plus';

const gridTable = ref();
const tableOptions = ref({ tableTitle: '测试表格' });
const gridTableData = [
  { d1: '213', d2: '333', d5: true, d6: '4' },
  { d1: '654', d2: '888', d5: false, d6: '1' },
];

// 定义 row 的类型
interface RowType {
  d1: string;
  d2: string;
  d9: number;
  d3: string;
  d4: string;
  d5: boolean;
  d6: string;
  d7: string[];
  d8: string[];
}

// 省市数据
const provinceCityData = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'dongcheng', label: '东城区' },
      { value: 'xicheng', label: '西城区' },
      { value: 'chaoyang', label: '朝阳区' },
    ],
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' },
      { value: 'changning', label: '长宁区' },
    ],
  },
  {
    value: 'guangdong',
    label: '广东省',
    children: [
      { value: 'guangzhou', label: '广州市' },
      { value: 'shenzhen', label: '深圳市' },
      { value: 'dongguan', label: '东莞市' },
    ],
  },
];

const filterTreeLabel = (data: any[], value: string): null | string =>
  data
    .flatMap((item) => [
      item.value === value ? item.label : null,
      item.children ? filterTreeLabel(item.children, value) : null,
    ])
    .find(Boolean) || null;

const optionData = [
  {
    value: '1',
    label: 'Option1',
  },
  {
    value: '2',
    label: 'Option2',
  },
  {
    value: '3',
    label: 'Option3',
  },
  {
    value: '4',
    label: 'Option4',
  },
  {
    value: '5',
    label: 'Option5',
  },
];

const gridOptions = ref<VxeGridProps>({
  height: 500,
  columns: [
    {
      field: 'd1',
      title: '输入框',
      editRender: {
        name: 'CellInput',
        placeholder: '请输入',
        props: {
          placeholder: '请输入',
        },
        attrs: {
          // onBlur: (e: OnActionClickParams, row: any) => {
          //   console.log('输入框失焦', e);
          //   console.log('输入框失焦', row);
          // },
          onChange: (row: RowType) => {
            row.d2 = '2024-8-6';
          },
        },
      },
    },
    {
      field: 'd2',
      title: '输入框',
      editRender: {
        name: 'CellInput',
        placeholder: '请输入',
        props: {
          placeholder: '请输入',
        },
      },
    },
    {
      field: 'd9',
      title: '数值框',
      editRender: {
        name: 'CellInputNumber',
        placeholder: '请输入',
        props: ({ row }: { row: RowType }) => {
          return {
            placeholder: '请输入',
            disabled: !row.d1,
          };
        },
      },
    },
    {
      field: 'd3',
      title: '时间',
      editRender: {
        name: 'CellDatePicker',
        placeholder: '请选择',
        props: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择',
        },
      },
    },
    {
      field: 'd4',
      title: '时间区间',
      editRender: {
        name: 'CellDatePicker',
        placeholder: '请选择',
        props: {
          format: 'YYYY-MM-DD',
          valueFormat: 'YYYY-MM-DD',
          placeholder: '请选择',
          startPlaceholder: '开始日期',
          endPlaceholder: '结束日期',
          type: 'daterange',
        },
      },
    },
    {
      title: '是否有效',
      field: 'd5',
      formatter: ({ cellValue }) => {
        return cellValue === false ? '禁用' : '启用';
      },
      editRender: {
        name: 'CellSwitch',
        placeholder: '请选择',
        attrs: {
          activeText: '启用',
          inactiveText: '禁用',
        },
      },
    },
    {
      title: '下拉框',
      field: 'd6',
      formatter: ({ cellValue }) => {
        const item = optionData.find(
          (item: { label: string; value: string }) => item.value === cellValue,
        );
        return item ? `${item?.label}` : '';
      },
      editRender: {
        name: 'CellSelect',
        placeholder: '请选择',
        props: {
          options: optionData,
          isShowlabel: true,
        },
        attrs: {
          placeholder: '请选择',
        },
      },
    },
    {
      title: '级联',
      field: 'd7',
      formatter: ({ cellValue }) => {
        if (cellValue) {
          const matchedLabels = cellValue
            .map((v: string) => filterTreeLabel(provinceCityData, v))
            .filter(Boolean)
            .join('/');
          return matchedLabels;
        } else {
          return '';
        }
      },
      editRender: {
        name: 'CellCascader',
        placeholder: '请选择',
        props: {
          options: provinceCityData,
          clearable: true,
          filterable: true,
          placeholder: '请选择',
        },
      },
    },
    {
      title: '树选择',
      field: 'd8',
      formatter: ({ cellValue }) => {
        if (cellValue) {
          const matchedLabels = cellValue
            .map((v: string) => filterTreeLabel(provinceCityData, v))
            .filter(Boolean)
            .join(',');
          return matchedLabels;
        } else {
          return '';
        }
      },
      editRender: {
        name: 'CellTreeSelect',
        placeholder: '请选择',
        props: {
          data: provinceCityData,
          placeholder: '请选择',
          multiple: true,
          showCheckbox: true,
          renderAfterExpand: false,
        },
      },
    },
    {
      title: '操作',
      align: 'center',
      width: 250,
      cellRender: {
        attrs: {
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['edit', 'revert', 'copy', 'cancel', 'delete'],
      },
      field: 'operation',
    },
  ],
  editRules: {
    d1: [
      { required: true, message: '姓名不能为空', trigger: 'blur' },
      {
        min: 2,
        max: 10,
        message: '姓名长度必须在 2 到 10 个字符之间',
        trigger: 'blur',
      },
    ],
    d2: [{ required: true, message: '姓名不能为空', trigger: 'blur' }],
  },
});

function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'cancel': {
      gridTable.value.cancelRow(e.row);
      break;
    }
    case 'copy': {
      gridTable.value.insertCopyRow(e.row);
      break;
    }
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
    case 'edit': {
      gridTable.value.editRow(e.row);
      break;
    }
    case 'revert': {
      gridTable.value.revertRow(e.row);
      break;
    }
  }
}

const getTableData = async () => {
  // const data = await gridTable.value.getTableData();
  // console.log(data);
};

const validate = async () => {
  const isValid = await gridTable.value.tableValidate();

  if (isValid) {
    // 存在验证不通过的数据
    // console.log(3232);
  } else {
    // console.log(5555);
    // 所有数据验证通过
  }
};

const setTableData = async () => {
  await gridTable.value.setTableData(gridTableData);
};
</script>

<template>
  <div>
    <DynamicTable
      ref="gridTable"
      :grid-options="gridOptions"
      :table-options="tableOptions"
    >
      <template #toolbar-tools>
        <ElButton @click="setTableData">赋值</ElButton>
        <ElButton @click="validate">校验</ElButton>
        <ElButton @click="getTableData">获取数据</ElButton>
      </template>
    </DynamicTable>
  </div>
</template>
