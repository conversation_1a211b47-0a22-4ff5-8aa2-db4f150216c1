<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import MaterialInfo from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

/** 共享数据 */
const data = ref();

/** 物料信息查看模式*/
const [MaterialInfoModal, materialInfoModalApi] = useVbenModal({
  showConfirmButton: false,
  destroyOnClose: true,
  onClosed() {
    materialInfoModalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = materialInfoModalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <MaterialInfoModal>
    <MaterialInfo :is-view="true" :material-id="data.materialId" />
  </MaterialInfoModal>
</template>
