import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

/** 库存情况 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'warehouseName',
      label: '仓库',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'inventory',
      label: '库存量',
    },
    {
      component: 'div',
      fieldName: 'availableInventory',
      label: '可用量',
    },
    {
      component: 'div',
      fieldName: 'blockQuantity',
      label: '锁库量',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'lastInTime',
      label: '最后入库时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'lastOutTime',
      label: '最后出库时间',
    },
    {
      component: 'div',
      fieldName: 'safetyInventory',
      label: '安全库存',
      formItemClass: 'col-span-full xl:col-span-2',
    },
    {
      component: 'div',
      fieldName: 'obsoletePeriod',
      label: '呆滞期',
      formItemClass: 'col-span-full xl:col-span-1',
    },
  ];
}
