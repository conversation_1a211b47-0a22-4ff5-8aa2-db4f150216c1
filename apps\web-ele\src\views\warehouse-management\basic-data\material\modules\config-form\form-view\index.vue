<script setup lang="ts">
import type { materialConfig } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElTable, ElTableColumn } from 'element-plus';

import { getMaterialInvcConfig } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const formRef = ref();
/** 安全预警设置回填数据 */
const safetyInventoryWarnData = ref<materialConfig.safetyInventoryWarnType[]>(
  [],
);
/** 呆滞期设置回填数据 */
const slowMovingAnalysisData = ref<materialConfig.slowMovingAnalysisType[]>([]);
/** 库存配置表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});
/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getMaterialInvcConfig(props.materialId);
    // 填充表单数据
    formApi.setValues({
      warehouseName: data?.warehouseName,
      locationName: data?.locationName,
    });
    // 填充安全预警设置和呆滞期设置表单数据
    safetyInventoryWarnData.value = data?.safetyInventoryWarnList;
    slowMovingAnalysisData.value = data?.slowMovingAnalysisList;
  } catch {
    // ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  getData();
});
defineExpose({
  Form,
  formApi,
  props,
});
</script>

<template>
  <FormCard
    title="库存配置"
    :is-footer="false"
    v-loading="loading"
    body-class="!p-[10px]"
  >
    <Form ref="formRef">
      <template #safetyInventoryWarnList>
        <ElTable max-height="400" :data="safetyInventoryWarnData" border>
          <ElTableColumn align="center" prop="warehouseName" label="仓库" />
          <ElTableColumn
            align="center"
            prop="safetyInventory"
            label="安全库存"
          />
          <ElTableColumn
            align="center"
            prop="isSafetyInventoryWarn"
            label="参与预警"
          >
            <template #default="{ row }">
              {{ row.isSafetyInventoryWarn ? '是' : '否' }}
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
      <template #slowMovingAnalysisList>
        <ElTable max-height="400" :data="slowMovingAnalysisData" border>
          <ElTableColumn align="center" prop="warehouseName" label="仓库" />
          <ElTableColumn
            align="center"
            prop="obsoletePeriod"
            label="呆滞期/天"
          />
          <ElTableColumn
            align="center"
            prop="isSlowMovingAnalysis"
            label="参与分析"
          >
            <template #default="{ row }">
              {{ row.isSlowMovingAnalysis ? '是' : '否' }}
            </template>
          </ElTableColumn>
        </ElTable>
      </template>
    </Form>
  </FormCard>
</template>
