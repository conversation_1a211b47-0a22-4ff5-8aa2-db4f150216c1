import { requestClient } from '#/api/request';

import { systemPath } from '../path';

export namespace DocStatus {
  /** 单据状态子项 */
  export interface DocStatusItem {
    /** 单据状态Id */
    docStatusId: string;
    /** 单据标识 */
    docCode: string;
    /** 单据状态key */
    docStatusKey: string;
    /** 单据状态名称 */
    docStatusLabel: string;
    /** 状态排序 */
    docStatusOrder: string;
    /** 是否开始状态 */
    isStart: boolean;
  }
}

/** 根据单据编号获取流转状态列表*/
export async function getDocStatusList(docNumber: string) {
  return requestClient.get(
    `${systemPath}/doc/status/getDocStatusList/${docNumber}`,
  );
}

/** 根据单据标识获取单据状态列表*/
export async function getDocStatusInfo(docCode: string) {
  return requestClient.get<DocStatus.DocStatusItem[]>(
    `${systemPath}/doc/status/getDocStatusInfo/${docCode}`,
  );
}
