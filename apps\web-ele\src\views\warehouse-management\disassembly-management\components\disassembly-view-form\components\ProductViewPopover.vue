<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage } from 'element-plus';

import {
  getInvcByWarehouseIdAndMaterialId,
  getInventoryChangeList,
  getOutBoundDocDetailByOrigDoc,
} from '#/api';
import TipsPopover from '#/components/tips-popover/index.vue';

const props = defineProps({
  /** 拆卸数据 */
  assemblyDocDetail: {
    type: Object,
    default: () => ({}),
  },
});

const loading = ref(false);
/** 显示数据 */
const tipContent = ref<{
  isOutbound: boolean;
  newInventory?: number;
  oldInventory?: number;
  quantity?: number;
}>();

/** 查询当前仓库库存 */
const queryCurrentInventory = async () => {
  const res = await getInvcByWarehouseIdAndMaterialId(
    props.assemblyDocDetail.warehouseId ?? '',
    props.assemblyDocDetail.productMaterialId ?? '',
    false,
    false,
    false,
  );
  tipContent.value = {
    isOutbound: false,
    oldInventory: res.inventory,
    quantity: props.assemblyDocDetail.quantity,
  };
};

/** 查询历史变动库存 */
const queryHistoricalInventory = async () => {
  if (!tipContent.value) {
    const res = await getInventoryChangeList({
      materialIdList: [props.assemblyDocDetail.productMaterialId],
      warehouseIdList: [props.assemblyDocDetail.warehouseId],
      origDocIdList: [props.assemblyDocDetail.assemblyDocId],
    });
    tipContent.value = {
      isOutbound: true,
      oldInventory: res[0]?.oldInventory,
      newInventory: res[0]?.newInventory,
      quantity: props.assemblyDocDetail.quantity,
    };
  }
};

/** 获取出库单数据 */
const getOutBoundDoc = async () => {
  if (!tipContent.value) {
    const res = await getOutBoundDocDetailByOrigDoc(
      props.assemblyDocDetail.assemblyDocId,
      props.assemblyDocDetail.assemblyDocNumber,
    );

    if (!res) {
      tipContent.value = {
        isOutbound: false,
      };
      return;
    }

    if (res.docStatus === 'collected') {
      // 已完成，查询历史变动库存
      queryHistoricalInventory();
    } else {
      tipContent.value = {
        isOutbound: false,
      };
    }
  }
};

/** 拆卸单据母件库存出库分析 */
const showParentStock = async () => {
  try {
    loading.value = true;
    const status = props.assemblyDocDetail.docStatus;
    if (['awaitOut', 'checking'].includes(status)) {
      await queryCurrentInventory();
    } else if (['awaitWarehousing', 'finish'].includes(status)) {
      await queryHistoricalInventory();
    } else if (['cancelAudit', 'closed'].includes(status)) {
      // 根据出库单据状态显示是否已出库
      await getOutBoundDoc();
    } else {
      tipContent.value = {
        isOutbound: false,
      };
    }
  } catch {
    ElMessage.error('加载库存信息失败'); // 错误提示
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <TipsPopover placement="right" @show="showParentStock" width="200px">
    <div v-loading="loading">
      <div v-if="tipContent">
        <div v-if="tipContent.isOutbound">
          <span>成品已出库</span>
          <div v-if="tipContent.newInventory">
            出库后库存量：{{ tipContent.newInventory }}
          </div>
          <div v-if="tipContent.oldInventory">
            <span> 出库前库存量：{{ tipContent.oldInventory }} </span>
            <span v-if="tipContent.quantity">
              (
              <span class="text-green-500"> -{{ tipContent.quantity }} </span>
              )
            </span>
          </div>
        </div>
        <div v-else>
          <span>成品未出库</span>
          <div v-if="tipContent.oldInventory">
            当前库存量：{{ tipContent.oldInventory }}
          </div>
          <div v-if="tipContent.oldInventory">
            <span>
              出库后库存量：{{
                tipContent.oldInventory - (tipContent.quantity ?? 0)
              }}
            </span>
            <span v-if="tipContent.quantity">
              (
              <span class="text-green-500"> -{{ tipContent.quantity }} </span>
              )
            </span>
          </div>
        </div>
      </div>
    </div>
  </TipsPopover>
</template>
