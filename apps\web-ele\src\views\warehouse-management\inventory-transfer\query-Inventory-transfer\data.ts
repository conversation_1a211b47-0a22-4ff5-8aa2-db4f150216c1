import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api';

import { h, markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

import { getDocStatusInfo, getEnableWarehouseList, getEnumByName } from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';
import FilterDatePicker from '#/components/filter-date-picker/Index.vue';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'transferDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const transferTypeList = data.map((item: any) => ({
            label: item.enumLabel,
            value: item.enumValue,
          }));
          return transferTypeList;
        },
        api: () => {
          return getEnumByName('WmTransferTypeEnums');
        },
        maxCollapseTags: 1,
        filterable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docCode',
      label: '调拨类型',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.EnableWarehouse[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'oldWarehouseIdList',
      formItemClass: 'col-span-1',
      label: '调出仓库',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'targetWarehouseIdList',
      formItemClass: 'col-span-1',
      label: '调入仓库',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Select',
      componentProps: {
        multiple: true,
        maxCollapseTags: 2,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择调拨类型',
      },
      dependencies: {
        async componentProps(values) {
          values.docStatusList = [];
          if (values.docCode) {
            const res = await getDocStatusInfo(values.docCode);
            const optionsList = res?.map((item) => ({
              label: item.docStatusLabel,
              value: item.docStatusKey,
            }));
            const options = optionsList.filter(
              (item) => item.value !== 'pending',
            );
            return {
              options,
              placeholder: '请选择',
            };
          } else {
            return {
              disabled: true,
              placeholder: '请选择调拨类型',
            };
          }
        },
        triggerFields: ['docCode'],
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
    {
      component: markRaw(FilterDatePicker),
      fieldName: 'submitTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },
    {
      component: markRaw(FilterDatePicker),
      fieldName: 'outFinishTime',
      label: '出库时间',
      formItemClass: 'col-span-2',
    },
    {
      component: markRaw(FilterDatePicker),
      fieldName: 'inFinishTime',
      label: '入库时间',
      formItemClass: 'col-span-2',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      title: '单据编号',
      field: 'transferDocNumber',
      minWidth: 235,
    },
    {
      title: '调拨类型',
      field: 'docCodeLabel',
      minWidth: 110,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      title: '申请人',
      field: 'submitUserName',
      minWidth: 150,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 150,
    },
    {
      title: '调出仓库',
      field: 'oldWarehouseName',
      minWidth: 150,
    },
    {
      title: '出库执行人',
      field: 'outExecutorUserName',
      minWidth: 150,
    },
    {
      title: '出库时间',
      field: 'outFinishTime',
      minWidth: 150,
    },
    {
      title: '调入仓库',
      field: 'targetWarehouseName',
      minWidth: 150,
    },
    {
      title: '入库执行人',
      field: 'inExecutorUserName',
      minWidth: 150,
    },
    {
      title: '入库时间',
      field: 'inFinishTime',
      minWidth: 150,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 185,
      title: '操作',
    },
  ];
}
