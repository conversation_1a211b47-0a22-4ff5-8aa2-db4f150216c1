import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElInputTag, ElTag } from 'element-plus';

import {
  getDictItemList,
  getEnumByName,
  getMaterialCategoryTree,
  getOriginalDocConfigList,
} from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN_AND_OUT', 'OUT']);

// 查询参数类型
export interface SearchParams {
  applyTime: string[];
  executorTime: string[];
  outBoundDocNumberList: string[];
  origDocNumberList: string[];
  materialCodeList: string[];
  materialName: string;
  applyUserList: string[];
  materialUserList: string[];
  collectorUserList: string[];
  origDocTypeCodeList: string[];
  isStandard: boolean;
  materialAttributeList: string[];
  materialTypeList: string[];
  materialCategoryList: string[];
  isProxyExec: boolean;
  warehouseName: string;
  locationName: string;
  batchNumber: string;
}

// 表格数据类型
export interface RowType {
  outBoundDocId: string;
  outBoundDocNumber: string;
  isRectify: boolean;
  pictureFileId: string;
  materialCode: string;
  materialName: string;
  materialSpecs: string;
  applyQuantitySum: number;
  actualQuantitySum: number;
  baseUnitLabel: string;
  materialAttributeLabel: string;
  materialTypeLabel: string;
  materialCategoryName: string;
  origDocTypeName: string;
  origDocNumber: string;
  applyUserName: string;
  applyUser: string;
  origDocId: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  materialUserName: string;
  materialUser: string;
  applyTime: string;
  isProxyExec: boolean;
  collectorUserName: string;
  collectorUser: string;
  executorUserName: string;
  executorUser: string;
  executorUserDeptName: string;
  executorUserDeptId: string;
  executorTime: string;
  docStatusLabel: string;
  docStatus: string;
  preparationStateLabel: string;
  preparationState: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'outBoundDocNumberList',
      label: '出库单号',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
    },

    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入物料名称',
      },
      fieldName: 'materialName',
      label: '物料名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入仓库名称',
      },
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入库位名称',
      },
      fieldName: 'locationName',
      label: '库位名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入批次号',
      },
      fieldName: 'batchNumber',
      label: '批次号',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'materialUserList',
      modelPropName: 'value',
      label: '使用人',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'collectorUserList',
      modelPropName: 'value',
      label: '领料人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '出库类型',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '标准', value: true },
          { label: '非标准', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isStandard',
      label: '是否标准物料',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialAttributeList',
      label: '物料属性',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialTypeList',
      label: '物料大类',
    },

    {
      component: 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(data: any) {
            return {
              label: data.categoryName,
              value: data.categoryCode,
              children: data.children
                ? data.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((data: any) => convertDeptData(data));
          return convertedData;
        },
        api: () => {
          return getMaterialCategoryTree();
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 2,
        multiple: true,
        showCheckbox: true,
      },
      defaultValue: [],
      fieldName: 'materialCategoryList',
      formItemClass: '',
      label: '物料细类',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isProxyExec',
      label: '是否代领',
    },
    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'executorTime',
      formItemClass: 'col-span-2',
      label: '出库时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },

    {
      field: 'materialCode',
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'inline-flex px-2',
            },
            [
              h(
                'div',
                { class: 'w-[45px] mr-1' },
                row.isStandard
                  ? ''
                  : h(ElTag, { type: 'danger' }, { default: () => '非标' }),
              ),
              h('span', { class: 'flex-1' }, row.materialCode),
            ],
          );
        },
      },
      title: '物料编号',
      minWidth: 220,
      align: 'left',
      headerAlign: 'center',
    },

    {
      field: 'isStandard',
      title: '是否标准物料',
      visible: false,
    },
    {
      field: 'executorTime',
      title: '出库时间',
      width: 150,
    },
    {
      field: 'docStatusLabel',
      title: '出库单状态',
      visible: false,
    },
    {
      field: 'docStatus',
      title: '出库单状态值',
      visible: false,
    },

    {
      field: 'materialName',
      title: '物料名称',
      width: 150,
    },

    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[45px] max-w-[110px] text-[30px]',
          }),
      },
      field: 'pictureFileId',
      title: '物料图片',
      width: 150,
    },

    {
      field: 'materialSpecs',
      title: '具体规格',
      minWidth: 230,
    },

    {
      field: 'materialAttributeLabel',
      title: '物料属性',
      width: 100,
      visible: false,
    },

    {
      field: 'materialTypeLabel',
      title: '物料大类',
      width: 100,
      visible: false,
    },
    {
      field: 'materialCategoryName',
      title: '物料细类',
      width: 100,
      visible: false,
    },

    {
      field: 'applyQuantitySum',
      title: '应发数量',
      width: 100,
    },

    {
      field: 'actualQuantitySum',
      title: '实发数量',
      width: 100,
    },

    {
      field: 'actualItemList',
      title: '出库明细',
      align: 'left',
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {},
            row?.actualItemList?.length > 0
              ? row.actualItemList.map((item: any) => {
                  return h(
                    'div',
                    {
                      class:
                        'flex items-center text-black font-bold mb-1 rounded-md overflow-hidden',
                    },
                    [
                      h(
                        'span',
                        { class: 'bg-[#E3ECFD] px-2' },
                        item.warehouseName,
                      ),
                      h(
                        'span',
                        { class: 'bg-[#DBFFCA] px-2' },
                        item.locationName,
                      ),
                      h(
                        'span',
                        { class: 'bg-[#FFF9C8] px-2' },
                        item.batchNumber,
                      ),
                      h(
                        'span',
                        { class: 'bg-[#FCDABD] px-2' },
                        `×${item.actualQuantity}`,
                      ),
                    ],
                  );
                })
              : '',
          );
        },
      },
      width: 'auto',
      headerAlign: 'center',
    },

    {
      field: 'baseUnitLabel',
      title: '单位',
      width: 100,
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'inline-flex px-2',
            },
            [
              h(
                'div',
                { class: 'w-[45px] mr-1' },
                row.isRectify
                  ? h(ElTag, { type: 'primary' }, { default: () => '补录' })
                  : '',
              ),
              h('span', { class: 'flex-1' }, row.outBoundDocNumber),
            ],
          );
        },
      },
      field: 'outBoundDocNumber',
      title: '出库单号',
      minWidth: 250,
      align: 'left',
      headerAlign: 'center',
    },

    {
      field: 'outBoundDocId',
      title: '出库单ID',
      visible: false,
    },

    {
      field: 'isRectify',
      title: '是否补录',
      visible: false,
    },

    {
      field: 'origDocTypeName',
      width: 180,
      title: '出库类型',
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'origDocId',
      title: '申请单ID',
      visible: false,
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyUser',
      title: '申请人ID',
      visible: false,
    },
    {
      field: 'applyUserDeptId',
      title: '申请人部门ID',
      visible: false,
    },

    {
      field: 'applyUserDeptName',
      title: '申请人部门',
      visible: false,
    },

    {
      field: 'materialUserName',
      title: '使用人',
      width: 'auto',
    },

    {
      field: 'materialUser',
      title: '使用人ID',
      visible: false,
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 150,
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'inline-flex px-2',
            },
            [
              h(
                'div',
                { class: 'w-[45px] mr-1' },
                row.isProxyExec
                  ? h(ElTag, { type: 'primary' }, { default: () => '代领' })
                  : '',
              ),
              h('span', { class: 'flex-1' }, row.collectorUserName),
            ],
          );
        },
      },
      field: 'collectorUserName',
      title: '领料人',
      width: 'auto',
      align: 'left',
      headerAlign: 'center',
    },

    {
      field: 'collectorUser',
      title: '领料人ID',
      visible: false,
    },

    {
      field: 'isProxyExec',
      title: '是否代领',
      visible: false,
    },

    {
      field: 'executorUserName',
      title: '出库人',
      width: 'auto',
    },

    {
      field: 'executorUser',
      title: '出库人ID',
      visible: false,
    },
    {
      field: 'executorUserDeptName',
      title: '出库人部门',
      visible: false,
    },
    {
      field: 'executorUserDeptId',
      title: '出库人部门ID',
      visible: false,
    },

    {
      field: 'preparationStateLabel',
      title: '备料状态',
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: row.preparationState === '00' ? 'info' : 'success',
            },
            { default: () => row.preparationStateLabel },
          );
        },
      },
      width: 150,
    },

    {
      field: 'preparationState',
      title: '备料状态',
      visible: false,
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'outBoundDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ];
}
