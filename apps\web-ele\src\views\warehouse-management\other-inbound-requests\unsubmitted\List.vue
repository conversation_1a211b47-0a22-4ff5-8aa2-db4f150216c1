<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElDatePicker, ElMessage, ElMessageBox } from 'element-plus';

import {
  delInOutReqDocInBound,
  exportInOutMyDraftDocPageInBound,
  getOtherMyDraftDocPageInBound,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import FormEdit from '../modules/FormEdit.vue';
import FormView from '../modules/FormView.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
const exportLoading = ref(false);
/** 最后修改时间 */
const modifyTime = ref({
  modifyStartTime: props.params?.modifyStartTime,
  modifyEndTime: props.params?.modifyEndTime,
});

/** 模态框组件*/
const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: FormView,
  destroyOnClose: true,
});
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: FormEdit,
  destroyOnClose: true,
});
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    modifyTime.value = {
      modifyStartTime: '',
      modifyEndTime: '',
    };
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    handleReset,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getOtherMyDraftDocPageInBound({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            ...modifyTime.value,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 查看 */
const onView = (row: any) => {
  viewModalApi
    .setState({
      title: '其他入库申请单详情',
    })
    .setData({
      inOutReqDocId: row.inOutReqDocId,
      inOutReqDocNumber: row.inOutReqDocNumber,
      processInstanceId: row.processInstanceId,
      docStatus: row.docStatus,
      showDelBtn: true,
      refreshList: () => {
        gridApi.query();
      },
    })
    .open();
};
/** 新增 */
const onAdd = () => {
  editModalApi
    .setData({
      refreshList: () => {
        gridApi.query();
      },
    })
    .open();
};
/** 提交单据 */
const onSubmit = async (row: any) => {
  editModalApi
    .setState({
      title: '提交其他入库申请单',
    })
    .setData({
      inOutReqDocId: row.inOutReqDocId,
      inOutReqDocNumber: row.inOutReqDocNumber,
      isShowSave: true,
      refreshList: () => {
        gridApi.query();
      },
    })
    .open();
};
/** 删除单据 */
const onDelete = async (inOutReqDocId: string) => {
  try {
    await ElMessageBox.confirm('确认删除？', '提示', {
      type: 'warning',
    });
    await delInOutReqDocInBound(inOutReqDocId);
    ElMessage.success('删除成功');
    gridApi.query();
  } catch (error) {
    console.error(error);
    // ElMessage.error('删除失败');
  }
};
/** 导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInOutMyDraftDocPageInBound({
      ...formValues,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

onMounted(async () => {
  await gridApi.formApi.setValues({
    inOutReqDocNumberList:
      props.params?.inOutReqDocNumberList?.split(',') || [],
    docCodeList: props.params?.docCodeList?.split(',') || [],
  });
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <ViewModal class="h-full w-10/12" />
    <EditModal class="h-full w-10/12" />
    <Grid>
      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onAdd"
          v-access:code="'wm:inboundreq:submit'"
        >
          新增
        </ElButton>
      </template>
      <template #form-modifyTime>
        <ElDatePicker
          v-model="modifyTime.modifyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, modifyTime.modifyEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="modifyTime.modifyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                modifyTime.modifyStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #CellOperation="{ row }">
        <ElButton link size="small" @click="onView(row)" type="info">
          查看
        </ElButton>
        <ElButton
          v-if="row.docStatus === 'pending'"
          link
          size="small"
          @click="onSubmit(row)"
          type="primary"
          v-access:code="'wm:inboundreq:submit'"
        >
          提交单据
        </ElButton>
        <ElButton
          v-if="row.docStatus === 'reject'"
          link
          size="small"
          @click="onSubmit(row)"
          type="primary"
          v-access:code="'wm:inboundreq:submit'"
        >
          再次提交
        </ElButton>
        <ElButton
          link
          size="small"
          @click="onDelete(row.inOutReqDocId)"
          type="danger"
        >
          删除
        </ElButton>
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:inboundreq:export'"
          >
            <template #icon>
              <IconFont name="xiazai" />
            </template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
