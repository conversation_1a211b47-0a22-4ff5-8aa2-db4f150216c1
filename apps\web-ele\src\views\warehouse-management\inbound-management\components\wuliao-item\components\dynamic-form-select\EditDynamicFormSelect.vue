<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../../types/index.ts';

import { nextTick, ref, watch } from 'vue';

import { DynamicForm } from '@girant-web/dynamic-table-component';

import { getLocationList } from '#/api/warehouse-management/index';
import { add } from '#/utils/numberUtils';

import { generateEditDynamicFormOptions } from './EditDynamicFormSelect';

const props = defineProps({
  warehouseItemData: {
    type: Object as PropType<MaterialItem.WarehouseItemDataType>,
    default: () => ({}),
  },
  currentWarehouseId: {
    type: String,
    default: '',
  },
  materialId: {
    type: String,
    default: '',
  },
  defaultLocationId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['entryQuantityChange', 'warehouseChange']);

const dynamicFormRef = ref<InstanceType<typeof DynamicForm>>();

const currentWarehouseId = ref<string>(props.currentWarehouseId);

// 库位选项列表
const locationListOptions = ref<
  {
    label: string;
    value: string;
  }[]
>([]);

// 初始化时的第一条库位数据
const initLocationList = ref<MaterialItem.LocationItemType[]>([]);

// 数据是否处理完成
const isLoaded = ref(false);

// 当前仓库填入的数量
const currentWarehouseFillQuantity = ref(0);

// 获取表单数据
const getDynamicFormData = async (hasWarehouseId: boolean = false) => {
  if (!dynamicFormRef.value) {
    console.warn('dynamicFormRef is not ready');
    return [];
  }

  try {
    const formValuesPromises = await dynamicFormRef.value.getAllFormValues();

    const formData = await Promise.all(formValuesPromises);

    if (hasWarehouseId) {
      return formData.map((item: any) => {
        const data = {
          ...item,
          actualQuantity: item.quantity,
          warehouseId: props.currentWarehouseId,
          materialId: props.materialId,
        };
        delete data.quantity;
        return data;
      });
    }
    return formData;
  } catch (error) {
    console.error('Error getting form data:', error);
    return [];
  }
};

// 获取当前仓库填入的数量
const getCurrentWarehouseFillQuantity = async () => {
  const formData = await getDynamicFormData();
  let fillQuantity = 0;
  for (const item of formData) {
    fillQuantity = add(fillQuantity, item.quantity);
  }
  currentWarehouseFillQuantity.value = fillQuantity;
  emits('entryQuantityChange');
};

// 监听当前选择的仓库id
watch(
  () => props.currentWarehouseId,
  async (newVal) => {
    currentWarehouseId.value = newVal;

    if (!newVal) {
      locationListOptions.value = [];
      isLoaded.value = true;
      return;
    }

    // 获取库位列表
    const locationListRes = await getLocationList({
      warehouseId: newVal,
      isLock: false,
      isEnable: true,
    });

    // 处理库位列表
    locationListOptions.value = locationListRes.map((item) => ({
      label: item.locationName,
      value: item.locationId,
    }));

    dynamicFormRef.value?.removeAllForms();
    dynamicFormRef.value?.initForm([]);

    if (!isLoaded.value) {
      const initLocationItem = props.warehouseItemData.locationList?.[0] || {
        locationId: '',
        unitPrice: 0,
        batchNumber: '',
        quantity: 0,
      };

      if (initLocationItem) {
        initLocationItem.locationId =
          locationListOptions.value.find(
            (item) => item.value === props.defaultLocationId,
          )?.value ||
          locationListOptions.value[0]?.value ||
          '';
      }

      initLocationList.value = [initLocationItem];
    }

    isLoaded.value = true;

    nextTick(() => {
      getCurrentWarehouseFillQuantity();
      emits('warehouseChange');
    });
  },
  { immediate: true, deep: true },
);

// 数量改变
const onQuantityChange = async () => {
  // 防抖
  getCurrentWarehouseFillQuantity();
};

// 动态表单配置
const formOptions = generateEditDynamicFormOptions({
  locationListOptions,
  currentWarehouseId,
  materialId: props.materialId,
  onQuantityChange,
});

// 删除一个表单
const removeForm = () => {
  nextTick(() => {
    getCurrentWarehouseFillQuantity();
  });
};

// 校验表单数据
const validateFormData = async () => {
  const validateFormData = await dynamicFormRef.value?.validateAllForms(false);
  return validateFormData;
};

defineExpose({
  getDynamicFormData,
  validateFormData,
  currentWarehouseFillQuantity,
});
</script>

<template>
  <DynamicForm
    v-if="isLoaded"
    ref="dynamicFormRef"
    class="flex-1 pl-4"
    :form-data="initLocationList"
    :form-options="formOptions"
    @remove-form="removeForm"
  />
</template>
