<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

const props = defineProps<{
  docStatus: string;
  outBoundDocId: string;
  outBoundDocNumber: string;
}>();

const emits = defineEmits(['boundSuccess', 'boundLoading']);

const formModalRef = ref();

const onInboundSuccess = () => {
  emits('boundSuccess');
};

const onInboundLoading = (loading: boolean) => {
  emits('boundLoading', loading);
};

const currentComponent = computed(() => {
  switch (props.docStatus) {
    // 已出库
    case 'collected': {
      return markRaw(
        defineAsyncComponent(
          () => import('../outbound-already/modules/Form.vue'),
        ),
      );
    }

    // 待出库
    case 'pending': {
      return markRaw(
        defineAsyncComponent(
          () => import('../outbound-pending/modules/Form.vue'),
        ),
      );
    }

    // 已关闭
    default: {
      return markRaw(
        defineAsyncComponent(
          () => import('../outbound-documents/modules/Form.vue'),
        ),
      );
    }
  }
});
</script>

<template>
  <component
    :is="currentComponent"
    ref="formModalRef"
    :out-bound-doc-id="props.outBoundDocId"
    :out-bound-doc-number="props.outBoundDocNumber"
    @bound-success="onInboundSuccess"
    @bound-loading="onInboundLoading"
  />
</template>
