import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

// 统计入库申请明细子项数量（项数）
export async function getInApplyItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getApplyItemNum`,
    params,
  );
}

// 统计入库申请明细物料项
export async function getInApplyMaterialItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getApplyMaterialItemNum`,
    params,
  );
}

// 统计入库申请明细物料总数
export async function getInApplyMaterialNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getApplyMaterialNum`,
    params,
  );
}
