import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ElInputTag, ElTag } from 'element-plus';

import {
  getDocStatusInfo,
  getEnumByName,
  getOriginalDocConfigList,
} from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN', 'IN_AND_OUT']);

const { hasAccessByCodes } = useAccess();

// 查询参数类型
export interface SearchParams {
  inBoundDocNumberList: string;
  applyUserList: string;
  executorUserList: string;
  origDocTypeCodeList: string[];
  origDocNumberList: string;
  applyTime: string[];
  closeTime: string[];
  executorTime: string[];
  docStatusList: string[];
}

// 表格数据类型
export interface RowType {
  applyTime: string;
  applyUser: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  applyUserName: string;
  inBoundDocId: string;
  inBoundDocNumber: string;
  isRectify: boolean;
  origDocId: string;
  origDocNumber: string;
  origDocTypeCode: string;
  origDocTypeName: string;
  docStatus: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'inBoundDocNumberList',
      label: '入库单号',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const workStatusTypeList = data.map((item: any) => ({
            label: item.docStatusLabel,
            value: item.docStatusKey,
          }));
          workStatusTypeList.push({ label: '全部', value: '' });
          return workStatusTypeList;
        },
        api: () => {
          return getDocStatusInfo('WM0010');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'docStatusList',
      label: '入库单状态',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '入库类型',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      label: '申请人',
      modelPropName: 'value',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'executorUserList',
      label: '入库人',
      modelPropName: 'value',
      formItemClass: 'col-span-1',
    },

    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'executorTime',
      formItemClass: 'col-span-2',
      label: '入库时间',
    },

    {
      component: 'Input',
      fieldName: 'closeTime',
      formItemClass: 'col-span-2',
      label: '关闭时间',
    },
  ];
}

/** 判断tag类型 */
const getTagType = (type: string) => {
  switch (type) {
    // 取消审核中
    case 'cancelAudit': {
      return 'warning';
    }
    // 已关闭
    case 'closed': {
      return 'info';
    }

    // 待入库
    case 'pending': {
      return 'primary';
    }
    // 已入库
    case 'stocked': {
      return 'success';
    }
    default: {
      return 'info';
    }
  }
};

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    slots: { default: 'action' },
    title: '操作',
    fixed: 'right',
    width: 'auto',
  } as NonNullable<VxeTableGridOptions['columns']>[0];

  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'inline-flex px-2',
            },
            [
              h(
                'div',
                { class: 'w-[45px] mr-1' },
                row.isRectify
                  ? h(ElTag, { type: 'primary' }, { default: () => '补录' })
                  : '',
              ),
              h('span', { class: 'flex-1' }, row.inBoundDocNumber),
            ],
          );
        },
      },
      field: 'inBoundDocNumber',
      title: '入库单号',
      minWidth: 250,
      align: 'left',
      headerAlign: 'center',
    },

    {
      field: 'origDocTypeName',
      minWidth: 180,
      title: '入库类型',
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: getTagType(row.docStatus),
            },
            {
              default: () => row.docStatusLabel,
            },
          );
        },
      },
      field: 'docStatusLabel',
      title: '入库单状态',
      width: 150,
    },

    {
      field: 'docStatus',
      title: '入库单状态值',
      visible: false,
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 150,
    },

    {
      field: 'executorUserName',
      title: '入库人',
      width: 'auto',
    },

    {
      field: 'executorTime',
      title: '执行入库时间',
      width: 150,
    },

    ...(hasAccessByCodes(['wm:inbound:exec', 'wm:inoutbound:cancel:submit'])
      ? [operationColumn]
      : []),
  ];
}
