import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace OutboundDetailsApi {
  export interface OutboundDetailsPageParams {
    /** 出库单据号列表，多个用英文逗号分隔 */
    outBoundDocNumberList: string[];
    /** 申请人id列表，精确查询 */
    applyUserList: string[];
    /** 源单据类型标识列表，精确查询，根据查询来源单据配置列表接口的出参docCode字段。 */
    origDocTypeCodeList: string[];
    /** 源单据编号列表，精确查询 */
    origDocNumberList: string[];
    /** 是否补录，不填则展示全部，true-补录，false-非补录 */
    isRectify: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: string[];
    /** 使用人id列表，精确查询 */
    materialUserList: string[];
    /** 使用人部门id列表，精确查询 */
    materialUserDeptList: string[];
    /** 备料状态值列表，字典WmPreparationStateEnums */
    preparationStateList: string[];
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 物料编号列表，精确查询 */
    materialCodeList?: string[];
    /** 物料名称列表，精确查询 */
    materialName?: string;
    /** 物料属性列表，精确查询 */
    materialAttributeList?: string[];
    /** 物料大类列表，精确查询 */
    materialTypeList?: string[];
    /** 物料细类列表，精确查询 */
    materialCategoryList?: string[];
    /** 是否标准物料，精确查询 */
    isStandard: boolean;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  export interface OutboundActualDetailsPageParams {
    outBoundDocNumberList?: string[];
    origDocTypeCodeList?: string[];
    origDocNumberList?: string[];
    applyUserList?: string[];
    collectorUserList?: string[];
    materialUserList?: string[];
    executorUserList?: string[];
    materialIdList?: string[];
    materialCodeList?: string[];
    materialName?: string;
    materialAttributeList?: string[];
    materialTypeList?: string[];
    materialCategoryList?: string[];
    isStandard?: boolean;
    warehouseIdList?: string[];
    warehouseName?: string;
    locationIdList?: string[];
    locationName?: string;
    batchNumber?: string;
    isProxyExec?: boolean;
    isRectify?: boolean;
    applyStartTime?: string;
    applyEndTime?: string;
    executorStartTime?: string;
    executorEndTime?: string;
    preparationStateList?: string[];
    docStatusList?: string[];
  }

  export interface CommonItem {
    /* 仓库id */
    warehouseId: string;

    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库名称 */
    warehouseName: string;

    /* 库位id */
    locationId: string;

    /* 库位编号 */
    locationCode: string;

    /* 库位名称 */
    locationName: string;

    /* 批次号 */
    batchNumber: string;

    /* 均价（单价），默认不可见 */
    unitPrice: number;
  }

  /** 申请明细子项 */
  export interface ApplyItem extends CommonItem {
    /* 申请数量 */
    applyQuantity: number;
  }

  /* 实际明细子项 */
  export interface ActualItem extends CommonItem {
    /* 出入库实际数量 */
    actualQuantity: number;
  }

  export interface OutboundDetailsRecord {
    /* 出库单据ID */
    outBoundDocId: string;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: string;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: string;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 领料人ID */
    collectorUser: string;

    /* 领料人姓名 */
    collectorUserName: string;

    /* 领料人部门ID */
    collectorUserDeptId: string;

    /* 领料人部门名称 */
    collectorUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: string;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: string;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 使用人ID */
    materialUser: string;

    /* 使用人姓名 */
    materialUserName: string;

    /* 使用人部门ID */
    materialUserDeptId: string;

    /* 使用人部门名称 */
    materialUserDeptName: string;

    /* 领料人确认方式值，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethod: string;

    /* 领料人确认方式标签，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethodLabel: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 是否代领 */
    isProxyExec: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 执行出库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: Record<string, unknown>;

    /* 是否自动完成出库 */
    isAutoIo: boolean;

    /* 备料状态值，枚举WmPreparationStateEnums */
    preparationState: string;

    /* 备料状态标签，枚举WmPreparationStateEnums */
    preparationStateLabel: string;

    /* 单据状态值，字典wmOutDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmOutDocStatus */
    docStatusLabel: string;

    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 规格型号 */
    materialSpecs: string;

    /* 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard: boolean;

    /* 申请总数 */
    applyQuantitySum: number;

    /* 实际总数 */
    actualQuantitySum: number;
    /* 申请明细列表 */
    applyItemList: ApplyItem[];
    /* 实际明细列表，为空时代表此物料没有做出入库 */
    actualItemList: ActualItem[];
  }
}

/**
 * 查询出库单据明细分页列表
 */
export async function getOutBoundItemPage(
  params: OutboundDetailsApi.OutboundDetailsPageParams,
) {
  return requestClient.post<OutboundDetailsApi.OutboundDetailsRecord>(
    `${warehousePath}/wm/outBound/item/getOutBoundItemPage`,
    { ...params },
  );
}

/** 导出出库单据明细列表*/
export async function exportOutBoundItem(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/actual/exportOutBoundItem`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/**
 * 查询已出库单据明细分页列表
 */
export async function getOutBoundActualItemPage(
  params: OutboundDetailsApi.OutboundActualDetailsPageParams,
) {
  return requestClient.post<OutboundDetailsApi.OutboundDetailsRecord>(
    `${warehousePath}/wm/outBound/actual/getOutBoundItemPage`,
    { ...params },
  );
}

/** 导出已出库单据明细列表*/
export async function exportOutBoundDetailPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/actual/exportOutBoundDetailPage`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/**
 * 查询出库单据申请明细分页列表
 */
export async function getOutBoundApplyItemPage(
  params: OutboundDetailsApi.OutboundActualDetailsPageParams,
) {
  return requestClient.post<OutboundDetailsApi.OutboundDetailsRecord>(
    `${warehousePath}/wm/outBound/apply/getOutBoundItemPage`,
    { ...params },
  );
}

/** 导出出库单据申请明细列表*/
export async function exportOutBoundApplyDetailPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/apply/exportOutBoundDetailPage`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
