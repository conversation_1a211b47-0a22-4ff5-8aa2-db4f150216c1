import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { dictItemListType } from '#/api/common';
import type {
  InventoryQueryApi,
  WarehouseInfoApi,
} from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElBadge, ElInputTag } from 'element-plus';

import { getDictItemList } from '#/api/common';
import { getWarehouseList } from '#/api/warehouse-management';
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '仓库',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'materialName',
      label: '物料名称',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      fieldName: 'isStandard',
      label: '是否标准物料',
      labelWidth: 100,
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'inventoryQuantity',
      label: '库存量',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'availableInventory',
      label: '可用量',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'lastInTime',
      formItemClass: 'col-span-2 w-full',
      label: '最后入库时间',
      labelWidth: 100,
    },
    {
      component: 'Input',
      fieldName: 'lastOutTime',
      formItemClass: 'col-span-2 w-full',
      label: '最后出库时间',
      labelWidth: 100,
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialTypeList',
      label: '物料大类',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'materialCategoryList',
      label: '物料细类',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      formItemClass: 'col-span-1',
      fieldName: 'materialAttributeList',
      label: '物料属性',
    },
  ];
}

/** 表格 */
export function useColumns<T = InventoryQueryApi.InventoryPage>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '图片',
      field: 'pictureFileId',
      minWidth: 90,
    },
    {
      title: '物料编号',
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '非标',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: row.isStandard,
              class: 'item',
            },
            {
              default: () => row.materialCode,
            },
          );
        },
      },
      field: 'materialCode',
      minWidth: 150,
    },
    {
      title: '物料名称',
      field: 'materialName',
      minWidth: 100,
    },
    {
      title: '具体规格',
      field: 'materialSpecs',
      minWidth: 200,
    },
    {
      title: '单位',
      field: 'baseUnitLabel',
      minWidth: 40,
    },
    {
      title: '仓库名称',
      field: 'warehouseName',
      minWidth: 100,
    },
    {
      title: '库存量',
      field: 'inventory',
      minWidth: 65,
    },
    {
      title: '可用量',
      field: 'availableInventory',
      minWidth: 65,
    },
    {
      title: '安全库存',
      field: 'safetyInventory',
      minWidth: 65,
    },
    {
      title: '最后入库时间',
      field: 'lastInTime',
      minWidth: 115,
    },
    {
      title: '最后出库时间',
      field: 'lastOutTime',
      minWidth: 115,
    },
    {
      title: '物料属性',
      field: 'materialAttributeLabel',
      minWidth: 65,
    },
    {
      title: '物料大类',
      field: 'materialTypeLabel',
      minWidth: 65,
    },
    {
      title: '物料细类',
      field: 'materialCategoryName',
      minWidth: 100,
    },
    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'materialName',
          nameTitle: '即时库存',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: ['view'],
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 65,
      title: '操作',
    },
  ];
}
