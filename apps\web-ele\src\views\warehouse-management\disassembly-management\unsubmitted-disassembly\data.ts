import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api';

import { markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

import { getEnableWarehouseList } from '#/api';
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'assemblyDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.EnableWarehouse[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '所属仓库',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'productMaterialCodeList',
      label: '成品编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'productMaterialName',
      label: '成品名',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'modifyTime',
      label: '修改时间',
      formItemClass: 'col-span-2',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: 'assemblyDocNumber',
      },
      title: '单据编号',
      field: 'assemblyDocNumber',
      minWidth: 235,
    },
    {
      slots: {
        default: 'productMaterialName',
      },
      title: '成品',
      field: 'productMaterialName',
      minWidth: 235,
    },
    {
      title: '成品规格',
      field: 'materialSpecs',
      minWidth: 260,
    },
    {
      slots: {
        default: 'quantity',
      },
      title: '数量',
      field: 'quantity',
      minWidth: 75,
    },
    {
      title: '修改时间',
      field: 'modifyTime',
      minWidth: 115,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 150,
      title: '操作',
    },
  ];
}
