<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getWarehouseList } from '#/api/warehouse-management';

import { useColumns } from './data';

const props = defineProps({
  /** 仓库编号 */
  warehouseCode: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    columns: useColumns(),
    minHeight: 50,
    maxHeight: 500,
    showOverflow: false,
    keepSource: true,
    pagerConfig: {
      enabled: false,
    },
    data: [],
  } as VxeTableGridOptions<WarehouseInfoApi.WarehouseList[]>,
});
const data = ref<WarehouseInfoApi.WarehouseList[]>();
/** 获取仓库下库位列表*/
const getLocationList = async () => {
  try {
    loading.value = true;
    data.value = await getWarehouseList({
      warehouseCode: props.warehouseCode,
      isLoc: true,
    });
    gridApi.setGridOptions({
      data: data.value[0]?.locationList || [],
    });
  } catch {
    ElMessage.error('获取库位列表失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.warehouseCode) {
    getLocationList();
  } else {
    ElMessage.error('没有仓库编号');
  }
});
defineExpose({
  gridApi,
  Grid,
});
</script>
<template>
  <Grid />
</template>
