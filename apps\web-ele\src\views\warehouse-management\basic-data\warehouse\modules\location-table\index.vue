<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { useVbenVxeGrid } from '@girant/adapter';

import { getLocationPage } from '#/api/warehouse-management';

import { useColumns } from './data';

const props = defineProps({
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
});
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    border: true,
    columns: useColumns(),
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          if (!props.warehouseId) {
            return {
              records: [],
              total: 0,
            };
          }
          return await getLocationPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            warehouseId: props.warehouseId,
          });
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      rowConfig: {
        keyField: 'locationId',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
  } as VxeTableGridOptions<WarehouseInfoApi.WarehouseInfo>,
});

defineExpose({
  gridApi,
  Grid,
});
</script>
<template>
  <Grid />
</template>
