<script setup lang="ts">
import { ref } from 'vue';

import { Page } from '@vben/common-ui';

import { ElCard } from 'element-plus';

import Form from './modules/Form.vue';
import tree from './modules/tree.vue';

/** 当前表单 */
const thisForm = ref('warehouse');
/** 当前id */
const currentId = ref('');
/** 当前编号 */
const currentCode = ref('');
/** 是否是查看 */
const isView = ref(false);
/** 树形组件 */
const treeRef = ref<InstanceType<typeof tree>>();
/** 刷新表单 */
const key = ref(0);
/** 当前选中的仓库id */
const warehouseId = ref('');

/** 新增仓库 */
const addWarehouse = async () => {
  isView.value = false;
  currentId.value = '';
  currentCode.value = '';
  thisForm.value = 'warehouse';
  key.value++;
};

/** 新增库位 */
const addLocation = async (id: string) => {
  isView.value = false;
  currentId.value = '';
  currentCode.value = '';
  thisForm.value = 'location';
  key.value++;
  warehouseId.value = id;
};

/** 节点点击 */
const nodeClick = (data: any) => {
  // 切换表单 有仓库id说明是仓库 否则是库位
  if (data.warehouseId) {
    thisForm.value = 'warehouse';
    currentId.value = data.warehouseId;
    currentCode.value = data.warehouseCode;
    isView.value = true;
  } else {
    thisForm.value = 'location';
    currentId.value = data.locationId;
    currentCode.value = data.locationCode;
    isView.value = true;
  }
  // 刷新表单
  key.value++;
};
</script>

<template>
  <Page auto-content-height>
    <ElCard
      class="h-full min-w-[1250px]"
      body-class="flex h-full gap-x-[10px] !p-[10px]"
    >
      <tree
        ref="treeRef"
        class="border-primary relative h-full min-w-[380px] rounded-[10px] border !p-[10px]"
        @add-warehouse="addWarehouse"
        @add-location="addLocation"
        @node-click="nodeClick"
      />
      <Form
        :key="key"
        :current-id="currentId"
        :current-code="currentCode"
        :form="thisForm"
        class="w-full"
        :is-view="isView"
        :warehouse-id="warehouseId"
        @form-submit-success="treeRef?.refreshData"
      />
    </ElCard>
  </Page>
</template>
