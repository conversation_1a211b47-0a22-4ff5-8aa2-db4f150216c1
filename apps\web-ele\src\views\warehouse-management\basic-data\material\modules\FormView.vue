<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import ConfigFormEdit from './config-form/form-edit/index.vue';
import ConfigFormView from './config-form/form-view/index.vue';
import MaterialForm from './material-form/index.vue';

/** 共享数据 */
const sharedData = ref();
const [Modal, modalApi] = useVbenModal({
  footer: true,
  showCancelButton: true,
  showConfirmButton: false,
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
const loading = ref(false);
const materialRef = ref<InstanceType<typeof MaterialForm>>();
const configRef = ref<InstanceType<typeof ConfigFormEdit>>();

defineExpose({
  materialRef,
  configRef,
});
</script>

<template>
  <Modal>
    <div v-loading="loading">
      <MaterialForm ref="materialRef" :material-id="sharedData.materialId" />
      <ConfigFormView ref="configRef" :material-id="sharedData.materialId" />
    </div>
  </Modal>
</template>
