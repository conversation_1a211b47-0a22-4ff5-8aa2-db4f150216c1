import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { ElBadge, ElInputTag, ElTag } from 'element-plus';

import { getEnumByName, getOriginalDocConfigList } from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN_AND_OUT', 'OUT']);

// 查询参数类型
export interface SearchParams {
  outBoundDocNumberList: string[];
  applyUserList: string[];
  origDocTypeCodeList: string[];
  origDocNumberList: string[];
  isRectify: boolean;
  applyTime: string[];
  materialUserList: string[];
  materialUserDeptList: string[];
  preparationStateList: string[];
  isProxyExec: boolean;
  collectorUserList: string[];
  executorTime: string[];
}

// 表格数据类型
export interface RowType {
  outBoundDocId: string;
  outBoundDocNumber: string;
  isRectify: boolean;
  origDocTypeName: string;
  origDocNumber: string;
  applyUserName: string;
  applyUser: string;
  origDocId: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  materialUserName: string;
  materialUser: string;
  applyTime: string;
  isProxyExec: boolean;
  collectorUserName: string;
  collectorUser: string;
  collectorConfirmMethodLabel: string;
  collectorConfirmMethod: string;
  executorUserName: string;
  executorUser: string;
  executorUserDeptName: string;
  executorUserDeptId: string;
  executorConfirmMethodLabel: string;
  executorConfirmMethod: string;
  executorTime: string;
  preparationStateLabel: string;
  preparationState: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'outBoundDocNumberList',
      label: '出库单号',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '出库类型',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'materialUserList',
      modelPropName: 'value',
      label: '使用人',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'collectorUserList',
      modelPropName: 'value',
      label: '领料人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isProxyExec',
      label: '是否代领',
    },

    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'executorTime',
      formItemClass: 'col-span-2',
      label: '出库时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '补录',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: !row.isRectify,
              class: 'item',
            },
            {
              default: () => row.outBoundDocNumber,
            },
          );
        },
      },
      field: 'outBoundDocNumber',
      title: '出库单号',
      minWidth: 250,
    },
    {
      field: 'outBoundDocId',
      title: '出库单ID',
      visible: false,
    },

    {
      field: 'isRectify',
      title: '是否补录',
      visible: false,
    },

    {
      field: 'origDocTypeName',
      width: 180,
      title: '出库类型',
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'origDocId',
      title: '申请单ID',
      visible: false,
    },

    {
      field: 'executorTime',
      title: '出库时间',
      width: 150,
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyUser',
      title: '申请人ID',
      visible: false,
    },
    {
      field: 'applyUserDeptId',
      title: '申请人部门ID',
      visible: false,
    },

    {
      field: 'applyUserDeptName',
      title: '申请人部门',
      visible: false,
    },

    {
      field: 'materialUserName',
      title: '使用人',
      width: 'auto',
    },

    {
      field: 'materialUser',
      title: '使用人ID',
      visible: false,
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 150,
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '代领',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: !row.isProxyExec,
              class: 'item',
            },
            {
              default: () => row.collectorUserName,
            },
          );
        },
      },
      field: 'collectorUserName',
      title: '领料人',
      width: 'auto',
    },

    {
      field: 'collectorUser',
      title: '领料人ID',
      visible: false,
    },

    {
      field: 'isProxyExec',
      title: '是否代领',
      visible: false,
    },

    {
      field: 'preparationStateLabel',
      title: '备料状态',
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: row.preparationState === '00' ? 'info' : 'success',
            },
            { default: () => row.preparationStateLabel },
          );
        },
      },
      width: 150,
    },

    {
      field: 'preparationState',
      title: '备料状态',
      visible: false,
    },

    {
      field: 'collectorConfirmMethodLabel',
      title: '领料人确认方式',
      width: 150,
    },
    {
      field: 'collectorConfirmMethod',
      title: '领料人确认方式值',
      visible: false,
    },

    {
      field: 'executorUserName',
      title: '出库人',
      width: 'auto',
    },

    {
      field: 'executorUser',
      title: '出库人ID',
      visible: false,
    },
    {
      field: 'executorUserDeptName',
      title: '出库人部门',
      visible: false,
    },
    {
      field: 'executorUserDeptId',
      title: '出库人部门ID',
      visible: false,
    },

    {
      field: 'executorConfirmMethodLabel',
      title: '出库人确认方式',
      width: 150,
    },
    {
      field: 'executorConfirmMethod',
      title: '仓管员确认方式值',
      visible: false,
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'outBoundDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ];
}
