import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

export namespace DisassemblyApi {
  /** 分页基本 */
  export interface PageBase {
    [key: string]: any;
    records: Array<any>;
    total: string;
    pageSize: string;
    pageNum: string;
    pages: string;
  }

  export interface AssemblyDoc {
    /** 组装拆卸单据ID */
    assemblyDocId: string;
    /** 组装拆卸单据编号 */
    assemblyDocNumber: string;
    /** 组装拆卸类型值，枚举WmAssemblyTypeEnums */
    docCode: string;
    /** 组装拆卸类型标签，枚举WmAssemblyTypeEnums */
    docCodeLabel: string;
    /** 是否自动完成出入库 */
    inAutoIo: boolean;
    /** 所属仓库ID */
    warehouseId: string;
    /** 所属仓库编号 */
    warehouseCode: string;
    /** 所属仓库名称 */
    warehouseName: string;
    /** 成品库位ID */
    productLocationId: string;
    /** 成品库位编号 */
    productLocationCode: string;
    /** 成品库位名称 */
    productLocationName: string;
    /** 成品物料ID */
    productMaterialId: string;
    /** 成品物料编号 */
    productMaterialCode: string;
    /** 成品物料名称 */
    productMaterialName: string;
    /** 成品图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 成品属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 成品属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 成品大类值，字典baseMaterialType */
    materialType: string;
    /** 成品大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 是否标准物料 */
    isStandard: boolean;
    /** 成品批次号 */
    productBatchNumber: string;
    /** 成品数量 */
    quantity: number;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 关闭人ID */
    closeUser: string;
    /** 关闭人姓名 */
    closeUserName: string;
    /** 执行人ID */
    executorUser: string;
    /** 执行人姓名 */
    executorUserName: string;
    /** 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;
    /** 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: string;
    /** 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;
    /** 审核状态，写入当前流程所在节点 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例ID */
    processInstanceId: string;
    /** 单据状态值，字典publicDocStatus */
    docStatus: string;
    /** 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }

  export interface MyDraftDoc {
    /** 组装拆卸单据ID */
    assemblyDocId: string;
    /** 组装拆卸单据编号 */
    assemblyDocNumber: string;
    /** 组装拆卸类型值，枚举WmAssemblyTypeEnums */
    docCode: string;
    /** 组装拆卸类型标签，枚举WmAssemblyTypeEnums */
    docCodeLabel: string;
    /** 所属仓库ID */
    warehouseId: string;
    /** 所属仓库编号 */
    warehouseCode: string;
    /** 所属仓库名称 */
    warehouseName: string;
    /** 成品库位ID */
    productLocationId: string;
    /** 成品库位编号 */
    productLocationCode: string;
    /** 成品库位名称 */
    productLocationName: string;
    /** 成品批次号 */
    productBatchNumber: string;
    /** 成品数量 */
    quantity: number;
    /** 成品物料ID */
    productMaterialId: string;
    /** 成品物料编号 */
    productMaterialCode: string;
    /** 成品物料名称 */
    productMaterialName: string;
    /** 成品图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 成品属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 成品属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 成品大类值，字典baseMaterialType */
    materialType: string;
    /** 成品大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 是否标准物料 */
    isStandard: boolean;
    /** 最后修改时间，时间格式：yyyy-MM-dd HH:mm */
    modifyTime: string;
    /** 单据状态值，字典publicDocStatus */
    docStatus: string;
    /** 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }

  export interface AssemblyDocPage extends PageBase {
    records: AssemblyDoc[];
  }

  export interface MyDraftDocPage extends PageBase {
    records: MyDraftDoc[];
  }

  /** AssemblyMaterialListRespVO */
  export interface AssemblyMaterial {
    /** 组装拆卸原料ID */
    assemblyMaterialId: string;
    /** 仓库ID */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 库位ID */
    locationId: string;
    /** 库位编号 */
    locationName: string;
    /** 库位名称 */
    locationCode: string;
    /** 批次号 */
    batchNumber: string;
    /** 原料ID */
    materialId: string;
    /** 原料编号 */
    materialCode: string;
    /** 原料名称 */
    materialName: string;
    /** 原料图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 原料属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 原料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 原料大类值，字典baseMaterialType */
    materialType: string;
    /** 原料大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 原料细类 */
    materialCategory: string;
    /** 原料细类名称 */
    materialCategoryName: string;
    /** 是否标准物料 */
    isStandard: boolean;
    /** 原料数量 */
    quantity: number;
  }

  export interface AssemblyDocDetail {
    /** 组装拆卸单据ID */
    assemblyDocId: string;
    /** 组装拆卸单据编号 */
    assemblyDocNumber: string;
    /** 组装拆卸类型值，枚举WmAssemblyTypeEnums */
    docCode: string;
    /** 组装拆卸类型标签，枚举WmAssemblyTypeEnums */
    docCodeLabel: string;
    /** 是否自动完成出入库 */
    isAutoIo: boolean;
    /** 是否提交 */
    isSubmit: boolean;
    /** 所属仓库ID */
    warehouseId: string;
    /** 所属仓库编号 */
    warehouseCode: string;
    /** 所属仓库名称 */
    warehouseName: string;
    /** 成品库位ID */
    productLocationId: string;
    /** 成品库位编号 */
    productLocationCode: string;
    /** 成品库位名称 */
    productLocationName: string;
    /** 成品物料ID */
    productMaterialId: string;
    /** 成品物料编号 */
    productMaterialCode: string;
    /** 成品物料名称 */
    productMaterialName: string;
    /** 成品图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 成品属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 成品属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 成品大类值，字典baseMaterialType */
    materialType: string;
    /** 成品大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 是否标准物料 */
    isStandard: boolean;
    /** 成品批次号 */
    productBatchNumber: string;
    /** 成品数量 */
    quantity: number;
    /** 创建人ID */
    createUser: string;
    /** 创建人名称 */
    createUserName: string;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 关闭人ID */
    closeUser: string;
    /** 关闭人姓名 */
    closeUserName: string;
    /** 执行人ID */
    executorUser: string;
    /** 执行人姓名 */
    executorUserName: string;
    /** 创建时间，时间格式：yyyy-MM-dd HH:mm */
    createTime: string;
    /** 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;
    /** 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: string;
    /** 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;
    /** 审核状态，写入当前流程所在节点 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例ID */
    processInstanceId: string;
    /** 单据状态值，字典publicDocStatus */
    docStatus: string;
    /** 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
    /** 备注(原因) */
    remark: string;
    /** 附件流水号 */
    serialNumber: string;
    /** 组装拆卸原料列表 */
    assemblyMaterialList: AssemblyMaterial[];
  }

  export interface AssemblyDocNum {
    /** 组装拆卸单据数量 */
    assemblyDocNum: number;
  }

  export interface AssemblyMaterialNum {
    /** 组装拆卸单据原料数量 */
    assemblyMaterialNum: number;
  }
}

/** 查询组装拆卸单据分页列表 */
export async function getAssemblyDocPage(params: Recordable<any>) {
  return requestClient.post<DisassemblyApi.AssemblyDocPage>(
    `${warehousePath}/wm/stock/assembly/getAssemblyDocPage`,
    params,
  );
}

/** 查询待我提交的组装拆卸单据分页列表 */
export async function getAssemblyMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post<DisassemblyApi.MyDraftDocPage>(
    `${warehousePath}/wm/stock/assembly/getMyDraftDocPage`,
    params,
  );
}

/** 获取组装拆卸单据详细信息 */
export async function getAssemblyDocDetail(
  assemblyDocId: string,
  assemblyDocNumber?: string,
  isQueryItem?: boolean,
) {
  return requestClient.get<DisassemblyApi.AssemblyDocDetail>(
    `${warehousePath}/wm/stock/assembly/getAssemblyDocDetail`,
    {
      params: { assemblyDocId, assemblyDocNumber, isQueryItem },
    },
  );
}

/** 导出组装拆卸单据列表 */
export async function exportAssemblyDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/stock/assembly/exportAssemblyDoc`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 导出待我提交的组装拆卸单据列表 */
export async function exportAssemblyMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/stock/assembly/exportMyDraftDocPage`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 提交组装拆卸单据 */
export async function submitAssemblyDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/stock/assembly/submitAssemblyDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 暂存组装拆卸单据 */
export async function saveOrModAssemblyDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/stock/assembly/saveOrModAssemblyDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 删除组装拆卸单据 */
export async function delAssemblyDoc(assemblyDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/stock/assembly/delAssemblyDoc/${assemblyDocId}`,
  );
}

/** 批量删除待我提交的组装拆卸单据 */
export async function batchDelAssemblyDoc(params: Recordable<any>) {
  return requestClient.get(
    `${warehousePath}/wm/stock/assembly/batchDelAssemblyDoc`,
    {
      params: {
        params,
      },
    },
  );
}

/** 执行组装拆卸单据 */
export async function execAssemblyDoc(assemblyDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/stock/assembly/execAssemblyDoc/${assemblyDocId}`,
  );
}

/** 关闭组装拆卸单据 */
export async function closeAssemblyDoc(assemblyDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/stock/assembly/closeAssemblyDoc/${assemblyDocId}`,
  );
}

/** 统计组装拆卸单据数量 */
export async function getAssemblyDocNum(params: Recordable<any>) {
  return requestClient.post<DisassemblyApi.AssemblyDocNum>(
    `${warehousePath}/wm/stock/assembly/getAssemblyDocNum`,
    params,
  );
}

/** 统计组装拆卸单据原料项数 */
export async function getAssemblyMaterialNum(params: Recordable<any>) {
  return requestClient.post<DisassemblyApi.AssemblyMaterialNum>(
    `${warehousePath}/wm/stock/assembly/material/getAssemblyMaterialNum`,
    params,
  );
}
