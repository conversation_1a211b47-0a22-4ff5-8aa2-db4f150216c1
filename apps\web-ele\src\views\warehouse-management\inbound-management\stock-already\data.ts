import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { ElBadge, ElInputTag } from 'element-plus';

import { getEnumByName, getOriginalDocConfigList } from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN', 'IN_AND_OUT']);

// 查询参数类型
export interface SearchParams {
  inBoundDocNumberList: string;
  applyUserList: string;
  executorUserList: string;
  origDocTypeCodeList: string[];
  origDocNumberList: string;
  applyTime: string[];
  executorTime: string[];
}

// 表格数据类型
export interface RowType {
  applyTime: string;
  applyUser: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  applyUserName: string;
  inBoundDocId: string;
  inBoundDocNumber: string;
  isRectify: boolean;
  origDocId: string;
  origDocNumber: string;
  origDocTypeCode: string;
  origDocTypeName: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'inBoundDocNumberList',
      label: '入库单号',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '入库类型',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'executorUserList',
      modelPropName: 'value',
      label: '入库人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'Input',
      fieldName: 'executorTime',
      formItemClass: 'col-span-2',
      label: '入库时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '补录',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: !row.isRectify,
              class: 'item',
            },
            {
              default: () => row.inBoundDocNumber,
            },
          );
        },
      },
      field: 'inBoundDocNumber',
      title: '入库单号',
      minWidth: 250,
    },
    {
      field: 'origDocTypeName',
      minWidth: 180,
      title: '入库类型',
    },

    {
      field: 'executorTime',
      title: '入库时间',
      width: 150,
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 150,
    },

    {
      field: 'executorUserName',
      title: '入库人',
      width: 'auto',
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'inBoundDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ];
}
