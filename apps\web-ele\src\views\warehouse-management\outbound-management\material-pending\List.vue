<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';

import { getPrepMyDraftDocPage } from '#/api/warehouse-management';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      modifyEndTime?: string;
      modifyStartTime?: string;
      outBoundDocNumberList?: string;
    }>,
    default: () => ({}),
  },
});
const modalFormRef = ref<InstanceType<typeof Form>>();
const prepDocId = ref<string>('');
const outBoundDocId = ref<string>('');
const outBoundDocNumber = ref<string>('');

/** 最近修改时间 */
const modifyTime = ref({
  // 开始时间
  modifyStartTime: props.params?.modifyStartTime || '',
  // 结束时间
  modifyEndTime: props.params?.modifyEndTime || '',
});

/** 弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '确认备料',
  destroyOnClose: true,
  onBeforeClose: () => {
    prepDocId.value = '';
    outBoundDocId.value = '';
    outBoundDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'view': {
      openPrepDocModal(e.row);
      break;
    }
  }
}

function openPrepDocModal(row: RowType) {
  prepDocId.value = row.prepDocId;
  outBoundDocId.value = row.outBoundDocId;
  outBoundDocNumber.value = row.outBoundDocNumber;
  formModalApi
    .setState({
      title: `备料单详情`,
    })
    .open();
}

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      modifyTime.value = {
        modifyStartTime: '',
        modifyEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[80px]',
    },

    collapsed: isEmpty(props.attr?.collapsed) ? false : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? false
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows,
    schema: useGridFormSchema(),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-5',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.modifyStartTime = modifyTime.value.modifyStartTime;
          params.modifyEndTime = modifyTime.value.modifyEndTime;

          return await getPrepMyDraftDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'supplierId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
  });
});

function onPrepSuccess() {
  formModalApi.close();
  gridApi.query();
}

function onPrepLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

const createDisabledDate = (isEnd: boolean) => {
  return (time: Date) => {
    if (!modifyTime.value.modifyEndTime && !isEnd) {
      return false;
    }
    // 是结束时间
    return isEnd
      ? time.getTime() < new Date(modifyTime.value.modifyStartTime).getTime()
      : time.getTime() > new Date(modifyTime.value.modifyEndTime).getTime();
  };
};
</script>

<template>
  <Page auto-content-height>
    <FormModal class="h-full w-10/12">
      <Form
        ref="modalFormRef"
        :prep-doc-id="prepDocId"
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outBoundDocNumber"
        @bound-loading="onPrepLoading"
        @bound-success="onPrepSuccess"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>
    <Grid>
      <template #form-modifyTime>
        <ElDatePicker
          v-model="modifyTime.modifyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="createDisabledDate(false)"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="modifyTime.modifyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="createDisabledDate(true)"
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
    </Grid>
  </Page>
</template>
