import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElInputTag } from 'element-plus';

import { getEnableWarehouseList } from '#/api/warehouse-management';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';
/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '仓库',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'materialName',
      label: '物料名称',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'blockUserList',
      label: '锁定人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'blockTime',
      formItemClass: 'col-span-2 w-full',
      label: '锁定时间',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px]',
          }),
      },
      title: '图片',
      field: 'pictureFileId',
      minWidth: 90,
    },
    {
      title: '物料编号',
      field: 'materialCode',
      minWidth: 120,
    },
    {
      title: '物料名称',
      field: 'materialName',
      minWidth: 100,
    },
    {
      title: '物料规格',
      field: 'materialSpecs',
      minWidth: 210,
    },
    {
      title: '基本单位',
      field: 'baseUnitLabel',
      minWidth: 70,
    },
    {
      title: '所属仓库',
      field: 'warehouseName',
      minWidth: 100,
    },
    {
      title: '关联单据标识',
      field: 'docTypeCode',
      minWidth: 130,
    },
    {
      title: '关联单据编号',
      field: 'docNumber',
      minWidth: 120,
    },
    {
      title: '锁库数量',
      field: 'blockQuantity',
      minWidth: 100,
    },
    {
      title: '锁定人',
      field: 'blockUserName',
      minWidth: 100,
    },
    {
      title: '锁定时间',
      field: 'blockTime',
      minWidth: 130,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 120,
      title: '操作',
    },
  ];
}
