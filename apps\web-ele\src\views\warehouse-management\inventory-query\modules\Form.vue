<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import MaterialForm from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

import DetailsForm from './details-form/index.vue';
import SituationForm from './situation-form/index.vue';

/** 共享数据 */
const sharedData = ref();
const [Modal, modalApi] = useVbenModal({
  footer: true,
  showCancelButton: true,
  showConfirmButton: false,
  closeOnClickModal: false,
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <div>
      <MaterialForm :material-id="sharedData.materialId" />
      <SituationForm
        :material-id="sharedData.materialId"
        :warehouse-id="sharedData.warehouseId"
      />
      <DetailsForm
        :material-id="sharedData.materialId"
        :warehouse-id="sharedData.warehouseId"
      />
    </div>
  </Modal>
</template>
