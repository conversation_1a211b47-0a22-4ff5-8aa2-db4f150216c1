import { requestClient } from '#/api/request';

import { authPath, baseDataPath, warehousePath } from '../path';

/** 来源单据配置 */
export namespace OriginalDocConfigApi {
  /** 入参 */
  export interface OriginalDocConfigParams {
    /** 单据类型编号列表 */
    docCodeList: string;
    /** 单据类型名称列表 */
    docName: string;
    /** 来源单据类型列表，枚举WmDocTypeEnums */
    /** 来源单据类型列表，枚举WmDocTypeEnums */
    originalDocTypeList: string;
  }
  /** 来源单据配置列表 */
  export type OriginalDocConfigListRes = Array<{
    /** 单据类型编号 */
    docCode: string;
    /** 单据类型名称 */
    docName: string;
    /** 来源单据类型，枚举WmDocTypeEnums */
    originalDocType: string;
    /** 来源单据类型标签，枚举WmDocTypeEnums */
    /** 来源单据类型标签，枚举WmDocTypeEnums */
    originalDocTypeLabel: string;
  }>;
}
/** 查询物料细类树 */
export interface MaterialCategoryTreeType {
  /**	类别id */
  categoryId: string;
  /** 类别名称 */
  categoryName: string;
  /** 类别编号 */
  categoryCode: string;
  /**	子类列表 */
  children?: MaterialCategoryTreeType[];
}
export namespace InventoryMaterialsByStrategyAPI {
  export interface InventoryMaterialsItem {
    /* 批次号 */
    batchNumber: string;

    /* 批次号库存量 */
    inventory: number;

    /* 最近入库时间，时间格式：yyyy-MM-dd HH:mm */
    lastInTime: Record<string, unknown>;

    /* 最近出库时间，时间格式：yyyy-MM-dd HH:mm */
    lastOutTime: Record<string, unknown>;

    /* 库位编号 */
    locationCode: string;

    /* 库位ID */
    locationId: string;

    /* 库位名称 */
    locationName: string;

    /* 建议用量，根据入参用量，按照库存策略分配给各明细用料 */
    suggestedQuantity: number;
  }

  export interface InventoryMaterialsByStrategy {
    /* 仓库ID */
    warehouseId: string;

    /* 物料ID */
    materialId: string;

    /* 仓库库存策略值 */
    inOutStrategy: string;

    /* 仓库库存策略标签 */
    inOutStrategyLabel: string;

    /* 库存明细用量列表 */
    materialItemList: InventoryMaterialsItem[];
  }
}
/** 物料信息列表 */
export interface MaterialInfoList {
  /** 物料ID */
  materialId: string;
  /** 物料编号 */
  materialCode: string;
  /** 物料名称 */
  materialName: string;
  /** 物料属性（字典 baseMaterialAttribute） */
  materialAttribute: string;
  /** 物料属性标签（字典 baseMaterialAttribute） */
  materialAttributeLabel: string;
  /** 物料大类（字典 baseMaterialType） */
  materialType: string;
  /** 物料大类标签（字典 baseMaterialType） */
  materialTypeLabel: string;
  /** 基本单位（字典 baseMaterialUnit） */
  baseUnit: string;
  /** 基本单位标签（字典 baseMaterialUnit） */
  baseUnitLabel: string;
  /**	物料规格 */
  materialSpecs: string;
  /** 物料数量 */
  quantity: number;
}
/** 解析物料清单excel获取物料信息 */
export interface ParseMaterialListParams {
  /** 重复物料编号列表 */
  repeatCodes: string[];
  /**	不存在的物料编号列表 */
  nonentityCodes: string[];
  /** 异常的物料编号列表 */
  unusualCodes: string[];
  /** 物料信息列表 */
  materialList: MaterialInfoList[];
}

/** 查询来源单据配置列表 */
export async function getOriginalDocConfigList(
  params?: Partial<OriginalDocConfigApi.OriginalDocConfigParams>,
) {
  return requestClient.post<OriginalDocConfigApi.OriginalDocConfigListRes>(
    `${warehousePath}/wm/original/config/getOriginalDocConfigList`,
    { ...params },
  );
}
/** 查询物料细类树 */
export async function getMaterialCategoryTree(params?: {
  categoryCode: string;
  categoryName: string;
}) {
  return requestClient.post<MaterialCategoryTreeType[]>(
    `${baseDataPath}/base/material/category/getMaterialCategoryTree`,
    params,
  );
}

/**
 * 二次校验
 */
export async function reconfirm(password: string) {
  return requestClient.post(`${authPath}/authorize/recheck/user`, {
    password,
  });
}

/** 根据仓库库存策略和用量查询可用的库存明细用量 */
export async function getInventoryMaterialsByStrategy(params: {
  inventory?: number;
  materialId: string;
  warehouseId: string;
}) {
  return requestClient.post<InventoryMaterialsByStrategyAPI.InventoryMaterialsByStrategy>(
    `${warehousePath}/wm/inventory/material/getInvcMaterialsByStrategy`,
    params,
  );
}

/** 解析物料清单excel获取物料信息 */
export async function parseMaterialList(data: { file: Blob | File }) {
  return requestClient.upload<ParseMaterialListParams>(
    `${baseDataPath}/base/material/analyze/getMaterialInfo`,
    data,
  );
}
/** 导出物料清单excel模板 */
export async function analyzeMaterialTemplate() {
  return requestClient.post(
    `${baseDataPath}/base/material/analyzeMaterialTemplate`,
    {},
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
