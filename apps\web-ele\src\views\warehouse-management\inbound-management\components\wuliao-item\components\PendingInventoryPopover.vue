<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../types';

import { computed } from 'vue';

import { isEmpty } from '@vben/utils';

import TipsPopover from '#/components/tips-popover/index.vue';
import { add } from '#/utils/numberUtils';

const props = defineProps({
  // 当前仓库数据(含库存量、可用量)
  warehouseItemData: {
    type: Object as PropType<MaterialItem.SelectWarehouseListType>,
    default: () => ({}),
  },
  // 当前填入的数量
  currentWarehouseFillQuantity: {
    type: Number,
    default: 0,
  },
});

const inventoryAddQuantity = computed(() => {
  return add(
    props.warehouseItemData.inventory || 0,
    props.currentWarehouseFillQuantity,
  );
});

const availableInventoryAddQuantity = computed(() => {
  return add(
    props.warehouseItemData.availableInventory || 0,
    props.currentWarehouseFillQuantity,
  );
});
</script>

<template>
  <div>
    <TipsPopover placement="top" width="250px">
      <div
        class="p-2 text-center text-sm text-gray-500"
        v-if="isEmpty(warehouseItemData)"
      >
        请先选择仓库
      </div>
      <div class="inventory-info w-full" v-else>
        <table>
          <thead>
            <tr>
              <th></th>
              <th>当前库存</th>
              <th>入库后</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>库存量</td>
              <td>{{ props.warehouseItemData.inventory }}</td>
              <td>
                {{ inventoryAddQuantity }}
              </td>
            </tr>
            <tr>
              <td>可用量</td>
              <td>{{ props.warehouseItemData.availableInventory }}</td>
              <td>
                {{ availableInventoryAddQuantity }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </TipsPopover>
  </div>
</template>
<style scoped>
.inventory-info {
  @apply rounded-lg bg-white p-2;
}

.inventory-info table {
  @apply w-full border-separate border-spacing-0;
}

.inventory-info th,
.inventory-info td {
  @apply p-2 text-center text-sm;
}

.inventory-info th {
  @apply bg-gray-50 font-medium text-gray-600;
}

.inventory-info td:first-child {
  @apply text-left font-medium text-gray-600;
}

.inventory-info tr:not(:last-child) td {
  @apply border-b border-gray-100;
}

:deep(.el-button.el-button--default) {
  @apply !text-base !font-medium;
}

:deep(.el-button--default.is-link) {
  @apply !text-primary-500 hover:!text-primary-600;
}

:deep(.el-button--danger.is-link) {
  @apply !text-red-500 hover:!text-red-600;
}
</style>
