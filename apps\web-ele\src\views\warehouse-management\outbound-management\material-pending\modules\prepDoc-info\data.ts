import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '暂无单据编号');
      },
      fieldName: 'prepDocNumber',
      label: '备料单号',
    },

    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'prepDocId',
      label: '备料单id',
      formItemClass: 'hidden',
    },

    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'modifyTime',
      label: '最后修改时间',
    },
  ];
}
