import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '-');
      },
      fieldName: 'origDocNumber',
      label: '转入单号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '-');
      },
      fieldName: 'origDocTypeName',
      label: '转入单类型',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '-');
      },
      fieldName: 'materialUserName',
      label: '领料人',
    },
  ];
}
