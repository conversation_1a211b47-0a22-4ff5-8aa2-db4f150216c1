import type { VbenFormProps, VbenFormSchema } from '@girant/adapter';

import type { materialConfig } from '#/api/warehouse-management';

import { ref } from 'vue';

import { getLocationList } from '#/api/warehouse-management';
/** 当前的物料配置数据 */
const currentMaterialConfigData = ref<materialConfig.materialPageRecords>();
/** 当前可用仓库列表 */
const availableWarehouseListData = ref<{ label: string; value: string }[]>();
/** 库存配置 */
export function useFormSchema(
  materialConfigData: materialConfig.materialPageRecords,
  availableWarehouseList: {
    label: string;
    value: string;
  }[],
): VbenFormSchema[] {
  currentMaterialConfigData.value = materialConfigData;
  availableWarehouseListData.value = availableWarehouseList;
  return [
    {
      component: 'Select',
      fieldName: 'warehouseId',
      label: '默认仓库',
      componentProps: {
        options: availableWarehouseList,
        clearable: true,
        filterable: true,
        placeholder: '请选择默认仓库',
      },
      dependencies: {
        componentProps(values: any) {
          // 如果当前值存在但不在选项列表中，则清空当前值
          if (
            values.warehouseId &&
            !availableWarehouseList.some(
              (item) => item.value === values.warehouseId,
            )
          ) {
            values.warehouseId = '';
            return {
              placeholder: '当前仓库不可用，请重新选择',
              options: availableWarehouseList,
            };
          }
          return {
            options: availableWarehouseList,
          };
        },
        triggerFields: ['warehouseId'],
      },
      labelWidth: 120,
    },
    {
      componentProps: {
        options: [],
        placeholder: '请先选择仓库',
        clearable: true,
        filterable: true,
      },
      dependencies: {
        async componentProps(values: any) {
          if (!values.warehouseId) {
            values.locationId = '';
          }
          if (values.warehouseId) {
            const res = await getLocationList({
              warehouseId: values.warehouseId,
              isEnable: true,
            });
            // 展示数据
            const data = res.map((item: any) => ({
              label: item.locationName,
              value: item.locationId,
            }));
            // 检查data中是否有locationId 的值，如果没有则清空locationId
            if (!data.some((item: any) => item.value === values.locationId)) {
              values.locationId = '';
            }
            return {
              options: data,
            };
          }
          return {};
        },
        triggerFields: ['warehouseId'],
      },
      component: 'Select',
      fieldName: 'locationId',
      label: '默认库位',
      formItemClass: 'col-span-1',
      labelWidth: 120,
    },
    {
      component: 'Input',
      fieldName: 'safetyInventoryWarnList',
      label: '安全库存预警设置',
      formItemClass: 'col-span-full items-start',
      labelWidth: 120,
    },
    {
      component: 'Input',
      fieldName: 'slowMovingAnalysisList',
      label: '呆滞期设置',
      formItemClass: 'col-span-full items-start',
      labelWidth: 120,
    },
  ];
}

/** 安全库存预警设置*/
export const safetyInventoryWarnOptions = ref<VbenFormProps>({
  wrapperClass: 'grid-cols-3',
  schema: [
    {
      component: 'Select',
      fieldName: 'warehouseId',
      labelClass: 'min-w-[50px]',
      label: '仓库',
      componentProps: {
        options: availableWarehouseListData,
        clearable: true,
        filterable: true,
        placeholder: '请选择仓库',
      },
      dependencies: {
        componentProps(values: any) {
          // 如果当前值存在但不在选项列表中，则清空当前值
          if (
            (values.warehouseId &&
              !availableWarehouseListData.value?.some(
                (item) => item.value === values.warehouseId,
              )) ||
            values.warehouseId === ''
          ) {
            values.warehouseId = '';
            return {
              placeholder: '当前仓库不可用，请重新选择',
              options: availableWarehouseListData,
            };
          }
          return {
            options: availableWarehouseListData,
          };
        },
        triggerFields: ['warehouseId'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 1,
        precision: 1,
        class: '!w-full',
      },
      dependencies: {
        componentProps(values: any) {
          if (!values.warehouseId) {
            values.safetyInventory = null;
          }
          return {};
        },
        disabled(values) {
          return !values.warehouseId;
        },
        triggerFields: ['warehouseId'],
      },
      fieldName: 'safetyInventory',
      label: '安全库存',
    },
    {
      component: 'Checkbox',
      labelClass: 'min-w-[50px]',
      renderComponentContent: () => {
        return {
          default: () => ['参与预警'],
        };
      },
      dependencies: {
        componentProps(values: any) {
          if (!values.warehouseId) {
            values.isSafetyInventoryWarn = false;
          }
          return {};
        },
        disabled(values) {
          return !values.warehouseId;
        },
        triggerFields: ['warehouseId'],
      },
      fieldName: 'isSafetyInventoryWarn',
      defaultValue: false,
      label: '',
    },
  ],
});

/** 呆滞期设置*/
export const slowMovingAnalysisOptions = ref<VbenFormProps>({
  wrapperClass: 'grid-cols-3',
  schema: [
    {
      component: 'Select',
      fieldName: 'warehouseId',
      labelClass: 'min-w-[50px]',
      label: '仓库',
      componentProps: {
        options: availableWarehouseListData,
        clearable: true,
        filterable: true,
        placeholder: '请选择仓库',
      },
      dependencies: {
        componentProps(values: any) {
          // 如果当前值存在但不在选项列表中，则清空当前值
          if (
            (values.warehouseId &&
              !availableWarehouseListData.value?.some(
                (item) => item.value === values.warehouseId,
              )) ||
            values.warehouseId === ''
          ) {
            values.warehouseId = '';
            return {
              placeholder: '当前仓库不可用，请重新选择',
              options: availableWarehouseListData,
            };
          }
          return {
            options: availableWarehouseListData,
          };
        },
        triggerFields: ['warehouseId'],
      },
    },
    {
      component: 'InputNumber',
      componentProps: {
        controlsPosition: 'right',
        min: 1,
        precision: 0,
        class: '!w-full',
      },
      dependencies: {
        componentProps(values: any) {
          if (!values.warehouseId) {
            values.obsoletePeriod = null;
          }
          return {};
        },
        disabled(values) {
          return !values.warehouseId;
        },
        triggerFields: ['warehouseId'],
      },
      fieldName: 'obsoletePeriod',
      label: '呆滞期/天',
    },
    {
      component: 'Checkbox',
      labelClass: 'min-w-[50px]',
      renderComponentContent: () => {
        return {
          default: () => ['参与分析'],
        };
      },
      dependencies: {
        componentProps(values: any) {
          if (!values.warehouseId) {
            values.isSlowMovingAnalysis = false;
          }
          return {};
        },
        disabled(values) {
          return !values.warehouseId;
        },
        triggerFields: ['warehouseId'],
      },
      defaultValue: false,
      fieldName: 'isSlowMovingAnalysis',
      label: '',
    },
  ],
});
