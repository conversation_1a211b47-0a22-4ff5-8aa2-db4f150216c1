import type { RouteRecordRaw } from 'vue-router';

const routes: RouteRecordRaw[] = [
  {
    meta: {
      icon: '',
      keepAlive: true,
      order: 1000,
      title: '组件',
    },
    name: 'Component',
    path: '/component',
    children: [
      {
        meta: {
          title: '表单',
        },
        name: 'FormDemos',
        path: '/component/form',
        component: () => import('#/views/component/form.vue'),
      },
      {
        meta: {
          title: '表格',
        },
        name: 'TableDemos',
        path: '/component/table',
        component: () => import('#/views/component/table.vue'),
      },
      {
        meta: {
          title: 'vxe表格',
        },
        name: 'TableVxe',
        path: '/component/vxeTable',
        component: () => import('#/views/component/role/list.vue'),
      },
    ],
  },
];

export default routes;
