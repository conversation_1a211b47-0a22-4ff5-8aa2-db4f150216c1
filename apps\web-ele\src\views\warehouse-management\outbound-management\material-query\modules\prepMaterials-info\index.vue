<script setup lang="ts">
import type { MaterialItemData } from '../../../components/materials-item/types';

import type {
  MaterialPendingApi,
  WarehouseListForMaterialListApi,
} from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { ElMessage } from 'element-plus';

import {
  getActiveWarehouseListByMaterialList,
  getPrepDocDetail,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';

import MaterialsItem from '../../../components/materials-item/index.vue';

const props = defineProps({
  prepDocId: {
    type: String,
    default: '',
  },
  prepDocNumber: {
    type: String,
    default: '',
  },
});

const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

const prepItemList = ref<MaterialItemData.DocItem[]>([]);

const allWarehouseListForMaterialList = ref<
  WarehouseListForMaterialListApi.WarehouseListForMaterialList[]
>([]);

/** 获取数据 */
const getPrepDocDetailHandle = async () => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId: props.prepDocId,
      prepDocNumber: props.prepDocNumber,
      isQueryItem: true,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
    return {} as MaterialPendingApi.PrepDocDetail;
  }
};

onMounted(async () => {
  if (props.prepDocId) {
    const data = await getPrepDocDetailHandle();
    prepDocDetail.value = data;

    if (data?.prepItemList) {
      const newItemList = data?.prepItemList.map(
        (item: MaterialPendingApi.PrepItemList) => {
          return {
            ...item,
            itemList: item.itemList.map(
              (subItem: MaterialPendingApi.ItemList) => {
                const {
                  batchNumber,
                  targetLocationCode,
                  targetLocationId,
                  targetLocationName,
                  warehouseCode,
                  warehouseId,
                  warehouseName,
                  transferQuantity,
                } = subItem;
                return {
                  batchNumber: batchNumber || '',
                  locationCode: targetLocationCode || '',
                  locationId: targetLocationId || '',
                  locationName: targetLocationName || '',
                  warehouseCode: warehouseCode || '',
                  warehouseId: warehouseId || '',
                  warehouseName: warehouseName || '',
                  quantity: transferQuantity || 0,
                };
              },
            ),
            quantitySum: item.transferQuantitySum,
          };
        },
      );

      prepItemList.value = newItemList;
    }

    if (prepItemList?.value?.length > 0) {
      const params = {
        materialIdList: prepItemList.value
          .map((item) => item.materialId)
          .join(','),
        docTypeCode: prepDocDetail.value.origDocTypeCode,
      };
      // 批量获取物料可入库的仓库列表
      const WForMListRes = await getActiveWarehouseListByMaterialList(params);

      allWarehouseListForMaterialList.value = WForMListRes;
    }
  }
});

const materialsItemRef = ref<InstanceType<typeof MaterialsItem>[]>([]);
// 获取所有表单数据
const getSubData = async () => {
  return prepItemList.value || ([] as MaterialItemData.DocItem[]);
};

defineExpose({
  getSubData,
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>备料明细</span>
    </template>

    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ prepItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template #default>
      <template
        v-for="(materialItem, index) in prepItemList"
        :key="materialItem.materialId"
      >
        <TriangleCard :number="index + 1" title="" class="mb-5">
          <template #content>
            <MaterialsItem
              ref="materialsItemRef"
              :material-item-data="materialItem"
              :orig-doc-type-code="prepDocDetail.origDocTypeCode"
              item-status="showView"
              :warehouse-list-for-material="
                allWarehouseListForMaterialList.find(
                  (item) => item.materialId === materialItem.materialId,
                )
              "
            />
          </template>
        </TriangleCard>
      </template>
    </template>
  </FormCard>
</template>
