<script setup lang="ts">
import type { TransferApi } from '#/api';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getWareTransferDocDetail } from '#/api';
import FormCard from '#/components/form-card/Index.vue';

import MaterialViewField from './components/MaterialField.vue';
import { useColumns } from './data';
import MaterialForm from './modules/MaterialForm.vue';

const props = defineProps({
  /** 库存调拨单据ID */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 库存调拨单据编号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 库存调拨单数据 */
const data = ref<TransferApi.TransferDocDetail>();

onMounted(() => {
  if (props.transferDocId || props.transferDocNumber) {
    getData();
  }
});

/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    const res = await getWareTransferDocDetail(
      props.transferDocId,
      props.transferDocNumber,
      true,
    );
    data.value = res;
    // 设置表单内容
    gridApi.setGridOptions({
      data: data.value?.transferItemList,
    });
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 原料明细表单 */
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: {
    columns: useColumns(),
    data: [],
    minHeight: 150,
    border: true,
    pagerConfig: {
      enabled: false,
    },
  },
});

/** 模态框组件*/
const [Modal, modalApi] = useVbenModal({
  connectedComponent: MaterialForm,
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
});

/** 显示物料详情信息 */
const showProductMaterial = (row: any) => {
  modalApi
    .setState({ title: '物料信息' })
    .setData({
      materialId: row.materialId,
    })
    .open();
};
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>调拨明细</span>
    </template>

    <Modal class="h-full w-10/12" />

    <Grid>
      <template #materialName="{ row }">
        <MaterialViewField
          :transfer-doc-detail="data"
          :material-id="row.materialId"
          :material-name="row.materialName"
          :material-code="row.materialCode"
          :picture-file-id="row.pictureFileId"
          :transfer-quantity="row.transferQuantity"
          @click="showProductMaterial(row)"
        />
      </template>
    </Grid>
  </FormCard>
</template>
