import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

export namespace TransferApi {
  /** 分页基本 */
  export interface PageBase {
    [key: string]: any;
    records: Array<any>;
    total: string;
    pageSize: string;
    pageNum: string;
    pages: string;
  }

  /** 原因选项子项 */
  export interface RemarkItem {
    /** 选项ID */
    optionId: number;
    /** 选项名称 */
    optionName: string;
  }

  export interface TransferDoc {
    /** 库存调拨单据ID */
    transferDocId: string;
    /** 库存调拨单据编号 */
    transferDocNumber: string;
    /** 调拨类型值，枚举WmTransferTypeEnums */
    docCode: string;
    /** 调拨类型标签，枚举WmTransferTypeEnums */
    docCodeLabel: string;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 关闭人ID */
    closeUser: string;
    /** 关闭人姓名 */
    closeUserName: string;
    /** 出库执行人ID */
    outExecutorUser: string;
    /** 出库执行人姓名 */
    outExecutorUserName: string;
    /** 入库执行人ID */
    inExecutorUser: string;
    /** 入库执行人姓名 */
    inExecutorUserName: string;
    /** 提交时间 */
    submitTime: string;
    /** 关闭时间 */
    closeTime: string;
    /** 出库完成时间 */
    outFinishTime: string;
    /** 入库完成时间 */
    inFinishTime: string;
    /** 原仓库ID */
    oldWarehouseId: string;
    /** 原仓库编号 */
    oldWarehouseCode: string;
    /** 原仓库名称 */
    oldWarehouseName: string;
    /** 目标仓库ID */
    targetWarehouseId: string;
    /** 目标仓库编号 */
    targetWarehouseCode: string;
    /** 目标仓库名称 */
    targetWarehouseName: string;
    /** 单据审核状态 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例ID */
    processInstanceId: string;
    /** 单据状态值，字典wmTransferDocStatus */
    docStatus: string;
    /** 单据状态标签，字典wmTransferDocStatus */
    docStatusLabel: string;
  }

  export interface TransferDetail {
    /** 库存调拨子项ID */
    transferItemId: string;
    /** 库存调拨单据ID */
    transferDocId: string;
    /** 库存调拨单据编号 */
    transferDocNumber: string;
    /** 调拨类型值，枚举WmTransferTypeEnums */
    docCode: string;
    /** 调拨类型标签，枚举WmTransferTypeEnums */
    docCodeLabel: string;
    /** 物料ID */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 物料别名 */
    materialAlias: string;
    /** 物料图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 物料大类值，字典baseMaterialType */
    materialType: string;
    /** 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料细类名称 */
    materialCategoryName: string;
    /** 原仓库ID */
    oldWarehouseId: string;
    /** 原仓库编号 */
    oldWarehouseCode: string;
    /** 原仓库名称 */
    oldWarehouseName: string;
    /** 原库位ID */
    oldLocationId: string;
    /** 原库位编号 */
    oldLocationCode: string;
    /** 原库位名称 */
    oldLocationName: string;
    /** 目标仓库ID */
    targetWarehouseId: string;
    /** 目标仓库编号 */
    targetWarehouseCode: string;
    /** 目标仓库名称 */
    targetWarehouseName: string;
    /** 目标库位ID */
    targetLocationId: string;
    /** 目标库位编号 */
    targetLocationCode: string;
    /** 目标库位名称 */
    targetLocationName: string;
    /** 批次号 */
    batchNumber: string;
    /** 调拨数量 */
    transferQuantity: number;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 关闭人ID */
    closeUser: string;
    /** 关闭人姓名 */
    closeUserName: string;
    /** 出库执行人ID */
    outExecutorUser: string;
    /** 出库执行人姓名 */
    outExecutorUserName: string;
    /** 入库执行人ID */
    inExecutorUser: string;
    /** 入库执行人姓名 */
    inExecutorUserName: string;
    /** 提交时间，时间格式为yyyy-MM-dd HH:mm */
    submitTime: string;
    /** 关闭时间，时间格式为yyyy-MM-dd HH:mm */
    closeTime: string;
    /** 出库完成时间 */
    outFinishTime: string;
    /** 入库完成时间 */
    inFinishTime: string;
    /** 完成时间，时间格式为yyyy-MM-dd HH:mm */
    finishTime: string;
    /** 单据状态值，字典wmTransferDocStatus */
    docStatus: string;
    /** 单据状态标签，字典wmTransferDocStatus */
    docStatusLabel: string;
  }

  export interface MyDraftDoc {
    /** 库存调拨单据ID */
    transferDocId: string;
    /** 库存调拨单据编号 */
    transferDocNumber: string;
    /** 调拨类型值，枚举WmTransferTypeEnums */
    docCode: string;
    /** 调拨类型标签，枚举WmTransferTypeEnums */
    docCodeLabel: string;
    /** 原仓库ID */
    oldWarehouseId: string;
    /** 原仓库编号 */
    oldWarehouseCode: string;
    /** 原仓库名称 */
    oldWarehouseName: string;
    /** 目标仓库ID */
    targetWarehouseId: string;
    /** 目标仓库编号 */
    targetWarehouseCode: string;
    /** 目标仓库名称 */
    targetWarehouseName: string;
    /** 最后修改时间，时间格式：yyyy-MM-dd HH:mm */
    modifyTime: string;
    /** 单据状态值，字典wmTransferDocStatus */
    docStatus: string;
    /** 单据状态标签，字典wmTransferDocStatus */
    docStatusLabel: string;
  }

  export interface TransferDocPage extends PageBase {
    records: TransferDoc[];
  }

  export interface TransferDetailPage extends PageBase {
    records: TransferDetail[];
  }

  export interface MyDraftDocPage extends PageBase {
    records: MyDraftDoc[];
  }

  export interface TransferItem {
    /** 库存调拨子项ID */
    transferItemId: string;
    /** 库存调拨单据ID */
    transferDocId: string;
    /** 物料ID */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 物料别名 */
    materialAlias: string;
    /** 物料图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 物料大类值，字典baseMaterialType */
    materialType: string;
    /** 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料细类名称 */
    materialCategoryName: string;
    /** 原库位ID */
    oldLocationId: string;
    /** 原库位编号 */
    oldLocationCode: string;
    /** 原库位名称 */
    oldLocationName: string;
    /** 目标库位ID */
    targetLocationId: string;
    /** 目标库位编号 */
    targetLocationCode: string;
    /** 目标库位名称 */
    targetLocationName: string;
    /** 批次号 */
    batchNumber: string;
    /** 调拨数量 */
    transferQuantity: number;
  }

  export interface TransferDocDetail {
    /** 库存调拨单据ID */
    transferDocId: string;
    /** 库存调拨单据编号 */
    transferDocNumber: string;
    /** 调拨类型值，枚举WmTransferTypeEnums */
    docCode: string;
    /** 调拨类型标签，枚举WmTransferTypeEnums */
    docCodeLabel: string;
    /** 提交人ID */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 关闭人ID */
    closeUser: string;
    /** 关闭人姓名 */
    closeUserName: string;
    /** 提交时间，时间格式为yyyy-MM-dd HH:mm */
    submitTime: string;
    /** 关闭时间，时间格式为yyyy-MM-dd HH:mm */
    closeTime: string;
    /** 原仓库ID */
    oldWarehouseId: string;
    /** 原仓库编号 */
    oldWarehouseCode: string;
    /** 原仓库名称 */
    oldWarehouseName: string;
    /** 目标仓库ID */
    targetWarehouseId: string;
    /** 目标仓库编号 */
    targetWarehouseCode: string;
    /** 目标仓库名称 */
    targetWarehouseName: string;
    /** 单据审核状态 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例ID */
    processInstanceId: string;
    /** 单据状态值，字典wmTransferDocStatus */
    docStatus: string;
    /** 单据状态标签，字典wmTransferDocStatus */
    docStatusLabel: string;
    /** 调拨原因选项列表 */
    remarkOptionList: RemarkItem[];
    /** 备注(原因) */
    remark: string;
    /** 附件流水号 */
    serialNumber: string;
    /** 库存调拨单据子项列表 */
    transferItemList: TransferItem[];
  }
}

/** 查询库存调拨单据分页列表 */
export async function getWareTransferDocPage(params: Recordable<any>) {
  return requestClient.post<TransferApi.TransferDocPage>(
    `${warehousePath}/wm/ware/transfer/getTransferDocPage`,
    params,
  );
}

/** 查询库存调拨单据明细分页列表 */
export async function getWareTransferItemPage(params: Recordable<any>) {
  return requestClient.post<TransferApi.TransferDetailPage>(
    `${warehousePath}/wm/ware/transfer/item/getTransferItemPage`,
    params,
  );
}

/** 查询待我提交的库存调拨单据分页列表 */
export async function getWareTransferMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post<TransferApi.MyDraftDocPage>(
    `${warehousePath}/wm/ware/transfer/getMyDraftDocPage`,
    params,
  );
}

/** 获取库存调拨单据详细信息 */
export async function getWareTransferDocDetail(
  transferDocId: string,
  transferDocNumber?: string,
  isQueryItem?: boolean,
) {
  return requestClient.get<TransferApi.TransferDocDetail>(
    `${warehousePath}/wm/ware/transfer/getTransferDocDetail`,
    {
      params: { transferDocId, transferDocNumber, isQueryItem },
    },
  );
}

/** 导出库存调拨单据列表 */
export async function exportWareTransferDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/ware/transfer/exportTransferDoc`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 导出库存调拨单据明细列表 */
export async function exportWareTransferItem(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/ware/transfer/item/exportTransferItem`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 导出待我提交的库存调拨单据列表 */
export async function exportWareTransferMyDraftDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/ware/transfer/exportMyDraftDoc`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 提交库存调拨单据 */
export async function submitWareTransferDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/ware/transfer/submitTransferDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 暂存库存调拨单据 */
export async function saveOrModWareTransferDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/ware/transfer/saveOrModTransferDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 删除库存调拨单据 */
export async function delWareTransferDoc(transferDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/ware/transfer/delTransferDoc/${transferDocId}`,
  );
}

/** 批量删除待我提交的库存调拨单据 */
export async function batchDelWareTransferDoc(params: Recordable<any>) {
  return requestClient.get(
    `${warehousePath}/wm/ware/transfer/batchDelTransferDoc`,
    {
      params: {
        params,
      },
    },
  );
}

/** 执行库存调拨单据 */
export async function execWareTransferDoc(transferDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/ware/transfer/execTransferDoc/${transferDocId}`,
  );
}

/** 关闭库存调拨单据 */
export async function closeWareTransferDoc(transferDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/ware/transfer/closeTransferDoc/${transferDocId}`,
  );
}
