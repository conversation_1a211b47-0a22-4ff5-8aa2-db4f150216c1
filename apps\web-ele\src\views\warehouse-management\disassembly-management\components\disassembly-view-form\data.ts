import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 调整单信息 */
export function useFormSchema(
  docStatus: string,
  isStaff: boolean,
  isAutoIo: boolean,
): VbenFormSchema[] {
  const data = [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'assemblyDocNumber',
      label: '单据编号',
    },
    {
      component: 'Input',
      fieldName: 'productMaterialName',
      label: '成品',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'baseUnitLabel',
      label: '基本单位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialSpecs',
      label: '规格类型',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'warehouseName',
      label: '所属仓库',
    },
    {
      component: 'Input',
      fieldName: 'quantity',
      label: '拆卸数量',
    },
    {
      component: 'Input',
      fieldName: 'docStatusLabel',
      label: '单据状态',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'submitUserName',
      label: '申请人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'submitTime',
      label: '提交时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'executorUserName',
      label: '执行人',
    },
    {
      component: 'Input',
      dependencies: {
        if(row: any) {
          return row.outBoundDocNumber;
        },
        triggerFields: ['outBoundDocNumber'],
      },
      fieldName: 'outBoundDocNumber',
      label: '出库单',
    },
    {
      component: 'Input',
      dependencies: {
        if(row: any) {
          return row.inBoundDocNumber;
        },
        triggerFields: ['inBoundDocNumber'],
      },
      fieldName: 'inBoundDocNumber',
      label: '入库单',
    },
    {
      component: 'Input',
      dependencies: {
        if(row: any) {
          return row.inOutCancelDocNumber;
        },
        triggerFields: ['inOutCancelDocNumber'],
      },
      fieldName: 'inOutCancelDocNumber',
      label: '取消单',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      dependencies: {
        if(row: any) {
          return row.closeTime;
        },
        triggerFields: ['closeTime'],
      },
      fieldName: 'closeTime',
      label: '关闭时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      dependencies: {
        if(row: any) {
          return row.closeUserName;
        },
        triggerFields: ['closeUserName'],
      },
      fieldName: 'closeUserName',
      label: '关闭申请人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', null, '暂无附件');
        }
        return h(UploadFiles, {
          mode: 'readMode',
          serialNumber: props.modelValue,
          tableProps: {
            maxHeight: '300',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full',
    },
    {
      component: 'Input',
      fieldName: 'documentProcess',
      formItemClass: 'col-span-full',
      label: '单据流程',
    },
  ];

  if (docStatus === 'awaitOut' && isStaff && !isAutoIo) {
    // 在 '执行人'后面插入 '领料码'
    const executorUserNameIndex = data.findIndex(
      (item) => item.fieldName === 'executorUserName',
    );
    if (executorUserNameIndex !== -1) {
      data.splice(executorUserNameIndex + 1, 0, {
        component: 'Input',
        fieldName: 'execCode',
        label: '领料码',
      });
    }
  }

  return data;
}
