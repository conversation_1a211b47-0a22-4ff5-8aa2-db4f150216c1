<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage } from 'element-plus';

import Form from '../../components/FormEdit.vue';

defineProps({
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
  docCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'handleCancel',
  'submitSuccess',
  'saveSuccess',
  'batchNumberLoading',
]);

const handleCancel = () => {
  emits('handleCancel');
};

const FormRef = ref();

const submitHandle = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FormRef.value?.submitHandle();
    if (result) {
      emits('submitSuccess');
    }
  } catch {
    ElMessage.error('提交失败');
  } finally {
    emits('batchNumberLoading', false);
  }
};

const saveHandle = async () => {
  try {
    emits('batchNumberLoading', true);
    const result = await FormRef.value?.saveHandle();
    if (result) {
      emits('saveSuccess');
    }
  } catch {
    ElMessage.error('暂存失败');
  } finally {
    emits('batchNumberLoading', false);
  }
};
</script>

<template>
  <div class="relative mb-12 h-full">
    <Form
      ref="FormRef"
      :batchnum-doc-id="batchnumDocId"
      :batchnum-doc-number="batchnumDocNumber"
      :doc-code="docCode"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="saveHandle"
        v-access:code="'wm:batchnum:exec'"
      >
        暂存
      </ElButton>
      <ElButton
        type="primary"
        @click="submitHandle"
        v-access:code="'wm:batchnum:exec'"
      >
        提交
      </ElButton>
    </div>
  </div>
</template>
