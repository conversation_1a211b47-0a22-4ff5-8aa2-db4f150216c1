import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

/** 其它出库申请单 */
export namespace OutBound {
  /** 提交和暂存返回 */
  export interface CommitAndSaveRes {
    /** 单据id */
    docId: string;
    /** 单据编号 */
    docNumber: string;
  }
  /** 分页基本 */
  export interface PageBase {
    [key: string]: any;
    records: Array<any>;
    total: string;
    pageSize: string;
    pageNum: string;
    pages: string;
  }
  /** 时间信息基本 */
  export interface Timestamps {
    /** 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;
    /** 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;
  }
  /** 出入库类型信息基本 */
  export interface InOutType {
    /** 其它出入库类型值，枚举WmInOrOutEnums */
    docType: string;
    /** 其它出入库类型标签，枚举WmInOrOutEnums */
    docTypeLabel: string;
    /** 出入库申请类型值，出库类型字典wmOtherOutReqType，入库类型字典wmOtherInReqType */
    docCode: string;
    /** 出入库申请类型标签，出库类型字典wmOtherOutReqType，入库类型字典wmOtherInReqType */
    docCodeLabel: string;
  }
  /** 使用人信息基本 */
  export interface UserInfo {
    /** 使用人id */
    materialUser: string;
    /** 使用人姓名 */
    materialUserName: string;
    /** 使用人部门id */
    materialUserDeptId: string;
    /** 使用人部门名称 */
    materialUserDeptName: string;
  }
  /** 提交人信息基本 */
  export interface SubmitUserInfo {
    /** 提交人id */
    submitUser: string;
    /** 提交人姓名 */
    submitUserName: string;
    /** 提交人部门id */
    submitUserDeptId: string;
    /** 提交人部门名称 */
    submitUserDeptName: string;
  }
  /** 领料人信息基本 */
  export interface CollectorUserInfo {
    /** 领料人id */
    collectorUser: string;
    /** 领料人姓名 */
    collectorUserName: string;
    /** 领料人部门id */
    collectorUserDeptId: string;
    /** 领料人部门名称 */
    collectorUserDeptName: string;
  }
  /** 物料信息基本 */
  export interface MaterialBase {
    /** 物料ID */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 物料图片ID */
    pictureFileId: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;
    /** 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 物料属性标签，字典baseMaterialAttribute	 */
    materialAttributeLabel: string;
    /** 物料大类值，字典baseMaterialType */
    materialType: string;
    /** 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料细类名称 */
    materialCategoryName: string;
    /** 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard: boolean;
  }

  /** 申请明细数据和实际明细数据共有数据 */
  export interface applyItemAndactualItem {
    /** 仓库id */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 库位id */
    locationId: string;
    /** 库位编号 */
    locationCode: string;
    /** 库位名称 */
    locationName: string;
    /** 批次号 */
    batchNumber: string;
    /**	均价（单价），默认不可见 */
    unitPrice: number;
  }
  /**	申请明细数据 */
  export interface applyItem extends applyItemAndactualItem {
    /** 出入库申请数量 */
    applyQuantity: number;
  }
  /** 实际明细数据 */
  export interface actualItem extends applyItemAndactualItem {
    /** 出入库实际数量 */
    actualQuantity: number;
  }

  /** 其它出入库申请单数据 */
  export interface InOutBoundReqDoc
    extends SubmitUserInfo,
      Timestamps,
      UserInfo {
    /** 其它出入库申请单id */
    inOutReqDocId: string;
    /** 其它出入库申请单编号 */
    inOutReqDocNumber: string;
    /** 审核状态，写入当前流程所在节点 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例id */
    processInstanceId: string;
    /** 单据状态，出库类型字典wmOutApplyDocStatus，入库类型字典wmInApplyDocStatus */
    docStatus: string;
    /** 单据状态标签，出库类型字典wmOutApplyDocStatus，入库类型字典wmInApplyDocStatus	 */
    docStatusLabel: string;
  }
  /** 其它出入库申请单据明细数据 */
  export interface OutBound extends InOutBoundReqDoc, MaterialBase {
    /** 申请总数 */
    applyQuantitySum: number;
    /** 实际总数 */
    actualQuantitySum: number;
    /** 申请明细列表 */
    applyItemList: applyItem[];
    /** 实际明细列表 */
    actualItemList: actualItem[];
  }
  /** 待我提交的其它出入库申请数据 */
  export interface MyDraftDoc extends InOutType, UserInfo {
    /** 其它出入库申请单id */
    inOutReqDocId: string;
    /** 其它出入库申请单编号 */
    inOutReqDocNumber: string;
    /** 是否补录 */
    isRectify: boolean;
    /** 创建人id */
    createUser: number;
    /** 创建人姓名 */
    createUserName: string;
    /** 最后修改时间，时间格式：yyyy-MM-dd HH:mm */
    modifyTime: string;
  }
  /** 其它出入库申请单据申请子项 和 其它出入库申请单据实际子项共有属性*/
  export interface InOutReqApplyAndInOutReqActual
    extends applyItem,
      Omit<MaterialBase, 'isStandard'> {
    /** 其它出入库申请单 ID */
    inOutReqDocId: number;
    /** 其它出入库申请单编号 */
    inOutReqDocNumber: string;
    /** 其它出入库类型值，枚举 WmInOrOutEnums */
    docType: string;
    /** 其它出入库类型标签，枚举 WmInOrOutEnums */
    docTypeLabel: string;
    /** 备注 */
    remark: string;
  }
  /** 其它出入库申请单据申请子项 */
  export interface InOutReqApply extends InOutReqApplyAndInOutReqActual {
    /** 其它出入库申请单据申请子项 ID */
    applyItemId: number;
  }
  /** 其它出入库申请单据实际子项 */
  export interface InOutReqActual extends InOutReqApplyAndInOutReqActual {
    /** 其它出入库申请单据实际子项ID */
    actualItemId: number;
  }
  /** 其它出入库申请单详细信息的数据 */
  export interface InOutBoundReqDocDetailReqItemList
    extends InOutType,
      MaterialBase,
      SubmitUserInfo,
      Timestamps,
      UserInfo {
    /** 实际明细列表 */
    actualItemList: actualItem[];
    /** 实际总数 */
    actualQuantitySum: number;
    /** 申请明细列表 */
    applyItemList: applyItem[];
    /** 申请总数 */
    applyQuantitySum: number;
    /** 审核状态，写入当前流程所在节点 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 单据状态，出库类型字典 wmOutApplyDocStatus，入库类型字典 wmInApplyDocStatus */
    docStatus: string;
    /** 单据状态标签，出库类型字典 wmOutApplyDocStatus，入库类型字典 wmInApplyDocStatus */
    docStatusLabel: string;
    /** 其它出入库申请单 id */
    inOutReqDocId: number;
    /** 其它出入库申请单编号 */
    inOutReqDocNumber: string;
    /** 是否补录 */
    isRectify: boolean;
    /** 领料码 */
    collectCode: string;
  }
  /** 其它出入库申请单详细信息 */
  export interface InOutBoundReqDocDetail
    extends CollectorUserInfo,
      InOutType,
      SubmitUserInfo,
      Timestamps,
      UserInfo {
    /** 其它出入库申请单 id */
    inOutReqDocId: number;
    /** 其它出入库申请单编号 */
    inOutReqDocNumber: string;
    /** 是否补录 */
    isRectify: boolean;
    /** 审核状态，写入当前流程所在节点 */
    auditStatus: string;
    /** 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;
    /** 审核流程实例 id */
    processInstanceId: string;
    /** 单据状态，出库类型字典 wmOutApplyDocStatus，入库类型字典 wmInApplyDocStatus */
    docStatus: string;
    /** 单据状态标签，出库类型字典 wmOutApplyDocStatus，入库类型字典 wmInApplyDocStatus */
    docStatusLabel: string;
    /** 附件流水号 */
    serialNumber: number;
    /** 数据 */
    reqItemList: InOutBoundReqDocDetailReqItemList[];
    /** 是否代领 */
    isProxyExec: boolean;
  }
  /** 其它出入库申请单据明细分页列表 */
  export interface InOutBoundPage extends PageBase {
    records: OutBound[];
  }
  /** 待我提交的其它出入库申请单分页列表 */
  export interface MyDraftDocPage extends PageBase {
    records: MyDraftDoc[];
  }
  /** 其它出入库申请单据申请子项分页列表 */
  export interface InOutReqApplyPage extends PageBase {
    records: InOutReqApply[];
  }
  /** 其它出入库申请单据实际子项分页列表 */
  export interface InOutReqActualPage extends PageBase {
    records: InOutReqActual[];
  }
  /** 他出入库申请单分页列表 */
  export interface InOutReqDocPage extends PageBase {
    records: InOutBoundReqDoc[];
  }
}

/** 提交其它出库申请单 */
export async function submitInOutReqDoc(params: Recordable<any>) {
  return requestClient.post<OutBound.CommitAndSaveRes>(
    `${warehousePath}/wm/outBound/req/submitInOutReqDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}
/** 暂存其它出库申请单 */
export async function saveInOutReqDoc(params: Recordable<any>) {
  return requestClient.post<OutBound.CommitAndSaveRes>(
    `${warehousePath}/wm/outBound/req/saveInOutReqDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}
/** 查询其它出库申请单据明细分页列表 */
export async function getInOutReqItemPage(params: Recordable<any>) {
  return requestClient.post<OutBound.InOutBoundPage>(
    `${warehousePath}/wm/outBound/req/item/getInOutReqItemPage`,
    params,
  );
}
/** 查询待我提交的其它出库申请单分页列表 */
export async function getOtherMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post<OutBound.MyDraftDocPage>(
    `${warehousePath}/wm/outBound/req/getMyDraftDocPage`,
    params,
  );
}
/** 查询其它出库申请单据实际子项分页列表 */
export async function getInOutReqActualPage(params: Recordable<any>) {
  return requestClient.post<OutBound.InOutReqActualPage>(
    `${warehousePath}/wm/outBound/req/getInOutReqActualPage`,
    params,
  );
}
/** 查询其它出库申请单分页列表 */
export async function getInOutReqDocPage(params: Recordable<any>) {
  return requestClient.post<OutBound.InOutReqDocPage>(
    `${warehousePath}/wm/outBound/req/getInOutReqDocPage`,
    params,
  );
}
/** 导出其它出库申请单列表 */
export async function exportInOutReqDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/req/exportInOutReqDoc`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 导出待我提交的其它出库申请单分页列表*/
export async function exportInOutMyDraftDocPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/req/exportMyDraftDocPage`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 导出其它出库申请单据明细列表*/
export async function exportInOutReqItem(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/req/item/exportInOutReqItem`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 导出其它出库申请单据申请子项列表 */
export async function exportInOutReqApplyItem(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${warehousePath}/wm/outBound/req/exportInOutReqApplyItem`,
    params,
    {
      responseReturn: 'body',
      responseType: 'blob',
    },
  );
}
/** 导出其它出库申请单据实际子项列表 */
export async function exportInOutReqActualItem(params: Recordable<any>) {
  return requestClient.post<Blob>(
    `${warehousePath}/wm/outBound/req/exportInOutReqActualItem`,
    params,
    {
      responseReturn: 'body',
      responseType: 'blob',
    },
  );
}
/** 删除待我提交的其它出库申请单 */
export async function delInOutReqDoc(inOutReqDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/outBound/req/delInOutReqDoc/${inOutReqDocId}`,
  );
}
/** 关闭其它出库申请单 */
export async function closeInOutReqDoc(inOutReqDocId: string) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/req/closeInOutReqDoc/${inOutReqDocId}`,
  );
}
/** 批量删除待我提交的其它出库申请单 */
export async function batchDelInOutReqDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/req/batchDelInOutReqDoc`,
    params,
  );
}
/** 获取其它出库申请单详细信息 */
export async function getInOutReqDocDetail(
  inOutReqDocId: string,
  inOutReqDocNumber?: string,
  isQueryItem?: boolean,
) {
  return requestClient.get<OutBound.InOutBoundReqDocDetail>(
    `${warehousePath}/wm/outBound/req/getInOutReqDocDetail`,
    {
      params: {
        inOutReqDocId,
        inOutReqDocNumber,
        isQueryItem,
      },
    },
  );
}
