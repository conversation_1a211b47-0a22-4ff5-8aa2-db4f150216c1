<script setup lang="ts">
import type { InventoryQueryApi } from '#/api/warehouse-management';

import { ref } from 'vue';

import {
  getInventoryDetailChangeList,
  getInventoryList,
} from '#/api/warehouse-management';
import TipsPopover from '#/components/tips-popover/index.vue';
import { add, subtract } from '#/utils/numberUtils';

const props = defineProps({
  /** 调拨数量 */
  transferQuantity: {
    type: Number,
    default: 0,
  },
  /** 批次号 */
  batchNumber: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 加/减 */
  type: {
    type: String,
    default: 'add',
  },
  /** 库位id */
  locationId: {
    type: String,
    default: '',
  },
  /** 是否已完成 */
  isCompleted: {
    type: Boolean,
    default: false,
  },
  /** 单据ID */
  docId: {
    type: String,
    default: '',
  },
});

const loading = ref(true);

const beforeInventory = ref(0);
const afterInventory = ref(0);
const inventoryList = ref<InventoryQueryApi.InventoryDetailList[]>([]);

const getInventoryListHandle = async () => {
  try {
    loading.value = true;
    const res = await getInventoryList({
      batchNumber: props.batchNumber,
      materialIdList: props.materialId,
      locationIdList: props.locationId,
    });

    loading.value = false;
    inventoryList.value = res;
  } catch {
    loading.value = false;
    return null;
  }
};

const inventoryDetailChangeList = ref<
  InventoryQueryApi.InventoryDetailChangeItem[]
>([]);

const getInventoryDetailChangeListHandle = async () => {
  try {
    loading.value = true;
    const res = await getInventoryDetailChangeList({
      batchNumber: props.batchNumber,
      materialIdList: props.materialId,
      locationIdList: props.locationId,
      docIdList: props.docId,
    });
    inventoryDetailChangeList.value = res;
    loading.value = false;
  } catch {
    loading.value = false;
    return null;
  }
};

const showHandle = async () => {
  switch (props.isCompleted) {
    case true: {
      await getInventoryDetailChangeListHandle();
      inventoryDetailChangeList.value.forEach((item) => {
        beforeInventory.value = add(beforeInventory.value, item.oldInventory);
      });

      inventoryDetailChangeList.value.forEach((item) => {
        afterInventory.value = add(afterInventory.value, item.newInventory);
      });

      break;
    }
    default: {
      await getInventoryListHandle();
      inventoryList.value.forEach((item) => {
        beforeInventory.value = add(beforeInventory.value, item.inventory);
      });

      afterInventory.value =
        props.type === 'add'
          ? add(beforeInventory.value, props.transferQuantity)
          : subtract(beforeInventory.value, props.transferQuantity);
    }
  }
};

const hideHandle = () => {
  beforeInventory.value = 0;
  afterInventory.value = 0;
  inventoryList.value = [];
  inventoryDetailChangeList.value = [];
  loading.value = true;
};
</script>

<template>
  <TipsPopover
    @show="showHandle"
    @hide="hideHandle"
    placement="top"
    class="!cursor-pointer"
  >
    <div v-loading="loading">
      <div v-if="loading" class="flex h-full items-center justify-center">
        <span>数据加载中...</span>
      </div>
      <div class="text-gray-500" v-else>
        <span>库存量</span>
        <div>
          <span>调拨前：</span>
          <span>{{ beforeInventory || 0 }}</span>
        </div>
        <div>
          <span>调拨后：</span>
          <span>
            {{ afterInventory || 0 }}
            <span v-if="props.type === 'add'" class="text-red-500">
              ( {{ `+${props.transferQuantity || 0}` }})
            </span>
            <span v-if="props.type === 'subtract'" class="text-green-500">
              ( {{ `-${props.transferQuantity || 0}` }})
            </span>
          </span>
        </div>
      </div>
    </div>
  </TipsPopover>
</template>
