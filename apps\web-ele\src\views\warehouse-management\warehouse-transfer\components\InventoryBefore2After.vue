<script setup lang="ts">
import type { InventoryQueryApi } from '#/api/warehouse-management';

import { ref } from 'vue';

import {
  getInventoryChangeList,
  getInventoryList,
} from '#/api/warehouse-management';
import TipsPopover from '#/components/tips-popover/index.vue';
import { add, subtract } from '#/utils/numberUtils';

const props = defineProps({
  /** 调拨数量 */
  transferQuantity: {
    type: Number,
    default: 0,
  },
  /** 批次号 */
  batchNumber: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 加/减 */
  type: {
    type: String,
    default: 'add',
  },
  /** 库位id */
  locationId: {
    type: String,
    default: '',
  },
  /** 是否已完成 */
  isCompleted: {
    type: Boolean,
    default: false,
  },
  /** 单据ID */
  docId: {
    type: String,
    default: '',
  },
});

const loading = ref(true);

const beforeInventory = ref(0);
const afterInventory = ref(0);
const inventoryList = ref<InventoryQueryApi.InventoryDetailList[]>([]);

const getInventoryListHandle = async () => {
  try {
    loading.value = true;
    const res = await getInventoryList({
      batchNumber: props.batchNumber,
      materialIdList: props.materialId,
      locationIdList: props.locationId,
    });

    loading.value = false;
    inventoryList.value = res;
  } catch {
    loading.value = false;
    return null;
  }
};

const inventoryChangeList = ref<InventoryQueryApi.InventoryChangeItem[]>([]);

const getInventoryChangeListHandle = async () => {
  try {
    loading.value = true;
    const res = await getInventoryChangeList({
      batchNumber: props.batchNumber,
      materialIdList: props.materialId,
      locationIdList: props.locationId,
      docIdList: props.docId,
    });
    inventoryChangeList.value = res;
    loading.value = false;
  } catch {
    loading.value = false;
    return null;
  }
};

const showHandle = async () => {
  switch (props.isCompleted) {
    case true: {
      await getInventoryChangeListHandle();
      inventoryChangeList.value.forEach((item) => {
        beforeInventory.value = add(beforeInventory.value, item.oldInventory);
      });

      inventoryChangeList.value.forEach((item) => {
        afterInventory.value = add(afterInventory.value, item.newInventory);
      });

      break;
    }
    default: {
      await getInventoryListHandle();
      inventoryList.value.forEach((item) => {
        beforeInventory.value = add(beforeInventory.value, item.inventory);
      });

      afterInventory.value =
        props.type === 'add'
          ? add(beforeInventory.value, props.transferQuantity)
          : subtract(beforeInventory.value, props.transferQuantity);
    }
  }
};

const hideHandle = () => {
  beforeInventory.value = 0;
  afterInventory.value = 0;
  inventoryList.value = [];
  inventoryChangeList.value = [];
  loading.value = true;
};
</script>

<template>
  <TipsPopover @show="showHandle" @hide="hideHandle" placement="top">
    <div v-loading="loading">
      <div v-if="loading" class="flex h-full items-center justify-center">
        <span>数据加载中...</span>
      </div>
      <div
        v-else-if="
          inventoryList.length === 0 && inventoryChangeList.length === 0
        "
        class="flex h-full items-center justify-center"
      >
        <span>暂无数据</span>
      </div>
      <div class="text-gray-500" v-else>
        <span>库存量</span>
        <div>
          <span>调拨前：</span>
          <span>{{ beforeInventory }}</span>
        </div>
        <div>
          <span>调拨后：</span>
          <span>
            {{ afterInventory }}
            <span v-if="props.type === 'add'" class="text-red-500">
              ( {{ `+${props.transferQuantity}` }})
            </span>
            <span v-if="props.type === 'subtract'" class="text-green-500">
              ( {{ `-${props.transferQuantity}` }})
            </span>
          </span>
        </div>
      </div>
    </div>
  </TipsPopover>
</template>
