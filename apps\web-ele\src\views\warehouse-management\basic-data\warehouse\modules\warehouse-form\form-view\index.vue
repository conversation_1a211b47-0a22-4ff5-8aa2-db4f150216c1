<script setup lang="ts">
import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox, ElScrollbar, ElTag } from 'element-plus';

import {
  getOriginalDocConfigList,
  getWarehouseDetail,
  getWarehouseRestriction,
  lockWarehouse,
  unlockWarehouse,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import LocationTable from '../../location-table/index.vue';
import { useFormSchema, useFormSchema2 } from './data';

const props = defineProps({
  warehouseId: {
    type: String,
    default: '',
  },
  /** 仓库编码 */
  warehouseCode: {
    type: String,
    default: '',
  },
  viewBtn: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(['formSubmitSuccess', 'edit']);
/** 是否锁定 */
const isLock = ref(false);
const loading = ref(false);
/** 仓库详细信息 */
const warehouseInfo = ref<WarehouseInfoApi.WarehouseDetail>();
/** 仓库策略信息 */
const warehouseRestriction = ref<WarehouseInfoApi.Restriction>();
/** 操作限制单据列表 */
const limitDocsList = ref<
  {
    docCode: string;
    docName: string;
  }[]
>([]);
/** 基础资料表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    formItemClass: 'items-baseline',
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 仓库策略表单 */
const [RestrictionForm, RestrictionFFormApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    labelWidth: 125,
    formItemClass: 'col-span-full',
  },
  schema: useFormSchema2(),
  showDefaultActions: false,
});

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    // 获取仓库详细信息 获取仓库策略信息 操作限制单据列表
    const [resWarehouse, resRestriction, reslimitDocsList] = await Promise.all([
      getWarehouseDetail(props.warehouseId, props.warehouseCode),
      getWarehouseRestriction(props.warehouseId, props.warehouseCode),
      getOriginalDocConfigList(),
    ]);
    limitDocsList.value = reslimitDocsList;
    warehouseInfo.value = resWarehouse;
    warehouseRestriction.value = resRestriction;
    isLock.value = resWarehouse?.isLock;
    // 填充表单数据
    formApi.setValues(resWarehouse);
    RestrictionFFormApi.setValues(resRestriction);
  } catch {
    ElMessage.error('数据获取失败');
  } finally {
    loading.value = false;
  }
};
/** 解锁 */
const unlock = async () => {
  try {
    await ElMessageBox.confirm('确定解锁吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    if (!warehouseInfo.value?.warehouseId) return;
    await unlockWarehouse(warehouseInfo.value.warehouseId);
    ElMessage.success('解锁成功');
    getData();
    emits('formSubmitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('解锁失败');
  } finally {
    loading.value = false;
  }
};
/** 锁定 */
const lock = async () => {
  try {
    await ElMessageBox.confirm('确定锁定吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await lockWarehouse(props.warehouseId);
    ElMessage.success('锁定成功');
    getData();
    emits('formSubmitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('锁定失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.warehouseId || props.warehouseCode) {
    getData();
  }
});
defineExpose({
  Form,
  formApi,
  RestrictionForm,
  RestrictionFFormApi,
});
</script>

<template>
  <div class="h-full w-full">
    <ElScrollbar noresize>
      <div v-loading="loading">
        <IconFont
          v-if="isLock"
          name="yisuoding"
          :size="150"
          color="dark:bg-gray-800"
          class="text-primary-500 absolute right-20 top-24 z-40"
        />
        <FormCard :is-footer="false">
          <template #title>
            <span>基础资料</span>
          </template>
          <template #default>
            <Form>
              <template #warehouseName="row">
                {{ row.value }}
                <div>
                  <ElTag
                    v-if="warehouseInfo?.isEnable === false"
                    class="ml-[10px]"
                    type="danger"
                  >
                    停用
                  </ElTag>
                  <ElTag class="ml-[10px]">
                    {{
                      warehouseRestriction?.isSafetyStockWarn
                        ? '参与预警'
                        : '不参与库存预警'
                    }}
                  </ElTag>
                  <ElTag class="ml-[10px]">
                    {{
                      warehouseRestriction?.isObsoleteAnalysis
                        ? '参与呆滞分析'
                        : '不参与呆滞分析'
                    }}
                  </ElTag>
                </div>
              </template>
            </Form>
          </template>
        </FormCard>
        <FormCard :is-footer="false">
          <template #title>
            <span>仓库策略</span>
          </template>
          <template #default>
            <RestrictionForm>
              <template #limitMaterialTypeList="row">
                <div>
                  {{
                    row.value
                      ? row.value
                          .map((item: any) => item.limitMaterialTypesLabel)
                          .join('、')
                      : '/'
                  }}
                </div>
              </template>
              <template #limitDocs="row">
                {{
                  row.value
                    ? row.value
                        .map(
                          (code: string) =>
                            limitDocsList.find((item) => item.docCode === code)
                              ?.docName,
                        )
                        .join('、')
                    : '/'
                }}
              </template>
            </RestrictionForm>
          </template>
        </FormCard>
        <FormCard :is-footer="false">
          <template #title>
            <span>库位列表</span>
          </template>
          <template #default>
            <LocationTable
              :warehouse-id="warehouseId || warehouseInfo?.warehouseId || ''"
            />
          </template>
        </FormCard>
      </div>
    </ElScrollbar>
    <div class="flex min-h-[40px] justify-end" v-if="props.viewBtn">
      <ElButton
        type="primary"
        @click="emits('edit', warehouseInfo?.warehouseId)"
        v-access:code="'wm:warehouse:edit:mod'"
      >
        编辑
      </ElButton>
      <ElButton
        type="primary"
        @click="unlock"
        v-access:code="'wm:warehouse:lock:unlock'"
        v-if="isLock"
      >
        解锁
      </ElButton>
      <ElButton
        type="primary"
        @click="lock"
        v-if="!isLock"
        v-access:code="'wm:warehouse:lock:lock'"
      >
        锁定
      </ElButton>
    </div>
  </div>
</template>
