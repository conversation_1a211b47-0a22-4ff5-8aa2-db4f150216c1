import { requestClient } from '#/api/request';

import { dictPath } from '../path';
/** 字典返回值类型*/
export type dictType = {
  /** 字典标识 */
  dictCode: string;
  /** 字典子项id */
  dictItemId: string;
  /** 字典键名 */
  dictKey: string;
  /** 字典标签 */
  dictLabel: string;
  /** 排序号 */
  dictSort: number;
  /** 字典值 */
  dictValue: string;
};

/** 字典子项列表返回类型 */
export interface dictItemListType {
  /** 字典子项ID */
  dictItemId: string;
  /** 字典标识 */
  dictCode: string;
  /** 字典标签 */
  dictLabel: string;
  /** 字典键名 */
  dictKey: string;
  /** 字典值 */
  dictValue: string;
  /** 排序号 */
  dictSort: number;
}

/** 字典子项列表返回类型 */
export interface dictItemListType {
  /** 字典子项ID */
  dictItemId: string;
  /** 字典标识 */
  dictCode: string;
  /** 字典标签 */
  dictLabel: string;
  /** 字典键名 */
  dictKey: string;
  /** 字典值 */
  dictValue: string;
  /** 排序号 */
  dictSort: number;
}

// 根据字典类型集合字典子项Map
export async function getDictListCacheMap(
  dictcode: string, // dictcode多个以英文逗号隔开
) {
  return requestClient.get(
    `${dictPath}/dict/manage/getDictListCacheMap?dictCode=${dictcode}`,
  );
}

/** 根据字典类型获取字典子项列表*/
export async function getDictItemList(dictcode: string) {
  return requestClient.get<dictItemListType[]>(
    `${dictPath}/dict/manage/getDictItemList/${dictcode}`,
  );
}
