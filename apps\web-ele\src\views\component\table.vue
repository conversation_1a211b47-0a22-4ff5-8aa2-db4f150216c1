<script setup lang="ts">
import { ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElButton } from 'element-plus';

import PaginationTable from './PaginationTable.vue';

const [Form] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  layout: 'horizontal',
  // 大屏一行显示3个，中屏一行显示2个，小屏一行显示1个
  wrapperClass: 'grid-cols-1 md:grid-cols-2 lg:grid-cols-3',
  schema: [
    {
      component: 'Input',
      fieldName: 'userName',
      label: '用户名',
    },
    {
      component: 'Input',
      fieldName: 'nickName',
      label: '昵称',
    },
    {
      component: 'Input',
      fieldName: 'phone',
      label: '联系电话',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '账号状态',
      componentProps: {
        filterable: true,
        options: [
          { value: '01', label: '全部' },
          { value: '02', label: '正常' },
          { value: '03', label: '锁定' },
        ],
      },
    },
    {
      component: 'Select',
      fieldName: 'stop',
      label: '是否停用',
      componentProps: {
        filterable: true,
        options: [
          { value: '01', label: '全部' },
          { value: '02', label: '是' },
          { value: '03', label: '否' },
        ],
      },
    },
    {
      fieldName: 'dateRange',
      label: '起止创建日',
      component: 'RangePicker',
    },
  ],
});

// 模拟数据请求函数
const fetchTableData = async (params: any) => {
  // 这里可以替换为实际的 API 请求
  return new Promise<{ data: any[]; total: number }>((resolve) => {
    setTimeout(() => {
      const mockData = [
        { name: '张三', age: 20, address: '北京市' },
        { name: '李四', age: 25, address: '上海市' },
        { name: '王五', age: 30, address: '广州市111' },
        { name: '张三1', age: 20, address: '北京市' },
        { name: '李四2', age: 25, address: '上海市' },
        { name: '王五3', age: 30, address: '广州市111' },
        { name: '张三4', age: 20, address: '北京市' },
        { name: '李四3', age: 25, address: '上海市' },
        { name: '王五4', age: 30, address: '广州市111' },
        { name: '张三5', age: 20, address: '北京市' },
        { name: '李四6', age: 25, address: '上海市' },
        { name: '王五7', age: 30, address: '广州市111' },
        { name: '张三8', age: 20, address: '北京市' },
        { name: '李四9', age: 25, address: '上海市' },
        { name: '王五0', age: 30, address: '广州市111' },
        { name: '张三21', age: 20, address: '北京市' },
        { name: '李四32', age: 25, address: '上海市' },
        { name: '王五43', age: 30, address: '广州市111' },
        { name: '张三54', age: 20, address: '北京市' },
        { name: '李四65', age: 25, address: '上海市' },
        { name: '王五76', age: 30, address: '广州市111' },
        { name: '张三87', age: 20, address: '北京市' },
        { name: '李四654', age: 25, address: '上海市' },
        { name: '王五436', age: 30, address: '广州市111' },
        // 可添加更多数据
      ];
      const filteredData = mockData.filter((item) => {
        if (params.name) {
          return item.name.includes(params.name);
        }
        return true;
      });
      const start = (params.page - 1) * params.pageSize;
      const end = start + params.pageSize;
      const paginatedData = filteredData.slice(start, end);
      resolve({
        data: paginatedData,
        total: filteredData.length,
      });
    }, 500);
  });
};

// 表格列配置
const columns = [
  { prop: 'name', label: '姓名' },
  { prop: 'age', label: '年龄' },
  { prop: 'address', label: '地址' },
];

// 查询条件
const query = ref({ name: '' });

// 是否显示边框
const showBorder = ref(true);
// 是否显示斑马纹
const showStripe = ref(true);

// 页码改变时的处理函数
const handlePageChange = (newPage: number) => {
  console.error('当前页码改变为:', newPage);
};

// 每页显示数量改变时的处理函数
const handleSizeChange = (newSize: number) => {
  console.error('每页显示数量改变为:', newSize);
};

// 处理查询按钮点击事件
// const handleSearch = () => {
//   // 触发数据请求
// };
</script>

<template>
  <div class="m-2">
    <ElCard class="m-0">
      <template #header>
        <div class="flex items-center">
          <Form />
        </div>
      </template>
      <div>
        <div class="border p-2">
          <ElButton type="primary">新增</ElButton>

          <div class="float-right">
            <ElButton>导入模板</ElButton>
            <ElButton>导入</ElButton>
            <ElButton>导出</ElButton>
          </div>
        </div>
        <PaginationTable
          :columns="columns"
          :request-api="fetchTableData"
          :query-params="query"
          :show-border="showBorder"
          :show-stripe="showStripe"
          @page-change="handlePageChange"
          @size-change="handleSizeChange"
        />
      </div>
    </ElCard>
  </div>
</template>
