import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

/** 库存锁库返回 */
export namespace InventoryLock {
  /** 分页基本 */
  export interface PageBase {
    [key: string]: any;
    records: Array<any>;
    total: string;
    pageSize: string;
    pageNum: string;
    pages: string;
  }
  /** 库存锁库数据 */
  export interface InvcBlock {
    blockId: string; // 库存锁库id
    warehouseId: string; // 所属仓库id
    warehouseCode: string; // 所属仓库编号
    warehouseName: string; // 所属仓库名称
    materialId: string; // 物料id
    materialCode: string; // 物料编号
    materialName: string; // 物料名称
    pictureFileId: string; // 物料图片id
    materialSpecs: string; // 规格型号
    baseUnit: string; // 基本单位，字典baseMaterialUnit
    baseUnitLabel: string; // 基本单位名称，字典baseMaterialUnit
    docTypeCode: string; // 关联单据类型标识
    docTypeName: string; // 关联单据类型名称
    docNumber: string; // 关联单据编号
    blockQuantity: number; // 锁库数量
    blockUser: string; // 锁定人
    blockUserName: string; // 锁定人姓名
    blockTime: string; // 锁定时间
    unblockUser: string; // 解锁人
    unblockUserName: string; // 解锁人姓名
    unblockTime: string; // 解锁时间
    isValid: boolean; // 是否有效
  }
  /** 库存锁库详细信息 */
  export interface InvcBlockDetail extends InvcBlock {
    /** 物料属性，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 物料属性名称，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 物料大类值, 字典baseMaterialType */
    materialType: string;
    /** 物料大类标签, 字典baseMaterialType */
    materialTypeLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料细类名称 */
    materialCategoryName: string;
    /** 备注 */
    remark: string;
    /** 附件流水号 */
    serialNumber: string;
    /** 锁库原因选项列表 */
    remarkOptionList: {
      /* 选项ID */
      optionId: string;
      /* 选项名称 */
      optionName: string;
    }[];
  }
  /** 库存锁库分页列表 */
  export interface InvcBlockPage extends PageBase {
    /** 库存锁库数据 */
    records: InvcBlock[];
  }
}

/** 库存锁定*/
export async function lock(params: Recordable<any>) {
  return requestClient.post(`${warehousePath}/wm/invc/block/lock`, params);
}
/** 查询库存锁库分页列表 */
export async function getInvcBlockPage(params: Recordable<any>) {
  return requestClient.post<InventoryLock.InvcBlockPage>(
    `${warehousePath}/wm/invc/block/getInvcBlockPage`,
    params,
  );
}
/** 导出库存锁库列表 */
export async function exportInvcBlock(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/invc/block/exportInvcBlock`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 库存解锁 */
export async function unlock(blockId: string) {
  return requestClient.get(`${warehousePath}/wm/invc/block/unlock/${blockId}`);
}
/** 获取库存锁库详细信息 */
export async function getInvcBlock(blockId: string) {
  return requestClient.get<InventoryLock.InvcBlockDetail>(
    `${warehousePath}/wm/invc/block/getInvcBlock/${blockId}`,
  );
}
