<script setup lang="ts">
import type { VxeGridProps } from '@girant/adapter';

import type { TransferQueryApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getTransferDocDetail } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import InventoryBefore2After from '../InventoryBefore2After.vue';

const props = defineProps({
  /** 调拨单号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
  /** 调拨单id */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

interface RowType {
  materialId: string;
  materialName: string;
  materialCode: string;
  materialSpecs: string;
  baseUnitLabel: string;
  locationAbatchNumber: string;
  oldLocationId: string;
  oldLocationName: string;
  batchNumber: string;
  targetLocationId: string;
  targetLocationName: string;
  transferQuantity: number;
}

const gridOptions: VxeGridProps<RowType> = {
  columns: [
    { title: '序号', type: 'seq', width: 50 },
    { field: 'materialId', title: '物料id', visible: false },
    { field: 'materialName', title: '物料名称', width: 'auto' },
    { field: 'materialCode', title: '物料编号', width: 'auto' },
    { field: 'materialSpecs', title: '物料规格', width: 'auto' },
    { field: 'baseUnitLabel', title: '基本单位', width: 'auto' },
    {
      field: 'locationAbatchNumber',
      title: '原库位-批次号',
      slots: {
        default: 'locationAbatchNumber',
      },
      width: 'auto',
    },
    { field: 'oldLocationId', title: '原库位id', visible: false },
    { field: 'oldLocationName', title: '原库位', visible: false },
    { field: 'batchNumber', title: '批次号', visible: false },
    { field: 'targetLocationId', title: '目标库位id', visible: false },
    {
      field: 'targetLocationName',
      title: '目标库位',
      slots: {
        default: 'targetLocationName',
      },
      width: 'auto',
    },
    { field: 'transferQuantity', title: '调拨数量', width: 80 },
  ],
  data: [],
  pagerConfig: {
    enabled: false,
  },
  sortConfig: {
    multiple: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ gridOptions });

const transferDocDetail = ref<TransferQueryApi.GetTransferDocDetailResponse>(
  {} as TransferQueryApi.GetTransferDocDetailResponse,
);

// 获取详情
const getTransferDocDetailHandle = async () => {
  try {
    const transferDocDetailRes = await getTransferDocDetail({
      transferDocId: props.transferDocId,
      transferDocNumber: props.transferDocNumber,
      isQueryItem: true,
    });
    transferDocDetail.value = transferDocDetailRes;
    return transferDocDetailRes;
  } catch {
    ElMessage.error('获取调拨单据失败');
    return {} as TransferQueryApi.GetTransferDocDetailResponse;
  }
};

/** 获取表单数据 */
const getFormData = (): TransferQueryApi.TransferItem[] => {
  const data = gridApi.grid.getFullData();
  if (data.length === 0) {
    return [] as TransferQueryApi.TransferItem[];
  }

  // 提取出需要的字段
  const result = data?.map((item: any) => {
    const {
      materialId,
      transferQuantity,
      targetLocationId,
      oldLocationId,
      batchNumber,
    } = item;
    return {
      materialId,
      transferQuantity,
      oldLocationId,
      targetLocationId,
      batchNumber,
    };
  });
  return result;
};

onMounted(async () => {
  if (props.transferDocId || props.transferDocNumber) {
    await getTransferDocDetailHandle();
    gridApi.setGridOptions({
      data: transferDocDetail.value.transferItemList,
    });
  } else {
    ElMessage.error('调拨单号或调拨单ID不能为空');
  }
});

defineExpose({
  getFormData,
});
</script>
<template>
  <FormCard :is-footer="false" title="调拨明细">
    <Grid>
      <template #locationAbatchNumber="{ row }">
        <div class="flex items-center">
          <span>{{ `${row.oldLocationName} (${row.batchNumber})` }}</span>
          <InventoryBefore2After
            :doc-id="transferDocId"
            :batch-number="row.batchNumber"
            :material-id="row.materialId"
            :transfer-quantity="row.transferQuantity"
            :location-id="row.oldLocationId"
            type="subtract"
            :is-completed="transferDocDetail.docStatus === 'finish'"
            v-if="!['reject', 'closed'].includes(transferDocDetail.docStatus)"
          />
        </div>
      </template>
      <template #targetLocationName="{ row }">
        <div class="flex items-center">
          <span>{{ row.targetLocationName }}</span>
          <InventoryBefore2After
            :doc-id="transferDocId"
            :batch-number="row.batchNumber"
            :material-id="row.materialId"
            :transfer-quantity="row.transferQuantity"
            :location-id="row.targetLocationId"
            type="add"
            :is-completed="transferDocDetail.docStatus === 'finish'"
            v-if="!['reject', 'closed'].includes(transferDocDetail.docStatus)"
          />
        </div>
      </template>
    </Grid>
  </FormCard>
</template>
