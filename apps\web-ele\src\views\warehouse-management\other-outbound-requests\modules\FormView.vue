<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox, ElScrollbar } from 'element-plus';

import {
  delInOutReqDoc,
  getInOutReqDocDetail,
} from '#/api/warehouse-management';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import OutboundOrderDetailsView from './outbound-order-details/form-view/index.vue';
import OutboundOrderInformationView from './outbound-order-information/form-view/index.vue';

/** 共享数据 */
const sharedData = ref();
const loading = ref(false);

/** 获取审核流程实例ID */
const getProcessId = async () => {
  try {
    const res = await getInOutReqDocDetail(
      sharedData.value.inOutReqDocId,
      sharedData.value.inOutReqDocNumber,
    );
    sharedData.value.processInstanceId = res?.processInstanceId;
  } catch (error) {
    console.error(error);
  }
};
const [ViewModal, viewModalApi] = useVbenModal({
  showConfirmButton: false,
  closeOnClickModal: false,
  title: '出库申请单详情',
  onCancel() {
    viewModalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = viewModalApi.getData<Record<string, any>>();
      if (!sharedData.value.processInstanceId) {
        getProcessId();
      }
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  viewModalApi.close();
};
/** 删除单据 */
const onDelete = async (inOutReqDocId: string) => {
  try {
    await ElMessageBox.confirm('确认删除？', '提示', {
      type: 'warning',
    });
    await delInOutReqDoc(inOutReqDocId);
    ElMessage.success('删除成功');
    refreshList();
  } catch (error) {
    console.error(error);
    // ElMessage.error('删除失败');
  }
};
defineExpose({
  loading,
});
</script>
<template>
  <ViewModal>
    <div v-loading="loading">
      <OutboundOrderInformationView
        :in-out-req-doc-number="sharedData.inOutReqDocNumber"
        :in-out-req-doc-id="sharedData.inOutReqDocId"
      />
      <OutboundOrderDetailsView
        :in-out-req-doc-number="sharedData.inOutReqDocNumber"
        :in-out-req-doc-id="sharedData.inOutReqDocId"
      />
      <FormCard :is-footer="false" v-if="sharedData.processInstanceId">
        <template #title>
          <span>审核流程</span>
        </template>
        <ElScrollbar>
          <ApprovalTimeline
            v-if="sharedData.processInstanceId"
            :process-instance-id="sharedData.processInstanceId"
          />
        </ElScrollbar>
      </FormCard>
    </div>
    <template #center-footer>
      <ElButton
        v-if="sharedData.docStatus === 'pending' || sharedData.showDelBtn"
        type="danger"
        @click="onDelete(sharedData.inOutReqDocId)"
      >
        删除单据
      </ElButton>
    </template>
  </ViewModal>
</template>
