import type { Recordable, UserInfo } from '@vben/types';

import { ref } from 'vue';
import { useRouter } from 'vue-router';

import { LOGIN_PATH } from '@vben/constants';
import { useRefresh } from '@vben/hooks';
import { preferences } from '@vben/preferences';
import { resetAllStores, useAccessStore, useUserStore } from '@vben/stores';

import { ElNotification } from 'element-plus';
import { defineStore } from 'pinia';

import {
  getCustomerinfo,
  getPermsApi,
  getStaffInfo,
  getSupplierinfo,
  getUserInfoApi,
  loginApi,
  logoutApi,
} from '#/api';
import { $t } from '#/locales';
// 定义数据结构
interface PermissionItem {
  permissionCode: string;
}

export const useAuthStore = defineStore('auth', () => {
  const accessStore = useAccessStore();
  const userStore = useUserStore();
  const router = useRouter();
  const { refresh } = useRefresh();
  const loginLoading = ref(false);

  /**
   * 异步处理登录操作
   * Asynchronously handle the login process
   * @param params 登录表单数据
   */
  async function authLogin(
    params: Recordable<any>,
    onSuccess?: () => Promise<void> | void,
  ) {
    // 异步处理用户登录操作并获取 accessToken
    let userInfo: null | UserInfo = null;
    try {
      loginLoading.value = true;
      const { token } = await loginApi(params);

      // 如果成功获取到 accessToken
      if (token) {
        // 将 accessToken 存储到 accessStore 中
        accessStore.setAccessToken(token);

        // 获取用户信息并存储到 accessStore 中
        const [fetchUserInfoResult, accessCodes] = await Promise.all([
          fetchUserInfo(),
          getPermsApi(),
        ]);

        userInfo = fetchUserInfoResult;
        // userStore.setUserInfo(userInfo);
        accessStore.setAccessCodes(extractPermissionCodes(accessCodes));

        if (accessStore.loginExpired) {
          accessStore.setLoginExpired(false);
          refresh();
        } else {
          onSuccess
            ? await onSuccess?.()
            : await router.push(
                userInfo?.homePath || preferences.app.defaultHomePath,
              );
        }

        if (userInfo?.realName) {
          ElNotification({
            message: `${$t('authentication.loginSuccessDesc')}:${userInfo?.realName}`,
            title: $t('authentication.loginSuccess'),
            type: 'success',
          });
        }
      }
    } finally {
      loginLoading.value = false;
    }

    return {
      userInfo,
    };
  }

  async function logout(redirect: boolean = true) {
    try {
      await logoutApi();
    } catch {
      // 不做任何处理
    }

    resetAllStores();
    accessStore.setLoginExpired(false);

    // 回登录页带上当前路由地址
    await router.replace({
      path: LOGIN_PATH,
      query: redirect
        ? {
            redirect: encodeURIComponent(router.currentRoute.value.fullPath),
          }
        : {},
    });
  }
  const userInfoFnMapper: Record<string, () => Promise<any>> = {
    '1': async () => await getStaffInfo(),
    '11': async () => await getSupplierinfo(),
    '21': async () => await getCustomerinfo(),
  };

  async function fetchUserInfo() {
    const userInfo = await getUserInfoApi();
    const key = String(userInfo.currentUserType);

    if (userInfoFnMapper[key]) {
      const additionalInfo = await userInfoFnMapper[key]();

      userStore.setUserInfo({ ...userInfo, ...additionalInfo });
      return { ...userInfo, ...additionalInfo };
    }

    userStore.setUserInfo(userInfo);
    return userInfo;
  }

  function extractPermissionCodes(permissions: PermissionItem[]): string[] {
    return permissions.map((item) => item.permissionCode);
  }

  function $reset() {
    loginLoading.value = false;
  }

  return {
    $reset,
    authLogin,
    fetchUserInfo,
    loginLoading,
    logout,
  };
});
