import type { VbenFormProps } from '@girant/adapter';

import type { Ref } from 'vue';

import { h } from 'vue';

import { z } from '@girant/adapter';
import { ElButton, ElMessage, ElOption, ElSelect } from 'element-plus';

import { generateBatchNumber } from '#/api/warehouse-management/index';
import IconFont from '#/components/IconFont/IconFont.vue';
import BatchNumberAlert from '#/views/warehouse-management/batch-number-operation/components/BatchNumberAlert.vue';

// 生成批次号
const generateBatchNumberForMaterialId = async (materialId: string) => {
  try {
    const batchNumber = await generateBatchNumber(materialId);
    return batchNumber;
  } catch {
    return '';
  }
};

export const generateEditDynamicFormOptions = ({
  locationListOptions,
  currentWarehouseId,
  materialId,
  onQuantityChange,
}: {
  currentWarehouseId: Ref<string>;
  locationListOptions: Ref<any[]>;
  materialId: string;
  onQuantityChange: () => void;
}): VbenFormProps => {
  return {
    schema: [
      {
        component: (props: any) => {
          return h(
            ElSelect,
            {
              modelValue: props.modelValue,
              placeholder: '请选择库位',
              disabled: !currentWarehouseId.value,
            },
            {
              // 使用默认插槽渲染选项
              default: () =>
                locationListOptions.value.map((item) =>
                  h(ElOption, {
                    key: item.value,
                    label: item.label,
                    value: item.value,
                  }),
                ),
            },
          );
        },
        fieldName: 'locationId',
        label: '库位',
        rules: z.string().min(1, '请选择库位'),
        labelWidth: 50,
      },
      {
        component: 'Input',
        fieldName: 'batchNumber',
        label: '批次号',
        defaultValue: '',
        componentProps: {
          placeholder: '为空系统自动生成',
          clearable: true,
        },
        dependencies: {
          triggerFields: ['locationId'],
          componentProps: (props) => {
            return {
              disabled: !props.locationId,
            };
          },
        },
        renderComponentContent: (values: any) => {
          return {
            suffix: () => {
              return h('div', {}, [
                h(
                  ElButton,
                  {
                    link: true,
                    size: 'small',
                    disabled: !values.locationId,
                    onClick: async (e: MouseEvent) => {
                      e.stopPropagation();
                      try {
                        const batchNumber =
                          await generateBatchNumberForMaterialId(materialId);
                        values.batchNumber = batchNumber;
                      } catch {
                        ElMessage.error('生成批次号失败');
                      }
                    },
                  },
                  () => h(IconFont, { name: 'bianji', class: 'iconfont' }),
                ),
                h(BatchNumberAlert, {
                  class: 'absolute right-[-30px] top-0',
                  batchNumber: values.batchNumber,
                  materialId,
                  locationId: values.locationId,
                }),
              ]);
            },
          };
        },
        labelWidth: 60,
        // 选择第一个是div的子元素设置overflow-visible
        formItemClass: 'mr-10 col-span-2 [&>div]:!overflow-visible',
      },
      {
        component: 'InputNumber',
        fieldName: 'quantity',
        label: '数量',
        componentProps: {
          min: 0,
          precision: 3,
          controlsPosition: 'right',
          onChange: onQuantityChange,
        },
        dependencies: {
          triggerFields: ['locationId'],
          componentProps: (props) => {
            return {
              disabled: !props.locationId,
            };
          },
        },
        defaultValue: 0,
        labelWidth: 50,
        rules: z.number().gt(0, '填写数量不能为0'),
      },
    ],
    wrapperClass: 'grid-cols-4',
  };
};
