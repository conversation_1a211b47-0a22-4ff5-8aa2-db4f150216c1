<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCard,
  ElMessage,
  ElMessageBox,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  delAssemblyDoc,
  exportAssemblyMyDraftDocPage,
  getAssemblyMyDraftDocPage,
} from '#/api';
import { docStatusDict } from '#/views/warehouse-management/disassembly-management/utils/index';

import EditForm from '../modules/EditForm.vue';
import ViewForm from '../modules/ViewForm.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const exportLoading = ref(false);
/** 当行单据内容 */
const docRow = ref('');

onMounted(async () => {
  /** 初始化搜索条件 */
  await gridApi.formApi.setValues({
    assemblyDocNumberList:
      props.params?.assemblyDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    productMaterialCodeList:
      props.params?.productMaterialCodeList?.split(',') || [],
    productMaterialName: props.params?.productMaterialName || '',
  });
});

/** 模态框组件*/
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: EditForm,
  destroyOnClose: true,
});
const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: ViewForm,
  destroyOnClose: true,
  onConfirm() {
    viewModalApi.close();
    resubmit(docRow.value);
  },
});

/** 获取筛选器值 */
const getFilterValue = async () => {
  const formValues = await gridApi.formApi.getValues();
  const { modifyTime, ...restFormValues } = formValues;
  return {
    ...restFormValues,
    modifyStartTime: modifyTime?.startTime,
    modifyEndTime: modifyTime?.endTime,
  };
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    schema: useGridFormSchema(),
    resetButtonOptions: { content: '筛选重置' },
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await getFilterValue();
          const res = await getAssemblyMyDraftDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 导出数据 */
const exportHandle = async () => {
  try {
    exportLoading.value = true;
    const formValues = await getFilterValue();
    const response = await exportAssemblyMyDraftDocPage({
      ...formValues,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

/** 新增 */
const onAdd = () => {
  editModalApi
    .setState({ title: '新增' })
    .setData({
      assemblyDocId: '',
      assemblyDocNumber: '',
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 查看 */
const onView = (row: any) => {
  docRow.value = row;
  const confirmText = row.docStatus === 'pending' ? '提交单据' : '再次提交';
  viewModalApi
    .setState({ title: '拆卸单详情', showConfirmButton: true, confirmText })
    .setData({
      assemblyDocId: row.assemblyDocId,
      assemblyDocNumber: row.assemblyDocNumber,
      processInstanceId: row.processInstanceId,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 再次提交 */
const resubmit = async (row: any) => {
  editModalApi
    .setState({ title: '提交单据' })
    .setData({
      assemblyDocId: row.assemblyDocId,
      assemblyDocNumber: row.assemblyDocNumber,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 删除待提交单据 */
const deleteDoc = async (assemblyDocId: string) => {
  try {
    await ElMessageBox.confirm('确定删除吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });

    await delAssemblyDoc(assemblyDocId);
    ElMessage.success('删除成功');
    refreshList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <EditModal class="h-full w-10/12" />
    <ViewModal class="h-full w-10/12" />

    <ElCard class="h-full" body-class="h-full !p-[10px]">
      <Grid>
        <template #toolbar-actions>
          <ElButton
            v-access:code="['wm:assembly:split:submit']"
            type="primary"
            @click="onAdd"
          >
            新增
          </ElButton>
        </template>
        <template #assemblyDocNumber="{ row }">
          <span v-if="row.assemblyDocNumber">{{ row.assemblyDocNumber }}</span>
          <span v-else>暂无单据编号</span>
        </template>
        <template #productMaterialName="{ row }">
          <span>{{ row.productMaterialName }}</span>
          <span v-if="row.productMaterialCode">
            ({{ row.productMaterialCode }})
          </span>
        </template>
        <template #quantity="{ row }">
          <span>{{ row.quantity }}</span>
          <span>{{ row.baseUnitLabel }}</span>
        </template>
        <template #docStatusLabel="{ row }">
          <ElTag size="small" :type="docStatusDict[row.docStatus]">
            {{ row.docStatusLabel }}
          </ElTag>
        </template>
        <template #CellOperation="{ row }">
          <div>
            <ElButton link size="small" @click="onView(row)" type="info">
              查看
            </ElButton>
            <ElButton link size="small" @click="resubmit(row)" type="primary">
              {{ row.docStatus === 'pending' ? '提交单据' : '再次提交' }}
            </ElButton>
            <ElButton
              link
              size="small"
              @click="deleteDoc(row.assemblyDocId)"
              type="danger"
            >
              删除
            </ElButton>
          </div>
        </template>
        <template #toolbar-tools>
          <ElTooltip
            class="box-item"
            effect="light"
            content="导出数据"
            placement="top-start"
          >
            <ElButton :loading="exportLoading" circle @click="exportHandle">
              <template #icon><IconFont name="xiazai" /></template>
            </ElButton>
          </ElTooltip>
        </template>
      </Grid>
    </ElCard>
  </Page>
</template>
