import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
/** 锁库信息 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'materialName',
      label: '物料',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'baseUnitLabel',
      label: '基本单位',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialSpecs',
      label: '规格型号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'warehouseName',
      label: '仓库',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'blockQuantity',
      label: '锁库数量',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'blockUserName',
      label: '锁定人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'blockTime',
      label: '锁定时间',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'docTypeName',
      label: '关联单据类型',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'docNumber',
      label: '关联单据编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full items-start',
      wrapperClass: 'block',
      label: '锁库原因',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', { class: 'mt-[2px]' }, '暂无附件');
        }
        return h(UploadFiles, {
          mode: 'readMode',
          serialNumber: props.modelValue,
          tableProps: {
            maxHeight: '300',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
