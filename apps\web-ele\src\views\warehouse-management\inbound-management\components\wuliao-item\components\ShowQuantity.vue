<script setup lang="ts">
defineProps({
  quantitySum: {
    type: Number,
    default: 0,
  },
  entryQuantitySum: {
    type: Number,
    default: 0,
  },
  baseUnitLabel: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <div class="flex items-center justify-end text-base text-sm text-gray-800">
    <span>申请入库 / 填入数量：</span>
    <span class="font-medium">{{ quantitySum }}</span>
    <span class="mx-1">/</span>
    <span
      class="font-medium"
      :class="[
        entryQuantitySum === quantitySum ? 'text-green-500' : 'text-red-500',
      ]"
    >
      {{ entryQuantitySum }}
    </span>
    <span class="ml-1 text-gray-500"> ({{ baseUnitLabel || '/' }}) </span>
  </div>
</template>
