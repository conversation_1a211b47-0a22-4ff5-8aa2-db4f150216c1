<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { computed, onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import {
  exportInBoundDetailPage,
  getInBoundApplyItemPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';
import { WS } from '#/utils/socket/common-socketio';

import FormToInboundModal from '../FormToInboundModal.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      batchNumber?: string;
      closeEndTime?: string;
      closeStartTime?: string;
      docStatusList?: string;
      inBoundDocNumberList?: string;
      isStandard?: unknown;
      locationName?: string;
      materialAttributeList?: string;
      materialCategoryList?: string;
      materialCodeList?: string;
      materialName?: string;
      materialTypeList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      warehouseName?: string;
    }>,
    default: () => ({}),
  },
});
// ws消息类型
const wsType = [
  'wm.inbound.docstatus.rescind.passed',
  'wm.inbound.docstatus.rescind.checking',
  'wm.inbound.docstatus.rescind.reject',
];
const router = useRouter();

const docStatusList = computed(() => {
  if (props.params?.docStatusList) {
    return props.params.docStatusList.split(',');
  }

  const queryDocStatusList = router.currentRoute.value.query?.docStatusList;
  if (queryDocStatusList) {
    if (Array.isArray(queryDocStatusList)) {
      return queryDocStatusList.filter(
        (item): item is string => typeof item === 'string',
      );
    }
    if (typeof queryDocStatusList === 'string') {
      return queryDocStatusList.split(',');
    }
  }

  return [];
});

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 关闭时间 */
const closeTime = ref({
  // 开始时间
  startTime: props.params?.closeStartTime || '',
  // 结束时间
  endTime: props.params?.closeEndTime || '',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      closeTime.value = {
        startTime: '',
        endTime: '',
      };
      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    // 控制表单是否显示折叠按钮
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(docStatusList.value),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    cellConfig: {
      height: 55,
    },
    columns: useColumns(onActionClick, docStatusList.value),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;

          params.closeStartTime = closeTime.value.startTime;
          params.closeEndTime = closeTime.value.endTime;

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          return await getInBoundApplyItemPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    inBoundDocNumberList: props.params?.inBoundDocNumberList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    applyUserList: props.params?.applyUserList?.split(',') || [],
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    materialName: props.params?.materialName || '',
    isStandard: isEmpty(props.params?.isStandard)
      ? ''
      : props.params?.isStandard,
    materialAttributeList:
      props.params?.materialAttributeList?.split(',') || [],
    materialCategoryList: props.params?.materialCategoryList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
    warehouseName: props.params?.warehouseName || '',
    locationName: props.params?.locationName || '',
    batchNumber: props.params?.batchNumber || '',
  });

  WS.on(wsType, refreshList);
});

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: FormToInboundModal,
  destroyOnClose: true,
  closeOnClickModal: false,
});

function onView(row: RowType) {
  let title = '确认入库';

  switch (row.docStatus) {
    case 'cancelAudit': {
      title = '取消审核详情';
      break;
    }

    case 'pending': {
      title = '入库';
      break;
    }

    case 'stocked': {
      title = '已入库详情';
      break;
    }

    default: {
      title = '已关闭详情';
      break;
    }
  }

  formModalApi
    .setState({
      title,
    })
    .setData({
      inBoundDocId: row.inBoundDocId,
      inBoundDocNumber: row.inBoundDocNumber,
      docStatus: row.docStatus,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
}
function onActionClick(e: OnActionClickParams<any>) {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
  }
}

/** 数据导出 */
async function exportInBoundDetailPageHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.startTime;
    formValues.applyEndTime = applyTime.value.endTime;
    formValues.closeStartTime = closeTime.value.startTime;
    formValues.closeEndTime = closeTime.value.endTime;
    const response = await exportInBoundDetailPage(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="h-full w-10/12" />

    <Grid>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportInBoundDetailPageHandle"
            v-access:code="'wm:inbound:export:item'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>

      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-closeTime>
        <ElDatePicker
          v-model="closeTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, closeTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="closeTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, closeTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
    </Grid>
  </Page>
</template>
