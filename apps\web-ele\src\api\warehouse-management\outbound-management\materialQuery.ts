import type { Recordable } from '@vben/types';

import type { MaterialPendingApi } from './materialPending';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace MaterialQueryApi {
  export interface MaterialQueryPageParams {
    /** 备料单据号列表，多个用英文逗号分隔 */
    prepDocNumberList: string[];
    /** 出库单据号列表，多个用英文逗号分隔 */
    outBoundDocNumberList: string[];
    /** 源单据类型标识列表，精确查询，根据查询来源单据配置列表接口的出参docCode字段。 */
    origDocTypeCodeList: string[];
    /** 申请人id列表，精确查询 */
    applyUserList: string[];
    /** 使用人id列表，精确查询 */
    materialUserList: string[];
    /** 备料状态值列表，字典WmPreparationStateEnums */
    preparationStateList: string[];
    /** 提交起始时间，时间格式：yyyy-MM-dd HH:mm */
    submitTimeStart?: string;
    /** 提交终止时间，时间格式：yyyy-MM-dd HH:mm */
    submitTimeEnd?: string;
    /** 备料起始时间，时间格式：yyyy-MM-dd HH:mm */
    executorStartTime?: string;
    /** 备料终止时间，时间格式：yyyy-MM-dd HH:mm */
    executorTimeEnd?: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  export interface MaterialQueryRecord {
    /* 备料单据ID */
    prepDocId: string;

    /* 备料单据编号 */
    prepDocNumber: string;

    /* 出库单据ID */
    outBoundDocId: string;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 领料人ID */
    materialUser: string;

    /* 领料人姓名 */
    materialUserName: string;

    /* 领料人部门ID */
    materialUserDeptId: string;

    /* 领料人部门名称 */
    materialUserDeptName: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 提交人部门ID */
    submitUserDeptId: string;

    /* 提交人部门姓名 */
    submitUserDeptName: string;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: Record<string, unknown>;

    /* 执行人ID */
    collectorUser: string;

    /* 执行人姓名 */
    collectorUserName: string;

    /* 执行人部门ID */
    collectorUserDeptId: string;

    /* 执行人部门名称 */
    collectorUserDeptName: string;

    /* 执行时间 */
    executorTime: Record<string, unknown>;

    /* 单据审核状态 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例ID */
    processInstanceId: string;

    /* 备料单据状态，字典wmPreparationDocStatus */
    docStatus: string;

    /* 备料单据状态标签，字典wmPreparationDocStatus */
    docStatusLabel: string;
  }
}

/**
 * 获取备料单据分页列表
 */
export async function getPrepDocPage(
  params: MaterialQueryApi.MaterialQueryPageParams,
) {
  return requestClient.post<MaterialQueryApi.MaterialQueryRecord>(
    `${warehousePath}/wm/prep/getPrepDocPage`,
    { ...params },
  );
}

/**
 * 执行备料单据
 */
export async function execPrepDoc(params: MaterialPendingApi.PrepDocParams) {
  return requestClient.post<MaterialQueryApi.MaterialQueryRecord>(
    `${warehousePath}/wm/prep/execPrepDoc`,
    { ...params },
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/**
 * 关闭备料单
 */
export async function closePrepDoc(prepDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/prep/closePrepDoc/${prepDocId}`,
  );
}

/**
 * 备料单据返仓
 */
export async function rollbackPrepDoc(prepDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/prep/rollbackPrepDoc/${prepDocId}`,
  );
}

/** 导出备料单据列表*/
export async function exportPrepDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/prep/exportPrepDoc`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
