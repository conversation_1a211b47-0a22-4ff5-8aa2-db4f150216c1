<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElTooltip } from 'element-plus';

import {
  exportStockedInBoundDoc,
  getAlreadyInBoundDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';
import { WS } from '#/utils/socket/common-socketio';

import FormToInboundModal from '../modules/FormToInboundModal.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      executorEndTime?: string;
      executorStartTime?: string;
      executorUserList?: string;
      inBoundDocNumberList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
    }>,
    default: () => ({}),
  },
});
// ws消息类型
const wsType = [
  'wm.inbound.docstatus.pending.add',
  'wm.inbound.docstatus.finished',
  'wm.inbound.docstatus.cancelAudit',
  'wm.inbound.docstatus.pending.cancelReject',
  'wm.inbound.docstatus.close',
  'wm.inbound.export.pending',
  'wm.inbound.export.finished',
];
/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 入库时间 */
const executorTime = ref({
  // 开始时间
  startTime: props.params?.executorStartTime || '',
  // 结束时间
  endTime: props.params?.executorEndTime || '',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      executorTime.value = {
        startTime: '',
        endTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;

          params.executorStartTime = executorTime.value.startTime;
          params.executorEndTime = executorTime.value.endTime;

          if (params.executorUserList) {
            params.executorUserList = params.executorUserList.join(',');
          }

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          return await getAlreadyInBoundDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    showOverflow: false,
    cellConfig: {
      height: 55,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    inBoundDocNumberList: props.params?.inBoundDocNumberList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    applyUserList: props.params?.applyUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
  });
  WS.on(wsType, refreshList);
});

const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: FormToInboundModal,
  destroyOnClose: true,
  closeOnClickModal: false,
});

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

function onView(row: RowType) {
  formModalApi
    ?.setState({ title: '已入库详情' })
    .setData({
      inBoundDocId: row.inBoundDocId,
      inBoundDocNumber: row.inBoundDocNumber,
      docStatus: 'stocked',
      refreshList: () => {
        refreshList();
      },
    })
    .open();
}

function onActionClick(e: OnActionClickParams<any>) {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
  }
}

/** 数据导出 */
async function exportStockedInBoundDocHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.startTime;
    formValues.applyEndTime = applyTime.value.endTime;
    formValues.executorStartTime = executorTime.value.startTime;
    formValues.executorEndTime = executorTime.value.endTime;
    const response = await exportStockedInBoundDoc(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="h-full w-10/12" />

    <Grid>
      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-executorTime>
        <ElDatePicker
          v-model="executorTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, executorTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="executorTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, executorTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportStockedInBoundDocHandle"
            v-access:code="'wm:inbound:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
