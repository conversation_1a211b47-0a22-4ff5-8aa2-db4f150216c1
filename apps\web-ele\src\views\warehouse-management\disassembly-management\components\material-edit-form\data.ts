import type { VxeGridProps, VxeTableGridOptions } from '@girant/adapter';

import { h } from 'vue';

import MaterialEditField from './components/MaterialField.vue';

/** 原料显示名拼接 */
export const viewMaterialName = (row: any) => {
  // 使用可选链确保安全访问
  const materialName = row.materialName || '';
  const materialCode = row.materialCode || '';

  if (materialName && materialCode) {
    return `${materialName} (${materialCode})`;
  }

  return materialName;
};

/** 物料信息 */
export const useGridOptions = (): VxeGridProps => {
  return {
    columns: [
      { title: '序号', type: 'seq', width: 50 },
      {
        editRender: {},
        field: 'materialId',
        slots: {
          default: ({ row }) => {
            return h(MaterialEditField, {
              modelValue: row.materialId,
              editable: false,
              pictureFileId: row.pictureFileId,
            });
          },
          edit: ({ $table, row }) => {
            return h(MaterialEditField, {
              modelValue: row.materialId,
              onChange: async (materialId, materialDetail) => {
                if (!materialId) {
                  $table.setRow(row, {
                    materialId: undefined,
                    materialSpecs: undefined,
                    baseUnitLabel: undefined,
                    quantity: undefined,
                  });
                  return;
                }
                const rowData = {
                  ...materialDetail,
                  materialId: {
                    materialId,
                    materialName: viewMaterialName(materialDetail),
                  },
                };
                $table.setRow(row, rowData);
              },
              pictureFileId: row.pictureFileId,
              editable: row.isEditable ?? true,
            });
          },
        },
        title: '原料',
        minWidth: 180,
      },
      {
        field: 'materialSpecs',
        title: '规格型号',
        minWidth: 180,
        showOverflow: false,
      },
      {
        field: 'baseUnitLabel',
        title: '基本单位',
        width: 110,
      },
      {
        slots: {
          default: 'quantity',
        },
        field: 'quantity',
        title: '数量',
        width: 180,
      },
      {
        slots: {
          default: 'difference',
        },
        field: 'difference',
        title: 'BOM差异',
        width: 110,
      },
      {
        align: 'center',
        slots: {
          default: 'CellOperation',
        },
        title: '操作',
        width: 60,
        fixed: 'right',
      },
    ],
    editRules: {
      materialId: [
        { message: '物料不能为空', required: true, trigger: 'blur' },
      ],
      quantity: [{ message: '数量不能为空', required: true, trigger: 'blur' }],
    },
    minHeight: 150,
  };
};

export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '物料',
      field: 'materialName',
      minWidth: 180,
    },
    {
      title: 'BOM配置数量',
      field: 'usageQuantity',
      minWidth: 120,
    },
    {
      title: '当前明细数量',
      field: 'quantity',
      minWidth: 120,
    },
    {
      slots: {
        default: 'difference',
      },
      title: '差异',
      field: 'difference',
      minWidth: 100,
    },
  ];
}
