<script setup lang="ts">
import type { InventoryQueryApi } from '#/api/warehouse-management';

import { ref } from 'vue';

import { getInventoryDetailChangeList } from '#/api/warehouse-management';
import TipsPopover from '#/components/tips-popover/index.vue';
import { add } from '#/utils/numberUtils';

const props = defineProps({
  /** 单据ID */
  docId: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
});

const loading = ref(true);

const oldInventory = ref(0);
const newInventory = ref(0);

const inventoryChangeList = ref<InventoryQueryApi.InventoryDetailChangeItem[]>(
  [],
);

const getInventoryDetailChangeListHandle = async () => {
  try {
    loading.value = true;
    const res = await getInventoryDetailChangeList({
      materialIdList: props.materialId,
      warehouseIdList: props.warehouseId,
      docIdList: props.docId,
    });
    inventoryChangeList.value = res;
    loading.value = false;
    return res;
  } catch {
    loading.value = false;
    return null;
  }
};

const showHandle = async () => {
  if (!loading.value) {
    return;
  }

  const res = await getInventoryDetailChangeListHandle();
  if (res) {
    for (const item of res) {
      oldInventory.value = add(oldInventory.value, item.oldInventory);
      newInventory.value = add(newInventory.value, item.newInventory);
    }
  }
};
</script>

<template>
  <div>
    <TipsPopover placement="top" width="250px" @show="showHandle">
      <div class="inventory-info w-full" v-loading="loading">
        <table>
          <thead>
            <tr>
              <th></th>
              <th>入库前</th>
              <th>入库后</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>库存量</td>
              <td>{{ oldInventory }}</td>
              <td>
                {{ newInventory }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </TipsPopover>
  </div>
</template>
<style scoped>
.inventory-info {
  @apply rounded-lg bg-white p-2;
}

.inventory-info table {
  @apply w-full border-separate border-spacing-0;
}

.inventory-info th,
.inventory-info td {
  @apply p-2 text-center text-sm;
}

.inventory-info th {
  @apply bg-gray-50 font-medium text-gray-600;
}

.inventory-info td:first-child {
  @apply text-left font-medium text-gray-600;
}

.inventory-info tr:not(:last-child) td {
  @apply border-b border-gray-100;
}

:deep(.el-button.el-button--default) {
  @apply !text-base !font-medium;
}

:deep(.el-button--default.is-link) {
  @apply !text-primary-500 hover:!text-primary-600;
}

:deep(.el-button--danger.is-link) {
  @apply !text-red-500 hover:!text-red-600;
}
</style>
