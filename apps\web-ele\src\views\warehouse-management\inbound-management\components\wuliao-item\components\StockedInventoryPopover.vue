<script setup lang="ts">
import type { InventoryQueryApi } from '#/api/warehouse-management';

import { ref } from 'vue';

import { getInventoryChangeList } from '#/api/warehouse-management';
import TipsPopover from '#/components/tips-popover/index.vue';

const props = defineProps({
  /** 单据ID */
  docId: {
    type: String,
    default: '',
  },
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 仓库id */
  warehouseId: {
    type: String,
    default: '',
  },
});

const loading = ref(true);

const inventoryChangeList = ref<InventoryQueryApi.InventoryChangeItem[]>([]);

const getInventoryChangeListHandle = async () => {
  try {
    loading.value = true;
    const res = await getInventoryChangeList({
      materialIdList: props.materialId,
      warehouseIdList: props.warehouseId,
      docIdList: props.docId,
    });
    inventoryChangeList.value = res;
    loading.value = false;
  } catch {
    loading.value = false;
    return null;
  }
};

const showHandle = async () => {
  await getInventoryChangeListHandle();
};

const hideHandle = () => {
  inventoryChangeList.value = [];
  loading.value = true;
};
</script>

<template>
  <div>
    <TipsPopover
      placement="top"
      width="250px"
      @show="showHandle"
      @hide="hideHandle"
    >
      <div class="inventory-info w-full" v-loading="loading">
        <table>
          <thead>
            <tr>
              <th></th>
              <th>入库前</th>
              <th>入库后</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>库存量</td>
              <td>{{ inventoryChangeList[0]?.oldInventory }}</td>
              <td>
                {{ inventoryChangeList[0]?.newInventory }}
              </td>
            </tr>
            <tr>
              <td>可用量</td>
              <td>{{ inventoryChangeList[0]?.oldAvailableInventory }}</td>
              <td>
                {{ inventoryChangeList[0]?.newAvailableInventory }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </TipsPopover>
  </div>
</template>
<style scoped>
.inventory-info {
  @apply rounded-lg bg-white p-2;
}

.inventory-info table {
  @apply w-full border-separate border-spacing-0;
}

.inventory-info th,
.inventory-info td {
  @apply p-2 text-center text-sm;
}

.inventory-info th {
  @apply bg-gray-50 font-medium text-gray-600;
}

.inventory-info td:first-child {
  @apply text-left font-medium text-gray-600;
}

.inventory-info tr:not(:last-child) td {
  @apply border-b border-gray-100;
}

:deep(.el-button.el-button--default) {
  @apply !text-base !font-medium;
}

:deep(.el-button--default.is-link) {
  @apply !text-primary-500 hover:!text-primary-600;
}

:deep(.el-button--danger.is-link) {
  @apply !text-red-500 hover:!text-red-600;
}
</style>
