import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { dictItemListType, WarehouseInfoApi } from '#/api';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElBadge, ElInputTag } from 'element-plus';

import {
  getDictItemList,
  getDocStatusInfo,
  getEnableWarehouseList,
  getEnumByName,
  getMaterialCategoryTree,
} from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'transferDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const transferTypeList = data.map((item: any) => ({
            label: item.enumLabel,
            value: item.enumValue,
          }));
          return transferTypeList;
        },
        api: () => {
          return getEnumByName('WmTransferTypeEnums');
        },
        maxCollapseTags: 1,
        filterable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docCode',
      label: '调拨类型',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.EnableWarehouse[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'oldWarehouseIdList',
      formItemClass: 'col-span-1',
      label: '调出仓库',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getEnableWarehouseList();
        },
      },
      fieldName: 'targetWarehouseIdList',
      formItemClass: 'col-span-1',
      label: '调入仓库',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Select',
      componentProps: {
        multiple: true,
        maxCollapseTags: 2,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择调拨类型',
      },
      dependencies: {
        async componentProps(values) {
          values.docStatusList = [];
          if (values.docCode) {
            const res = await getDocStatusInfo(values.docCode);
            const optionsList = res?.map((item) => ({
              label: item.docStatusLabel,
              value: item.docStatusKey,
            }));
            const options = optionsList.filter(
              (item) => item.value !== 'pending',
            );
            return {
              options,
              placeholder: '请选择',
            };
          } else {
            return {
              disabled: true,
              placeholder: '请选择调拨类型',
            };
          }
        },
        triggerFields: ['docCode'],
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'outFinishTime',
      label: '出库时间',
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'inFinishTime',
      label: '入库时间',
      formItemClass: 'col-span-2',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'materialName',
      label: '物料名',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialAttributeList',
      label: '物料属性',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'materialTypeList',
      label: '物料大类',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(data: any) {
            return {
              label: data.categoryName,
              value: data.categoryCode,
              children: data.children
                ? data.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((data: any) => convertDeptData(data));
          return convertedData;
        },
        api: () => {
          return getMaterialCategoryTree();
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 1,
        multiple: true,
        showCheckbox: true,
      },
      defaultValue: [],
      fieldName: 'materialCategoryList',
      label: '物料细类',
    },
    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          {
            label: '是',
            value: true,
          },
          {
            label: '否',
            value: false,
          },
        ],
      },
      fieldName: 'isStandard',
      label: '是否标准物料',
      labelWidth: 100,
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      title: '单据编号',
      field: 'transferDocNumber',
      minWidth: 235,
    },
    {
      title: '调拨类型',
      field: 'docCodeLabel',
      minWidth: 110,
    },
    {
      slots: {
        default: 'docStatusLabel',
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      title: '申请人',
      field: 'submitUserName',
      minWidth: 150,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 150,
    },
    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[50px] max-w-[110px]',
          }),
      },
      title: '物料图片',
      field: 'pictureFileId',
      minWidth: 150,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '非标',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: row.isStandard,
              class: 'item',
            },
            {
              default: () => row.materialCode,
            },
          );
        },
      },
      title: '物料编号',
      field: 'materialCode',
      minWidth: 180,
    },
    {
      title: '物料名称',
      field: 'materialName',
      minWidth: 150,
    },
    {
      title: '具体规格',
      field: 'materialSpecs',
      minWidth: 250,
    },
    {
      title: '物料属性',
      field: 'materialAttributeLabel',
      minWidth: 150,
    },
    {
      title: '物料大类',
      field: 'materialTypeLabel',
      minWidth: 150,
    },
    {
      title: '物料细类',
      field: 'materialCategoryName',
      minWidth: 150,
    },

    {
      title: '调出仓库',
      field: 'oldWarehouseName',
      minWidth: 150,
    },
    {
      title: '出库执行人',
      field: 'outExecutorUserName',
      minWidth: 150,
    },
    {
      title: '出库时间',
      field: 'outFinishTime',
      minWidth: 150,
    },
    {
      title: '调入仓库',
      field: 'targetWarehouseName',
      minWidth: 150,
    },
    {
      title: '入库执行人',
      field: 'inExecutorUserName',
      minWidth: 150,
    },
    {
      title: '入库时间',
      field: 'inFinishTime',
      minWidth: 150,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 185,
      title: '操作',
    },
  ];
}
