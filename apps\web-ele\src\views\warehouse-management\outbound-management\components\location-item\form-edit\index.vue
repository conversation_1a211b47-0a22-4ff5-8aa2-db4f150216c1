<script setup lang="ts">
import type { LocationInfoApi } from '#/api/warehouse-management/index';

import { onMounted } from 'vue';

import { useVbenForm } from '@girant/adapter';

import { getLocationList } from '#/api/warehouse-management/index';

import { useFormSchema } from './data';

const props = defineProps<{
  targetWarehouse: {
    locationId?: string;
    warehouseCode: string;
    warehouseId: string;
    warehouseName: string;
  };
}>();

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 60 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

onMounted(async () => {
  formApi.setValues(props.targetWarehouse);

  const locationListRes = await getLocationList({
    warehouseId: props.targetWarehouse.warehouseId,
    isLock: false,
    isPrepMaterial: true,
    isEnable: true,
  });

  const locationOptions = locationListRes.map(
    (item: LocationInfoApi.LocationDetail) => ({
      label: item.locationName,
      value: item.locationId,
    }),
  );

  formApi.updateSchema([
    {
      componentProps: {
        options: locationOptions,
      },
      fieldName: 'locationId',
    },
  ]);
});

// 获取表单数据
const getFormData = async () => {
  try {
    const validateRes = await formApi.validate();
    if (!validateRes.valid) {
      throw new Error('请检查备料目标库位表单');
    }
    const formValues = await formApi.getValues();
    return formValues;
  } catch {
    throw new Error('请检查备料目标库位表单');
  }
};

defineExpose({
  formApi,
  getFormData,
});
</script>

<template>
  <Form />
</template>
