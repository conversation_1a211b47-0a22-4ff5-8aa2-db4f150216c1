import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getWarehouseList } from '#/api/warehouse-management';

// 查询参数类型
export interface SearchParams {
  transferDocNumberList: string;
  warehouseIdList: string;
  modifyTime: string;
}

// 表格数据类型
export interface RowType {
  transferDocId: string;
  transferDocNumber: string;
  warehouseName: string;
  warehouseId: string;
  modifyTime: string;
  docStatusLabel: string;
  docStatus: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'transferDocNumberList',
      label: '单据编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '所属仓库',
    },

    {
      component: 'Input',
      fieldName: 'modifyTime',
      formItemClass: 'col-span-2',
      label: '修改时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      field: 'transferDocNumber',
      title: '单据编号',
      minWidth: 200,
    },
    {
      field: 'transferDocId',
      title: '入库单ID',
      visible: false,
    },

    {
      field: 'warehouseName',
      width: 180,
      title: '所属仓库',
    },

    {
      field: 'modifyTime',
      width: 180,
      title: '修改时间',
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: row.docStatus === 'reject' ? 'danger' : 'warning',
            },
            { default: () => row.docStatusLabel },
          );
        },
      },
      field: 'docStatusLabel',
      width: 150,
      title: '单据状态',
    },

    {
      field: 'docStatus',
      title: '单据状态值',
      visible: false,
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'transferDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
          {
            code: 'del',
            label: '删除单据',
            type: 'danger',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 150,
    },
  ];
}
