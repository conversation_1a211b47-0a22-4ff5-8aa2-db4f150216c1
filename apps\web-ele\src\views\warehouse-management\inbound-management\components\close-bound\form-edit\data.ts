import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'inBoundDocNumber',
      label: '入库单号',
    },
    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'origDocTypeName',
      label: '入库类型',
    },

    {
      component: 'Input',
      fieldName: 'applyUserName',
      label: '申请人',
    },

    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      label: '取消原因',
      formItemClass: 'col-span-full',
      rules: 'selectRequired',
    },

    {
      component: 'Textarea',
      componentProps: {
        autosize: { minRows: 3 },
        maxlength: 500,
        placeholder: '请输入',
        showWordLimit: true,
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full',
      label: '取消原因说明',
    },

    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full',
      label: '附件',
    },
  ];
}
