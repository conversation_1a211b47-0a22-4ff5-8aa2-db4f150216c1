<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage } from 'element-plus';

import { getTaskPredictionList } from '#/api';

import IconFont from '../IconFont/IconFont.vue';

const props = defineProps<{
  processInstanceId: string;
}>();

// 定义时间轴项目接口
interface TimelineItem {
  nodeName: string;
  handleResult?: string;
  handleResultLabel?: string;
  handleType: string;
  handleTypeLabel: string;
  taskEndTime?: string;
  branches?: { nodeKey: string; nodeName: string }[];
  assigneeName?: string;
  assigneeDeptName?: string;
  handlAssigneeList?: { id: number; name: string; type: string }[];
  comment?: string;
  [key: string]: any;
}

// 时间轴数据
const timelineData = ref<TimelineItem[]>([]);
const loading = ref(false);

// 定义常量
const STATUS_BASE_CLASS = 'rounded-full px-3 py-1 text-xs font-medium';
const CIRCLE_BASE_CLASS =
  'z-10 flex h-6 w-6 items-center justify-center rounded-full border-4';
const INNER_CIRCLE_BASE_CLASS = 'h-3 w-3 rounded-full';
const LINE_BASE_CLASS = 'absolute left-1/2 h-40 w-0.5 -translate-x-1/2';

const HANDLE_RESULT_APPROVED = '01';
const HANDLE_RESULT_REJECTED = '02';
const HANDLE_RESULT_PENDING = '03';
const HANDLE_TYPE_START = '00';
const HANDLE_TYPE_PROCESSING = '10';
const HANDLE_TYPE_COMPLETED = '20';

const loadData = async (processInstanceId: string) => {
  try {
    loading.value = true;
    const data = await getTaskPredictionList(processInstanceId);
    timelineData.value = data;
  } catch (error) {
    console.error('加载审核记录数据失败:', error);
    ElMessage.error('加载审核记录数据失败');
  } finally {
    loading.value = false;
  }
};

if (props.processInstanceId) {
  loadData(props.processInstanceId);
}

const hasBranches = (item: TimelineItem) =>
  item.branches && item.branches.length > 0;

const getStatusClass = (item: TimelineItem) => {
  if (item.handleResult) {
    return {
      [STATUS_BASE_CLASS]: true,
      'bg-green-100 text-green-800':
        item.handleResult === HANDLE_RESULT_APPROVED,
      'bg-red-100 text-red-800': item.handleResult === HANDLE_RESULT_REJECTED,
      'bg-yellow-100 text-yellow-800':
        item.handleResult === HANDLE_RESULT_PENDING,
    };
  }
  return {
    [STATUS_BASE_CLASS]: true,
    'bg-yellow-100 text-yellow-800': item.handleType === HANDLE_TYPE_PROCESSING,
    'bg-gray-100 text-gray-800': item.handleType === HANDLE_TYPE_COMPLETED,
  };
};

const getCircleClass = (item: TimelineItem) => {
  return {
    [CIRCLE_BASE_CLASS]: true,
    'border-green-500 bg-green-100': item.handleType === HANDLE_TYPE_START,
    'border-yellow-500 bg-yellow-100':
      item.handleType === HANDLE_TYPE_PROCESSING,
    'border-gray-300 bg-gray-100':
      item.handleType === HANDLE_TYPE_COMPLETED || hasBranches(item),
  };
};

const getInnerCircleClass = (item: TimelineItem) => {
  return {
    [INNER_CIRCLE_BASE_CLASS]: true,
    'bg-green-500': item.handleType === HANDLE_TYPE_START,
    'bg-yellow-500': item.handleType === HANDLE_TYPE_PROCESSING,
    'bg-gray-400':
      item.handleType === HANDLE_TYPE_COMPLETED || hasBranches(item),
  };
};

const getLineClass = (item: TimelineItem) => {
  return {
    [LINE_BASE_CLASS]: true,
    'bg-green-500': item.handleType === HANDLE_TYPE_START,
    'bg-gray-300': item.handleType !== HANDLE_TYPE_START,
  };
};
</script>

<template>
  <div v-loading="loading" class="approval-timeline container mx-auto p-4">
    <div class="rounded-lg bg-white">
      <div class="timeline" v-if="timelineData.length > 0">
        <div v-for="(item, index) in timelineData" :key="index">
          <div class="timeline-item mb-8 flex items-start">
            <!-- 左侧：状态和时间 -->
            <div class="w-1/4 pr-4 text-right">
              <div class="mb-1 flex items-center justify-end">
                <span
                  v-if="hasBranches(item)"
                  :class="`${STATUS_BASE_CLASS} bg-gray-100 text-gray-800`"
                >
                  未处理
                </span>
                <span
                  v-else-if="item.handleResult"
                  :class="getStatusClass(item)"
                >
                  {{ item.handleResultLabel }}
                </span>
                <span v-else :class="getStatusClass(item)">
                  {{ item.handleTypeLabel }}
                </span>
              </div>
              <div v-if="!hasBranches(item)" class="text-sm text-gray-500">
                {{ item.taskEndTime }}
              </div>
            </div>

            <!-- 中间：时间轴线 -->
            <div class="relative flex items-start">
              <div :class="getCircleClass(item)">
                <div :class="getInnerCircleClass(item)"></div>
              </div>
              <!-- 时间轴连接线 - 最后一个节点不显示 -->
              <div
                v-if="index < timelineData.length - 1"
                :class="getLineClass(item)"
              ></div>
            </div>

            <!-- 右侧：审批详情 -->
            <div class="h-32 w-3/4 pl-8">
              <div v-if="hasBranches(item)">
                <h3
                  v-for="branch in item.branches"
                  :key="branch.nodeKey"
                  class="text-lg font-bold text-gray-800"
                >
                  {{ branch.nodeName }}
                </h3>
              </div>
              <div v-else>
                <div class="">
                  <div class="mb-2 flex items-start justify-between">
                    <h3 class="text-lg font-bold text-gray-800">
                      {{ item.nodeName }}
                    </h3>
                  </div>
                  <div class="mt-2 text-sm text-gray-600">
                    <div
                      class="bg-primary-50 mb-1 flex w-80 items-center rounded-full p-2 text-center"
                      v-if="item.assigneeName"
                    >
                      <IconFont
                        class="mt-0.5 px-2 !text-gray-500"
                        :size="20"
                        name="tabbar_personal"
                      />
                      <span>【{{ item.assigneeDeptName }}】</span>
                      <span> {{ item.assigneeName }}</span>
                    </div>
                    <div
                      class="bg-primary-50 mb-1 flex w-80 items-center rounded-full p-2 text-center"
                      v-if="
                        item.handlAssigneeList &&
                        item.handlAssigneeList.length > 0
                      "
                    >
                      <div
                        class="flex items-center"
                        v-for="handlAssignee in item.handlAssigneeList"
                        :key="handlAssignee.id"
                      >
                        <IconFont
                          v-if="handlAssignee.type === 'STAFF'"
                          class="mt-1 px-2 !text-gray-500"
                          :size="20"
                          name="tabbar_personal"
                        />
                        <span> {{ handlAssignee.name }}</span>
                      </div>
                    </div>
                    <div
                      class="bg-primary-50 mb-1 mt-2 flex items-start rounded-md p-2"
                      v-if="item.comment"
                    >
                      <span>{{ item.comment }}</span>
                    </div>
                    <!-- <div class="flex items-center" v-if="item.copyTo">
                      <span class="w-24 text-gray-500">抄送对象：</span>
                      <span>{{ item.copyTo.join('、') }}</span>
                    </div> -->
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div v-else>
        <el-empty description="暂无流程信息" />
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes fade-in-up {
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes line-extend {
  from {
    height: 0;
  }

  to {
    height: 100%;
  }
}

.timeline-item {
  opacity: 0;
  transform: translateY(20px);
  animation: fade-in-up 0.6s ease forwards;
}

/* 为每个时间轴项设置延迟动画 */
.timeline-item:nth-child(1) {
  animation-delay: 0.1s;
}

.timeline-item:nth-child(2) {
  animation-delay: 0.2s;
}

.timeline-item:nth-child(3) {
  animation-delay: 0.3s;
}

.timeline-item:nth-child(4) {
  animation-delay: 0.4s;
}

.timeline-item:nth-child(5) {
  animation-delay: 0.5s;
}

.timeline-item:nth-child(6) {
  animation-delay: 0.6s;
}

.timeline-item:nth-child(7) {
  animation-delay: 0.7s;
}

.line-animation {
  animation: line-extend 0.6s ease forwards;
  animation-delay: calc(0.1s * var(--item-index));
}

/* 动画效果 */
</style>
