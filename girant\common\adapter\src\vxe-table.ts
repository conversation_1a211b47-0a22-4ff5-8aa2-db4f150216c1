import type { Recordable } from '@vben/types';

import { h } from 'vue';

import { IconifyIcon } from '@vben/icons';
import { $te } from '@vben/locales';
import { setupVbenVxeTable, useVbenVxeGrid } from '@vben/plugins/vxe-table';
import { get, isFunction, isString } from '@vben/utils';

import { $t } from '@girant/locales';
import { objectOmit } from '@vueuse/core';
import {
  ElButton,
  ElCascader,
  ElDatePicker,
  ElImage,
  ElInput,
  ElInputNumber,
  ElPopconfirm,
  ElSelectV2,
  ElSwitch,
  ElTag,
  ElTreeSelect,
} from 'element-plus';

import { useVbenForm } from './form';

import 'element-plus/dist/index.css';

setupVbenVxeTable({
  configVxeTable: (vxeUI) => {
    vxeUI.setConfig({
      grid: {
        align: 'center',
        border: false,
        columnConfig: {
          resizable: true,
        },
        formConfig: {
          enabled: false,
        },
        minHeight: 180,
        proxyConfig: {
          autoLoad: true,
          response: {
            result: 'items',
            total: 'total',
            list: 'items',
          },
          showActiveMsg: true,
          showResponseMsg: false,
        },
        round: true,
        showOverflow: true,
        size: 'small',
      },
    });

    vxeUI.renderer.forEach((_item, key) => {
      if (key.startsWith('Cell')) {
        vxeUI.renderer.delete(key);
      }
    });

    vxeUI.renderer.add('CellSequence', {
      renderTableDefault(_renderOpts, params) {
        const { $table, row, $grid } = params;
        const currentPage = $grid?.getProxyInfo()?.pager.currentPage || 1;
        const pageSize = $grid?.getProxyInfo()?.pager.pageSize || 20;
        const index = Number($table.getRowSeq(row));
        const sequence = (currentPage - 1) * pageSize + index;

        return h('span', String(sequence));
      },
    });

    vxeUI.renderer.add('CellImage', {
      renderTableDefault(_renderOpts, params) {
        const { column, row } = params;
        const src = row[column.field];
        return h(ElImage, { previewSrcList: [src], src });
      },
    });

    vxeUI.renderer.add('CellLink', {
      renderTableDefault(renderOpts) {
        const { props } = renderOpts;
        return h(
          ElButton,
          { link: true, size: 'small' },
          { default: () => props?.text },
        );
      },
    });

    vxeUI.renderer.add('CellInput', {
      renderTableEdit({ attrs, props }, { $table, column, row }) {
        return h(ElInput, {
          ...props,
          ...attrs,
          modelValue: row[column.field],
          onChange: async () =>
            attrs?.onChange?.({
              $table,
              row,
            }),
          'onUpdate:modelValue': async (val: string) => {
            row[column.field] = val;
          },
        });
      },
    });

    vxeUI.renderer.add('CellInputNumber', {
      renderTableEdit({ attrs, props }, { $table, column, row }) {
        // 获取列的宽度
        const columnWidth = column.renderWidth;
        return h(ElInputNumber, {
          ...props,
          ...attrs,
          modelValue: row[column.field],
          onChange: async () =>
            attrs?.onChange?.({
              $table,
              row,
            }),
          'onUpdate:modelValue': async (val: string) => {
            row[column.field] = val;
          },
          style: { width: `${columnWidth - 20}px` },
        });
      },
    });

    vxeUI.renderer.add('CellDatePicker', {
      renderTableEdit({ attrs, props }, { $table, column, row }) {
        // 获取列的宽度
        const columnWidth = column.renderWidth;
        return h(ElDatePicker, {
          ...props,
          ...attrs,
          modelValue: row[column.field],
          onChange: async () =>
            attrs?.onChange?.({
              $table,
              row,
            }),
          'onUpdate:modelValue': async (val: string) => {
            row[column.field] = val;
          },
          style: { width: `${columnWidth - 20}px` },
        });
      },
    });

    vxeUI.renderer.add('CellSelect', {
      renderTableEdit({ attrs, props }, { $table, column, row }) {
        return h(ElSelectV2, {
          ...props,
          ...attrs,
          modelValue: row[column.field],
          onChange: async () =>
            attrs?.onChange?.({
              $table,
              row,
            }),
          'onUpdate:modelValue': async (val: string) => {
            row[column.field] = val;
          },
        });
      },
    });

    vxeUI.renderer.add('CellCascader', {
      renderTableEdit({ attrs, props }, { $table, column, row }) {
        return h(ElCascader, {
          ...props,
          ...attrs,
          modelValue: row[column.field],
          onChange: async () =>
            attrs?.onChange?.({
              $table,
              row,
            }),
          'onUpdate:modelValue': (value: any) => {
            row[column.field] = value;
          },
        });
      },
    });

    vxeUI.renderer.add('CellTreeSelect', {
      renderTableEdit({ attrs, props }, { column, row }) {
        return h(ElTreeSelect, {
          ...props,
          ...attrs,
          modelValue: row[column.field],
          'onUpdate:modelValue': (value: any) => {
            row[column.field] = value;
          },
        });
      },
    });

    vxeUI.renderer.add('CellTag', {
      renderTableDefault({ options, props }, { column, row }) {
        const value = get(row, column.field);
        const tagOptions = options ?? [
          { color: 'success', label: '已启用', value: true },
          { color: 'error', label: '已禁用', value: false },
        ];
        const tagItem = tagOptions.find((item) => item.value === value);
        return h(
          ElTag,
          {
            ...props,
            ...objectOmit(tagItem ?? {}, ['label']),
          },
          { default: () => tagItem?.label ?? value },
        );
      },
    });

    vxeUI.renderer.add('CellSwitch', {
      renderTableDefault({ attrs, props }, { $table, column, row }) {
        const loadingKey = `__loading_${column.field}`;
        const finallyProps = {
          ...props,
          activeText: attrs?.activeText ?? '已启用',
          activeValue: attrs?.activeValue ?? true,
          inactiveText: attrs?.inactiveText ?? '已禁用',
          inactiveValue: attrs?.inactiveValue ?? false,
          inlinePrompt: true,
          loading: row[loadingKey] ?? false,
          modelValue: row[column.field],
          onChange,
        };
        async function onChange(newVal: any) {
          row[loadingKey] = true;
          try {
            const result = await attrs?.beforeChange?.(newVal, row);
            if (result !== false) {
              row[column.field] = newVal;
            }
            await attrs?.onChange?.({
              $table,
              row,
            });
          } finally {
            row[loadingKey] = false;
          }
        }
        return h(ElSwitch, finallyProps);
      },
      renderTableEdit({ attrs, props }, { $table, column, row }) {
        const loadingKey = `__loading_${column.field}`;
        const finallyProps = {
          ...props,
          activeText: attrs?.activeText ?? '已启用',
          activeValue: attrs?.activeValue ?? true,
          inactiveText: attrs?.inactiveText ?? '已禁用',
          inactiveValue: attrs?.inactiveValue ?? false,
          inlinePrompt: true,
          loading: row[loadingKey] ?? false,
          modelValue: row[column.field],
          onChange,
        };
        async function onChange(newVal: any) {
          row[loadingKey] = true;
          try {
            const result = await attrs?.beforeChange?.(newVal, row);
            if (result !== false) {
              row[column.field] = newVal;
            }
            await attrs?.onChange?.({
              $table,
              row,
            });
          } finally {
            row[loadingKey] = false;
          }
        }
        return h(ElSwitch, finallyProps);
      },
    });

    vxeUI.renderer.add('CellOperation', {
      renderTableDefault({ attrs, options, props }, { column, row }) {
        const defaultProps = { link: true, size: 'small', ...props };
        let align = 'end';
        switch (column.align) {
          case 'center': {
            align = 'center';
            break;
          }
          case 'left': {
            align = 'start';
            break;
          }
        }

        const presets: Recordable<Recordable<any>> = {
          view: {
            label: '查看',
            type: 'info',
          },
          cancel: {
            label: '取消',
            type: 'primary',
          },
          copy: {
            label: '复制',
            type: 'primary',
          },
          delete: {
            label: '删除',
            type: 'danger',
          },
          edit: {
            label: '修改',
            type: 'primary',
          },
          revert: {
            label: '恢复',
            type: 'primary',
          },
        };

        const operations: Array<Recordable<any>> = (
          options || ['edit', 'delete']
        )
          .map((opt) => {
            if (isString(opt)) {
              return presets[opt]
                ? { code: opt, ...presets[opt], ...defaultProps }
                : {
                    code: opt,
                    label: $te(`common.${opt}`) ? $t(`common.${opt}`) : opt,
                    ...defaultProps,
                  };
            } else {
              return { ...defaultProps, ...presets[opt.code], ...opt };
            }
          })
          .map((opt) => {
            const optBtn: Recordable<any> = {};
            Object.keys(opt).forEach((key) => {
              optBtn[key] = isFunction(opt[key]) ? opt[key](row) : opt[key];
            });
            return optBtn;
          })
          .filter((opt) => opt.show !== false);

        function renderBaseButton(opt: Recordable<any>, listen = true) {
          return h(
            ElButton,
            {
              ...props,
              ...opt,
              icon: undefined,
              onClick: listen
                ? () =>
                    attrs?.onClick?.({
                      code: opt.code,
                      row,
                    })
                : undefined,
            },
            {
              default: () => {
                const content = [];
                if (opt.icon) {
                  content.push(
                    h(IconifyIcon, { class: 'size-5', icon: opt.icon }),
                  );
                }
                content.push(opt.label || '操作');
                return content;
              },
            },
          );
        }

        function renderConfirm(opt: Recordable<any>) {
          let viewportWrapper: HTMLElement | null = null;
          return h(
            ElPopconfirm,
            {
              /**
               * 当popconfirm用在固定列中时，将固定列作为弹窗的容器时可能会因为固定列较窄而无法容纳弹窗
               * 将表格主体区域作为弹窗容器时又会因为固定列的层级较高而遮挡弹窗
               * 将body或者表格视口区域作为弹窗容器时又会导致弹窗无法跟随表格滚动。
               * 鉴于以上各种情况，一种折中的解决方案是弹出层展示时，禁止操作表格的滚动条。
               * 这样既解决了弹窗的遮挡问题，又不至于让弹窗随着表格的滚动而跑出视口区域。
               */
              getPopupContainer(el: any) {
                viewportWrapper = el.closest('.vxe-table--viewport-wrapper');
                return document.body;
              },
              placement: 'top-start',
              title: `确定删除${attrs?.nameTitle || '该行？'}`,
              ...props,
              ...opt,
              icon: undefined,
              onConfirm: () => {
                attrs?.onClick?.({
                  code: opt.code,
                  row,
                });
              },
              onOpenChange: (open: boolean) => {
                // 当弹窗打开时，禁止表格的滚动
                if (open) {
                  viewportWrapper?.style.setProperty('pointer-events', 'none');
                } else {
                  viewportWrapper?.style.removeProperty('pointer-events');
                }
              },
            },
            {
              content: () =>
                h(
                  'div',
                  { class: 'truncate' },
                  `确定删除${row[attrs?.nameField || '']}吗？`,
                ),
              reference: () => renderBaseButton(opt, false),
            },
          );
        }

        const btns = operations.map((opt) =>
          opt.code === 'delete' ? renderConfirm(opt) : renderBaseButton(opt),
        );

        return h(
          'div',
          {
            class: 'flex table-operations',
            style: { justifyContent: align },
          },
          btns,
        );
      },
    });
  },
  useVbenForm,
});

export { useVbenVxeGrid };
export type OnActionClickParams<T = Recordable<any>> = {
  code: string;
  row: T;
};
export type OnActionClickFn<T = Recordable<any>> = (
  params: OnActionClickParams<T>,
) => void;
export type * from '@vben/plugins/vxe-table';
