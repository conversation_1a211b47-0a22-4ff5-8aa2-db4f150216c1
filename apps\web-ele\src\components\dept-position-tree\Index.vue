<script setup lang="ts">
import type { ExpandTrigger } from 'element-plus';

import type { PropType } from 'vue';

import { computed, defineEmits, defineProps, onMounted, ref } from 'vue';

import { ElCascader, ElTag } from 'element-plus';

import { getAllDeptTree } from '#/api/components';

// 定义级联选择器选项接口
type CascaderOption = {
  [key: string]: any;
  children: CascaderOption[];
  disabled?: boolean;
  label: string;
  leaf?: boolean;
  value: number | string;
};

// 定义组件属性
const props = defineProps({
  /** 绑定的值，可以是数组、数字或字符串*/
  value: {
    type: [Array, Number, String] as PropType<
      Array<number | string> | number | string
    >,
    default: () => [],
  },
  /** 级联选择器props属性 */
  props: {
    type: Object,
    default: () => ({}),
  },
  /** 部门是否启用 */
  isEnable: {
    default: true,
    type: Boolean as PropType<Boolean | null>,
  },
  /** 是否加载岗位 */
  loadPosition: {
    default: false,
    type: Boolean,
  },
  /** 是否显示无岗位的部门 */
  showNoPositionDept: {
    default: false,
    type: Boolean,
  },
  /** 是否显示部门禁用标签 */
  showDisableTag: {
    default: true,
    type: Boolean,
  },
  /** 当部门禁用时,是否能选择 */
  disabledWhenDeptDisabled: {
    default: false,
    type: Boolean,
  },
});

const emit = defineEmits(['update:value', 'loadSuccess', 'loadFail']);
/** 组件*/
const cascaderRef = ref<InstanceType<typeof ElCascader>>();
/** 渲染数据 */
const deptTree = ref<CascaderOption[]>([]);

const loading = ref(false);

const selectedValue = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit('update:value', value);
  },
});

const handleChange = (value: any) => {
  selectedValue.value = value;
};

const cascaderProps = computed(() => ({
  expandTrigger: 'click' as ExpandTrigger,
  multiple: false,
  value: 'value',
  label: 'label',
  emitPath: false,
  checkStrictly: true,
  showCheckedStrategy: 'leaf',
  ...props.props,
}));

/** 查询部门树 */
const getDeptTree = async () => {
  try {
    loading.value = true;
    const data = await getAllDeptTree({
      isEnable: props.isEnable,
      isQueryPosition: props.loadPosition,
    });
    // 处理数据
    const convertDeptData = (dept: any) => {
      if (
        props.loadPosition &&
        !props.showNoPositionDept &&
        !dept.positionList?.length
      ) {
        return null;
      }
      return {
        label: `${dept.deptName}`,
        value: dept.deptId,
        isEnable: dept.isEnable,
        disabled: props.disabledWhenDeptDisabled ? false : !dept.isEnable,
        children: dept.children
          ? [
              ...dept.children
                .map((child: any) => convertDeptData(child))
                .filter(Boolean),
              ...(dept.positionList
                ? dept.positionList.map((position: any) => ({
                    label: position.positionName,
                    value: `${position.positionId}p`,
                    type: 'position',
                  }))
                : []),
            ]
          : [],
      };
    };
    deptTree.value = data
      .map((dept: any) => convertDeptData(dept))
      .filter(Boolean) as CascaderOption[];
  } catch (error) {
    console.error('获取部门树失败', error);
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  getDeptTree();
});
defineExpose({
  cascaderRef,
});
</script>

<template>
  <ElCascader
    v-model="selectedValue"
    :options="deptTree"
    :props="cascaderProps"
    @change="handleChange"
    ref="cascaderRef"
    :loading="loading"
    v-bind="$attrs"
    filterable
    clearable
    collapse-tags
    collapse-tags-tooltip
    :show-all-levels="false"
    :max-collapse-tags="2"
  >
    <template #default="{ data }">
      <div class="flex items-center">
        <span>{{ data.label }}</span>
        <ElTag
          class="ml-2"
          v-if="showDisableTag && data.isEnable === false"
          type="danger"
          size="small"
        >
          已禁用
        </ElTag>
      </div>
    </template>
  </ElCascader>
</template>
