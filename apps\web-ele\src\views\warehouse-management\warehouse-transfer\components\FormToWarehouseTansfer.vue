<script setup lang="ts">
import type { TransferQueryApi } from '#/api/warehouse-management';

import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElMessage } from 'element-plus';

import {
  saveOrModTransferDoc,
  submitTransferDoc,
} from '#/api/warehouse-management';

const props = defineProps({
  isView: {
    type: Boolean,
    default: false,
  },
  transferDocId: {
    type: String,
    default: '',
  },
  transferDocNumber: {
    type: String,
    default: '',
  },
});

const isEdit = computed(() => {
  return (
    !props.isView &&
    (!isEmpty(props.transferDocId) || !isEmpty(props.transferDocNumber))
  );
});

const warehouseId = ref('');

const AdjustmentInformationRef = ref();
const AdjustmentDetailsRef = ref();

const AdjustmentInformation = computed(() => {
  return props.isView
    ? markRaw(defineAsyncComponent(() => import('./adjustment-info/View.vue')))
    : markRaw(defineAsyncComponent(() => import('./adjustment-info/Edit.vue')));
});

const AdjustmentDetails = computed(() => {
  return props.isView
    ? markRaw(
        defineAsyncComponent(() => import('./adjustment-details/View.vue')),
      )
    : markRaw(
        defineAsyncComponent(() => import('./adjustment-details/Edit.vue')),
      );
});

const handleChangeWarehouseId = (value: string) => {
  warehouseId.value = value;
};

/** 获取调拨信息数据 */
const getInfoData = async () => {
  const infoData = await AdjustmentInformationRef.value?.getFormData();
  if (!infoData) {
    throw new Error('请检查调拨信息');
  }
  return infoData;
};

/** 获取调拨详情数据 */
const getDetailData = async () => {
  const detailData = await AdjustmentDetailsRef.value?.getFormData();
  if (!detailData) {
    throw new Error('请检查调拨详情');
  }

  if (detailData?.length === 0) {
    throw new Error('调拨详情表单不能为空');
  }

  return detailData;
};

/** 获取提交数据 */
const getSubmitData = async () => {
  try {
    const infoData = await getInfoData();
    const detailData = await getDetailData();
    const submitData: TransferQueryApi.SubmitTransferDocParams = {
      ...infoData,
      transferDocId: props.transferDocId,
      transferItemList: detailData,
    };

    return submitData;
  } catch (error) {
    ElMessage.error((error as Error).message);
    return false;
  }
};

/** 提交调拨 */
const submitHandle = async () => {
  try {
    const submitData = await getSubmitData();
    if (!submitData) {
      return;
    }

    await submitTransferDoc(submitData);
    ElMessage.success('提交成功');
    return true;
  } catch {
    ElMessage.error('提交失败');
    return false;
  }
};

/** 暂存调拨 */
const saveHandle = async () => {
  try {
    const submitData = await getSubmitData();
    if (!submitData) {
      return;
    }

    await saveOrModTransferDoc(submitData);
    ElMessage.success('暂存成功');
    return true;
  } catch {
    ElMessage.error('暂存失败');
    return false;
  }
};

defineExpose({
  getSubmitData,
  saveHandle,
  submitHandle,
});
</script>

<template>
  <div class="relative mb-12 h-full">
    <AdjustmentInformation
      ref="AdjustmentInformationRef"
      :is-edit="isEdit"
      :transfer-doc-id="transferDocId"
      :transfer-doc-number="transferDocNumber"
      @change-warehouse-id="handleChangeWarehouseId"
    />

    <AdjustmentDetails
      ref="AdjustmentDetailsRef"
      :is-edit="isEdit"
      :warehouse-id="warehouseId"
      :transfer-doc-id="transferDocId"
      :transfer-doc-number="transferDocNumber"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <slot name="btn-group"></slot>
    </div>
  </div>
</template>
