<script setup lang="ts">
import type { MaterialPendingApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getPrepDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps<{
  prepDocId: string;
}>();

const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

/** 获取备料数据 */
const getPrepDocDetailHandle = async (prepDocId: string) => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId,
      isQueryItem: false,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
    return {} as MaterialPendingApi.PrepDocDetail;
  }
};

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

onMounted(async () => {
  if (props.prepDocId) {
    const data = await getPrepDocDetailHandle(props.prepDocId);
    if (data) {
      prepDocDetail.value = data;
      formApi.setValues(data);
    }
  } else {
    ElMessage.error('备料单据ID不能为空');
  }
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>备料单信息</span>
    </template>
    <template #default>
      <Form />
    </template>
  </FormCard>
</template>
