<script lang="ts" setup>
import { defineProps, ref } from 'vue';

// 定义 props
const props = defineProps<{
  timeSearchValueProp?: '01' | '02' | '03' | '04' | '05' | '06' | 'custom';
}>();

const emits = defineEmits(['timeChange']);

const timeSearchOptionMap = {
  '01': {
    executorTimePeriodType: '01',
    executorTimePeriodUnit: '20',
    executorTimeInterval: '',
  },
  '02': {
    executorTimePeriodType: '01',
    executorTimePeriodUnit: '23',
    executorTimeInterval: '',
  },
  '03': {
    executorTimePeriodType: '01',
    executorTimePeriodUnit: '30',
    executorTimeInterval: '',
  },
  '04': {
    executorTimePeriodType: '00',
    executorTimePeriodUnit: '20',
    executorTimeInterval: '-1',
  },
  '05': {
    executorTimePeriodType: '00',
    executorTimePeriodUnit: '23',
    executorTimeInterval: '-1',
  },
  '06': {
    executorTimePeriodType: '00',
    executorTimePeriodUnit: '25',
    executorTimeInterval: '-1',
  },
  '07': {
    executorTimePeriodType: '00',
    executorTimePeriodUnit: '30',
    executorTimeInterval: '-1',
  },
  custom: {
    executorTimePeriodType: '',
    executorTimePeriodUnit: '',
  },
};

const timeSearchOption = ref([
  {
    label: '本月',
    value: '01',
  },
  {
    label: '本季度',
    value: '02',
  },
  {
    label: '本年',
    value: '03',
  },
  {
    label: '近一个月',
    value: '04',
  },
  {
    label: '近一个季度',
    value: '05',
  },
  {
    label: '近半年',
    value: '06',
  },
  {
    label: '近一年',
    value: '07',
  },
  {
    label: '自定义',
    value: 'custom',
  },
]);
// 使用传入的参数，如果没有则使用默认值 '01'
const timeSearchValue = ref(props.timeSearchValueProp || '01');
const isCustomTime = ref(false);
const customTime = ref('');

const clearCustomTime = () => {
  customTime.value = '';
  timeSearchValue.value = props.timeSearchValueProp || '01';
  isCustomTime.value = false;

  const obj = {
    executorStartTime: '',
    executorEndTime: '',
    ...timeSearchOptionMap[timeSearchValue.value],
  };
  emits('timeChange', obj);
};

const commitCustomTime = () => {
  timeSearchValue.value = 'custom';

  const obj = {
    executorStartTime: customTime.value[0],
    executorEndTime: customTime.value[1],
    ...timeSearchOptionMap.custom,
  };
  emits('timeChange', obj);
};

const searchChange = (value: string) => {
  if (value === 'custom') {
    isCustomTime.value = true;
    return;
  }

  customTime.value = '';
  const obj = {
    executorStartTime: '',
    executorEndTime: '',
    ...timeSearchOptionMap[value as keyof typeof timeSearchOptionMap],
  };
  emits('timeChange', obj);
};

const getValues = () => {
  const obj = {
    executorStartTime: '',
    executorEndTime: '',
    ...timeSearchOptionMap[timeSearchValue.value],
  };
  return obj;
};

defineExpose({
  getValues,
});
</script>

<template>
  <div>
    <el-select
      size="small"
      v-model="timeSearchValue"
      placeholder="Select"
      @change="searchChange"
    >
      <el-option
        v-for="item in timeSearchOption"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
      <template #footer>
        <el-button
          v-if="!isCustomTime"
          text
          bg
          size="small"
          @click="isCustomTime = true"
        >
          选择自定义时间
        </el-button>
        <template v-else>
          <div class="mb-2">
            <el-date-picker
              v-model="customTime"
              type="datetimerange"
              range-separator="~"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              size="small"
              format="YYYY-MM-DD HH:mm"
              value-format="YYYY-MM-DD HH:mm"
            />
          </div>
          <el-button type="primary" size="small" @click="commitCustomTime">
            确定
          </el-button>
          <el-button size="small" @click="clearCustomTime"> 取消 </el-button>
        </template>
      </template>
    </el-select>
  </div>
</template>
