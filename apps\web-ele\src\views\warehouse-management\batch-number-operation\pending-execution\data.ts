import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { EnumType } from '#/api/common';
import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

import { getDocStatusInfo, getEnumByName } from '#/api';
import { getWarehouseList } from '#/api/warehouse-management';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

// 查询参数类型
export interface SearchParams {
  batchnumDocNumberList: string;
  warehouseIdList: string;
  submitUserList: string;
  docCode: string;
  submitStartTime: string;
  submitEndTime: string;
  docStatusList: string;
}

// 表格数据类型
export interface RowType {
  batchnumDocId: string;
  batchnumDocNumber: string;
  warehouseName: string;
  warehouseCode: string;
  warehouseId: string;
  submitUserName: string;
  submitTime: string;
  docStatusLabel: string;
  docStatus: string;
  docCode: string;
  docCodeLabel: string;
  materialId: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'batchnumDocNumberList',
      label: '单据编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        placeholder: '请选择处理类型',
        afterFetch: (data: EnumType[]) => {
          const handleTypeList = data.map((item) => ({
            label: item.enumLabel,
            value: item.enumValue,
          }));
          return handleTypeList;
        },
        api: () => {
          return getEnumByName('WmBatchNumHandleTypeEnums');
        },
      },
      fieldName: 'docCode',
      formItemClass: 'col-span-1',
      label: '处理类型',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '仓库',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'submitUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const workStatusTypeList = data.map((item: any) => ({
            label: item.docStatusLabel,
            value: item.docStatusKey,
          }));
          return workStatusTypeList;
        },
        api: () => {
          return getDocStatusInfo('WM0050');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: ['passed'],
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'hidden',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      field: 'batchnumDocNumber',
      title: '单据编号',
      minWidth: 200,
    },
    {
      field: 'batchnumDocId',
      title: '批次号处理单据ID',
      visible: false,
    },

    {
      field: 'docCodeLabel',
      width: 150,
      title: '处理类型',
    },
    {
      field: 'docCode',
      title: '处理类型值',
      visible: false,
    },
    {
      slots: {
        default: ({ row }) => {
          return h('span', null, { default: () => row.docStatusLabel });
        },
      },
      field: 'docStatusLabel',
      title: '单据状态',
      visible: false,
    },
    {
      field: 'docStatus',
      title: '单据状态值',
      visible: false,
    },

    {
      field: 'submitUserName',
      width: 180,
      title: '提交人',
    },

    {
      field: 'submitTime',
      width: 150,
      title: '提交时间',
    },

    {
      field: 'warehouseName',
      width: 200,
      title: '仓库',
    },
    {
      field: 'warehouseCode',
      title: '仓库编号',
      visible: false,
    },
    {
      field: 'warehouseId',
      title: '仓库ID',
      visible: false,
    },

    {
      slots: {
        default: 'operation',
      },
      align: 'center',
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 'auto',
      minWidth: 150,
    },
  ];
}
