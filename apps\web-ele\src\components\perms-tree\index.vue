<script setup lang="ts">
import type {
  allMenuTreeType,
  clientTypeEnum,
  conditionListType,
  dictType,
  fieldListType,
  permissionListType,
} from './type';

import { nextTick, onMounted, ref, watch } from 'vue';

import { cloneDeep } from '@vben/utils';

import { Tree } from '@girant-web/tree-component';
import {
  ElButton,
  ElInput,
  ElMessage,
  ElMessageBox,
  ElScrollbar,
  ElSegmented,
} from 'element-plus';

import { getDictItemList, getEnumByName } from '#/api';
import { delBtn, delMenu, getAllMenuTree } from '#/api/components';

const props = defineProps({
  /** 是否是查看 */
  isView: {
    default: false,
    type: Boolean,
  },
  /** 是否显示新增 */
  showAdd: {
    default: false,
    type: Boolean,
  },
  /** 是否显示勾选 */
  showCheckbox: {
    default: false,
    type: Boolean,
  },
  /** 是否显示编辑区域 */
  showEdit: {
    default: false,
    type: Boolean,
  },
  /** 是否显示顶部区域 */
  showTop: {
    default: false,
    type: Boolean,
  },
});
const emits = defineEmits(['permsTreeSubmitSuccess', 'editMenu', 'addMenu']);

const treeRef = ref<any>({});
/** 展示数据 */
const fetchData = ref<allMenuTreeType[]>([]);
/** 菜单图标字典 */
const permTreeNodeIcon = ref<Map<string, dictType>>(new Map());
/** 菜单数据*/
const treeData = ref<allMenuTreeType[]>([]);
/** 树节点数据类型 */
const treeType = ref({
  '01': '目录',
  '02': '菜单',
  '03': '外链',
  permission: '按钮',
  field: '字段',
  condition: '数据筛选',
});

/** 菜单模式选项 */
const options = [
  { label: '全部', value: 'all' },
  { label: '菜单', value: 'menu' },
  { label: '按钮', value: 'button' },
];
/** 菜单显示模式 */
const treeModel = ref('all');
/** 需要显示操作区域的数据*/
const showOperate = new Set(['01', '02', '03', 'permission']);
const loading = ref(false);

/**
 * @description 处理permissionList按钮权限列表变成菜单类型
 * 并将字段权限和数据筛选权限一并转换成他的children数组
 * @param permissionList 按钮权限列表
 * @returns allMenuTreeType 菜单类型
 */
const handlePermissionList = (permissionList: permissionListType) => {
  const children: allMenuTreeType = {
    menuType: 'permission',
    id: permissionList.permissionId,
    label: ` 【按钮】${permissionList.permissionName}`,
    icon: permTreeNodeIcon.value.get('permission')?.dictLabel,
    ...permissionList,
    children: [],
  };
  // 检查是否有fieldList 字段权限(数据控制)
  if (permissionList.fieldList && permissionList.fieldList.length > 0) {
    permissionList.fieldList.forEach((field: fieldListType) => {
      const fieldList: allMenuTreeType = {
        menuType: 'field',
        id: field.fieldId, // 转换id
        label: `【数据控制】${field.fieldName}`,
        icon: permTreeNodeIcon.value.get('field')?.dictLabel,
        children: [],
        ...field,
      };
      children.children?.push(fieldList);
    });
  }
  // 检查是否有conditionList  数据筛选
  if (permissionList.conditionList && permissionList.conditionList.length > 0) {
    permissionList.conditionList.forEach((condition: conditionListType) => {
      const conditionList: allMenuTreeType = {
        id: condition.conditionId, // 转换id
        label: `【数据筛选】${condition.conditionName}`, // 转换name
        icon: permTreeNodeIcon.value.get('condition')?.dictLabel,
        menuType: 'condition',
        children: [],
        ...condition,
      };
      children.children?.push(conditionList);
    });
  }
  return children;
};

/**
 * @description 菜单 children递归 添加按钮、字段、数据控制权限
 * 在递归时调用handlePermissionList处理按钮权限列表中的数据
 * @param nodes 菜单列表
 */
const handleChildren = (nodes: allMenuTreeType[]) => {
  if (!nodes || !Array.isArray(nodes)) return;
  nodes.forEach((node) => {
    // 处理菜单
    node.label = `【${treeType.value[node.menuType! as '01' | '02' | '03']}】${node.menuName}`;
    node.icon = permTreeNodeIcon.value.get(node.menuType!)?.dictLabel;
    node.id = node.menuId;
    // 检查是否有children 如果有children 则递归到下一层
    if (node.children && node.children.length > 0) {
      handleChildren(node.children);
    }
    if (
      treeModel.value === 'all' &&
      node.permissionList &&
      node.permissionList.length > 0
    ) {
      node.permissionList?.forEach((permission) => {
        const newNode = handlePermissionList(permission);
        if (!node.children) {
          node.children = [];
        }
        node.children.push(newNode);
      });
    }
  });
};

/**
 * @description 提取树中的按钮
 * @param data 树数据列表
 * @returns 按钮列表
 */
const extractButtonMenus = (data: allMenuTreeType[]) => {
  // 递归遍历树 提取按钮
  function traverseMenus(
    menus: allMenuTreeType[],
    children: allMenuTreeType[], // 用于存储提取的按钮菜单
  ) {
    for (const menu of menus) {
      if (menu.permissionList && menu.permissionList.length > 0) {
        for (const permission of menu.permissionList) {
          children.push({
            id: permission.permissionId,
            label: `【按钮】${permission.permissionName}`,
            menuType: 'permission',
            children: [],
          } as unknown as allMenuTreeType);
        }
      }
      if (menu.children && menu.children.length > 0) {
        traverseMenus(menu.children, children);
      }
    }
  }
  for (const item of data) {
    const children: allMenuTreeType[] = [];
    traverseMenus(item.children!, children);
    item.children = children;
  }
  return data;
};
/**
 * @param value 选择的选项
 * @description 根据菜单模式来展示不同的数据 默认展示菜单数据
 * 1. 全部 展示全部数据
 * 2. 按钮 展示按钮数据
 */
const handleChange = (value: string) => {
  fetchData.value = cloneDeep(treeData.value);
  switch (value) {
    case 'all': {
      fetchData.value.forEach((item: allMenuTreeType) => {
        item.children!.forEach((child: allMenuTreeType) => {
          if (child.children) {
            handleChildren(child.children); // 检查并递归处理
          }
        });
      });
      break;
    }
    case 'button': {
      fetchData.value = extractButtonMenus(fetchData.value);
      break;
    }
    // 默认展示菜单数据
    default: {
      for (const item of fetchData.value) {
        handleChildren(item.children!);
      }
      break;
    }
  }
  // 清空搜索
  treeRef.value.searchText = '';
};

/** @description 确认框 返回值为true 表示确认 为false 表示取消*/
const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

/**
 * @description 删除按钮
 * @param id 按钮id
 */
const deleteBtn = async (id: string) => {
  try {
    await delBtn(id);
    emits('permsTreeSubmitSuccess');
    ElMessage.success('删除成功');
  } catch {
    ElMessage.error('删除失败');
  }
};

/**
 * @description 删除菜单
 * @param id 菜单id
 */
const deleteMenuById = async (id: string) => {
  try {
    await delMenu(id);
    emits('permsTreeSubmitSuccess');
    ElMessage.success('删除成功');
  } catch {
    ElMessage.error('删除失败');
  }
};

/** 点击操作 */
const operate = {
  /** 点击添加按钮 */
  addMenu: () => {
    const data = treeRef.value.tree.getCurrentNode();
    emits('addMenu', data);
  },

  /**
   * @param data 传入的菜单数据
   * @description 点击删除按钮 删除对应菜单 根据菜单类型来删除 默认删除菜单
   * permission 按钮
   */
  deleteMenu: async (data: allMenuTreeType) => {
    await confirm(`确定删除${data.label}吗？`, `提示`);
    switch (data.menuType) {
      case 'permission': {
        await deleteBtn(data.id!);
        break;
      }
      default: {
        await deleteMenuById(data.id!);
        break;
      }
    }
  },

  /**
   * @description 点击编辑按钮
   * @param data 传入的菜单数据
   */
  editMenu: (data: allMenuTreeType) => {
    emits('editMenu', data);
  },
};

/** 点击菜单进行查看 */
const nodeClick = (data: allMenuTreeType) => {
  if (showOperate.has(data.menuType!)) operate.editMenu(data);
};

//  监听数据变化
watch(
  () => treeData.value,
  () => {
    handleChange(treeModel.value); // 当数据变化时 调用handleChange方法更新数据
  },
);

/** 获取菜单树数据 并处理数据并赋值*/
async function getMenuTree() {
  try {
    loading.value = true;
    // 获取菜单树 // 获取客户端类型枚举 // 获取图标字典
    const [res, enumType, icon] = await Promise.all([
      getAllMenuTree(),
      getEnumByName('ClientTypeEnums'),
      getDictItemList('permTreeNodeIcon'),
    ]);
    // 要显示的菜单数据
    const enumData: allMenuTreeType[] = [];
    // 处理菜单数据 返回指定客户端类型的菜单树
    function filterMenus(
      items: allMenuTreeType[],
      targetClientType: string,
    ): allMenuTreeType[] {
      return items
        .filter((item) => item.clientType === targetClientType)
        .map((item) => ({
          ...item,
          id: item.menuId,
          label: `【${item.menuTypeLabel}】${item.menuName}`,
          icon: permTreeNodeIcon.value.get(item.menuType!)?.dictLabel,
        }));
    }
    // 处理图标字典
    permTreeNodeIcon.value = new Map(
      icon.map((item: dictType) => [item.dictValue, item]),
    );
    // 遍历枚举类型 并将枚举类型的菜单树赋值给enumData
    enumType.forEach((item: clientTypeEnum) => {
      enumData.push({
        id: item.enumValue,
        label: item.enumLabel,
        menuType: item.enumLabel,
        icon: permTreeNodeIcon.value.get(item.enumValue)?.dictLabel,
        children: filterMenus(res, item.enumValue),
      });
    });
    treeData.value = enumData;
  } catch {
    ElMessage.error('获取菜单树失败');
  } finally {
    loading.value = false;
  }
}

onMounted(async () => {
  // 初始化时启用严格模式
  treeRef.value.useConfig.checkStrictly = true;
  await getMenuTree();
  // 初始化时 展示全部数据
  handleChange('all');
  // 检查是否是查看 如果是查看 则禁用所有节点
  await nextTick(() => {});
  if (props.isView) {
    treeRef.value?.disabledAll(true);
  }
  // 设置一个定时器 等待树加载完成 然后禁用严格模式
  setTimeout(() => {
    treeRef.value.useConfig.checkStrictly = false;
  });
});
defineExpose({
  getMenuTree,
  treeRef,
});
</script>
<template>
  <div
    v-loading="loading"
    :class="{
      '!pt-[40px]': showTop,
      '!pb-[40px]': showAdd,
    }"
  >
    <!-- 顶部 -->
    <div
      v-if="showTop"
      class="absolute top-[5px] flex w-[520px] flex-nowrap justify-between"
    >
      <!-- 树模式选择器 -->
      <ElSegmented
        v-model="treeModel"
        :options="options"
        size="default"
        @change="handleChange"
      />
      <!-- 搜索框 -->
      <ElInput
        placeholder="搜索"
        v-model="treeRef.searchText"
        class="!w-[100px]"
        clearable
      />
    </div>
    <!-- 菜单树 -->
    <ElScrollbar>
      <Tree
        ref="treeRef"
        :data-source="fetchData"
        highlight-class="bg-yellow-300"
        :draggable="false"
        :multiple-choice="true"
        :show-checkbox="showCheckbox"
        tree-class="flex flex-nowrap"
        max-text-length="600px"
        @node-click="nodeClick"
        :tree-style="{
          maxWidth: '600px',
        }"
      >
        <template #icon="{ data }">
          <i :class="data.icon"></i>
        </template>
        <template #operate="{ data }" v-if="showEdit">
          <div
            class="ml-[10px]"
            v-if="data?.menuType && showOperate.has(data.menuType)"
          >
            <ElButton link type="primary" @click.stop="operate.editMenu(data)">
              <i class="icon-[bytesize--edit]"></i>编辑
            </ElButton>
            <ElButton type="danger" link @click.stop="operate.deleteMenu(data)">
              <i class="icon-[material-symbols-light--delete-outline]"></i>
              删除
            </ElButton>
          </div>
        </template>
      </Tree>
    </ElScrollbar>
    <ElButton
      v-if="showAdd"
      type="primary"
      class="absolute bottom-[5px] left-1/2 -translate-x-1/2 transform"
      @click="operate.addMenu"
    >
      新增
    </ElButton>
  </div>
</template>
