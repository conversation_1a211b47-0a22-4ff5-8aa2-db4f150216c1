<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  execBatchnumDoc,
  getBatchnumDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      batchnumDocNumberList?: string;
      docCode?: unknown;
      submitEndTime?: string;
      submitStartTime?: string;
      submitUserList?: string;
      warehouseIdList?: string;
    }>,
    default: () => ({}),
  },
});

const batchnumDocId = ref<string>('');
const batchnumDocNumber = ref<string>('');
const docCode = ref<string>('');

/** 申请时间 */
const submitTime = ref({
  // 开始时间
  submitStartTime: props.params?.submitStartTime || '',
  // 结束时间
  submitEndTime: props.params?.submitEndTime || '',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      submitTime.value = {
        submitStartTime: '',
        submitEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? false : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? false
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.submitStartTime = submitTime.value.submitStartTime;
          params.submitEndTime = submitTime.value.submitEndTime;

          if (params.origDocTypeCodeList) {
            params.origDocTypeCodeList = params.origDocTypeCodeList.join(',');
          }

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          if (params.docStatusList) {
            params.docStatusList = params.docStatusList.join(',');
          }

          return await getBatchnumDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'transferDocId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    batchnumDocNumberList:
      props.params?.batchnumDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    docCode: isEmpty(props.params?.docCode) ? '' : props.params?.docCode,
    submitStartTime: props.params?.submitStartTime || '',
    submitEndTime: props.params?.submitEndTime || '',
  });
});

const [FormModal, formModalApi] = useVbenModal({
  confirmText: '新增调拨',
  destroyOnClose: true,
  title: '批次号处理详情',
  onBeforeClose: () => {
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const onView = (row: RowType) => {
  batchnumDocId.value = row.batchnumDocId;
  batchnumDocNumber.value = row.batchnumDocNumber;
  docCode.value = row.docCode;
  formModalApi.open();
};

const execHandle = async (row: RowType) => {
  await ElMessageBox.confirm('确定执行吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        await execBatchnumDoc(row.batchnumDocId);
        ElMessage.success('执行成功');
        gridApi.query();
      } catch {
        ElMessage.error('执行失败');
      }
    })
    .catch(() => {});
};

function onBatchNumberLoading(loading: boolean = true) {
  formModalApi.setState({ loading });
}

function onBatchNumberSuccess() {
  onBatchNumberLoading(false);
  formModalApi.close();
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        :batchnum-doc-id="batchnumDocId"
        :batchnum-doc-number="batchnumDocNumber"
        :doc-code="docCode"
        @batch-number-loading="onBatchNumberLoading"
        @exec-success="onBatchNumberSuccess"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>
    <Grid>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.submitStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.submitEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.submitEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                submitTime.submitStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #operation="{ row }">
        <ElButton type="info" size="small" link @click="onView(row)">
          查看
        </ElButton>
        <ElButton
          type="primary"
          size="small"
          link
          @click="execHandle(row)"
          v-access:code="'wm:batchnum:exec'"
        >
          执行
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
