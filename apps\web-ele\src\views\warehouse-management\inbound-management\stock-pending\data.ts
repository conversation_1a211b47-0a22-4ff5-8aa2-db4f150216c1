import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ElBadge, ElInputTag } from 'element-plus';

import { getEnumByName, getOriginalDocConfigList } from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN', 'IN_AND_OUT']);

const { hasAccessByCodes } = useAccess();

// 查询参数类型
export interface SearchParams {
  inBoundDocNumberList: string;
  applyUserList: string;
  origDocTypeCodeList: string[];
  origDocNumberList: string;
  isRectify: boolean;
  applyTime: string[];
}

// 表格数据类型
export interface RowType {
  applyTime: string;
  applyUser: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  applyUserName: string;
  inBoundDocId: string;
  inBoundDocNumber: string;
  isRectify: boolean;
  origDocId: string;
  origDocNumber: string;
  origDocTypeCode: string;
  origDocTypeName: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'inBoundDocNumberList',
      label: '入库单号',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '入库类型',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isRectify',
      label: '是否补录',
      formItemClass: 'hidden',
    },

    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'inBoundDocNumber',
        nameTitle: '操作',
        onClick: onActionClick,
      },
      name: 'CellOperation',

      options: [
        ...(hasAccessByCodes(['wm:inbound:exec'])
          ? [
              {
                code: 'inbound',
                label: '确认入库',
                type: 'primary',
              },
            ]
          : []),
        ...(hasAccessByCodes(['wm:inoutbound:cancel:submit'])
          ? [
              {
                code: 'close',
                label: '取消入库',
                type: 'danger',
              },
            ]
          : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    title: '操作',
    width: 'auto',
  } as NonNullable<VxeTableGridOptions['columns']>[0];

  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '补录',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: !row.isRectify,
              class: 'item',
            },
            {
              default: () => row.inBoundDocNumber,
            },
          );
        },
      },
      field: 'inBoundDocNumber',
      title: '入库单号',
      minWidth: 250,
    },
    {
      field: 'inBoundDocId',
      title: '入库单ID',
      visible: false,
    },
    {
      field: 'origDocTypeName',
      width: 180,
      title: '入库类型',
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'origDocId',
      title: '申请单ID',
      visible: false,
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyUser',
      title: '申请人ID',
      visible: false,
    },
    {
      field: 'applyUserDeptId',
      title: '申请人部门ID',
      visible: false,
    },

    {
      field: 'applyUserDeptName',
      title: '申请人部门',
      visible: false,
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 200,
    },

    {
      field: 'isRectify',
      title: '是否补录',
      visible: false,
    },

    ...(hasAccessByCodes(['wm:inbound:exec', 'wm:inoutbound:cancel:submit'])
      ? [operationColumn]
      : []),
  ];
}
