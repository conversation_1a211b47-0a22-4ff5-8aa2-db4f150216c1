import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { DocStatus } from '#/api/common';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getDocStatusInfo } from '#/api/common';

import { docStatusDict } from '../config/list';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'invcAdjustDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'modifyTime',
      labelWidth: 100,
      formItemClass: 'col-span-2 w-full',
      label: '最后修改时间',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: DocStatus.DocStatusItem[]) => {
          const warehouseList = data.map((item) => ({
            label: item.docStatusLabel,
            value: item.docStatusKey,
          }));
          // 只保留审核驳回 、待提交
          const filterList = warehouseList.filter(
            (item) => item.value === 'reject' || item.value === 'pending',
          );
          return filterList;
        },
        api: () => {
          return getDocStatusInfo('WM0090');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
  ];
}
/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) => {
          return h('span', row.invcAdjustDocNumber || '暂无单据编号');
        },
      },
      title: '单据编号',
      field: 'invcAdjustDocNumber',
      minWidth: 120,
    },
    {
      title: '最后修改时间',
      field: 'modifyTime',
      minWidth: 115,
    },
    {
      slots: {
        default: ({ row }) =>
          h(
            ElTag,
            {
              size: 'small',
              type: docStatusDict[row.docStatus],
            },
            {
              default: () => row.docStatusLabel,
            },
          ),
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 100,
    },
    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 175,
      title: '操作',
    },
  ];
}
