import type { Component, DefineComponent } from 'vue';

import type {
  AccessModeType,
  GenerateMenuAndRoutesOptions,
  RouteRecordRaw,
} from '@vben/types';

import { defineComponent, h } from 'vue';

import {
  cloneDeep,
  generateRoutesByBackend,
  generateRoutesByFrontend,
  isFunction,
  isString,
  mapTree,
} from '@vben/utils';

async function generateAccessible(
  mode: AccessModeType,
  options: GenerateMenuAndRoutesOptions,
) {
  const { router, fetchMenuTreeAsync } = options;

  options.routes = cloneDeep(options.routes);

  // 从新接口获取菜单树数据
  const menuTree = (await fetchMenuTreeAsync?.()) || [];

  // 生成路由前处理路由数据，添加affixTab
  const processedRoutes = await processRoutesWithMenuData(
    options.routes,
    menuTree,
  );

  // 生成路由
  const accessibleRoutes = await generateRoutes(mode, {
    ...options,
    routes: processedRoutes,
  });

  const root = router.getRoutes().find((item) => item.path === '/');

  // 获取已有的路由名称列表
  const names = root?.children?.map((item) => item.name) ?? [];

  // 动态添加到router实例内
  accessibleRoutes.forEach((route) => {
    if (root && !route.meta?.noBasicLayout) {
      // 为了兼容之前的版本用法，如果包含子路由，则将component移除，以免出现多层BasicLayout
      // 如果你的项目已经跟进了本次修改，移除了所有自定义菜单首级的BasicLayout，可以将这段if代码删除
      if (route.children && route.children.length > 0) {
        delete route.component;
      }
      // 根据router name判断，如果路由已经存在，则不再添加
      if (!names?.includes(route.name)) {
        root.children?.push(route);
      }
    } else {
      router.addRoute(route);
    }
  });

  if (root) {
    if (root.name) {
      router.removeRoute(root.name);
    }
    router.addRoute(root);
  }

  const accessibleMenus = await generateMenusFromMenuTree(menuTree);

  return { accessibleMenus, accessibleRoutes };
}

interface MenuRecordRaw {
  name: string;
  path: string;
  children: MenuRecordRaw[];
  icon: string;
  meta: {
    affixTab: boolean;
    icon: string;
    openTab: boolean;
    order: number;
    title: string;
  };
}

// 新增函数，根据菜单树数据生成菜单
async function generateMenusFromMenuTree(
  menuTree: any[],
): Promise<MenuRecordRaw[]> {
  const mapMenu = (menu: any): MenuRecordRaw => ({
    name: menu.menuName,
    path: menu.targetUrl ?? '',
    children: (menu.children ?? []).map((child: any) => mapMenu(child)),
    icon: menu.menuIcon,
    meta: {
      affixTab: menu.isFixed,
      order: menu.menuOrder,
      title: menu.menuName,
      icon: menu.menuIcon,
      openTab: menu.isDefaultOpen,
    },
  });

  // 显式指定回调函数参数类型
  return menuTree.map((item: any) => mapMenu(item));
}

// 新增函数，处理路由数据，添加affixTab
async function processRoutesWithMenuData(
  routes: RouteRecordRaw[],
  menuTree: any[],
) {
  const findMenuByPath = (path: string) => {
    const flatMenuTree = flattenMenuTree(menuTree);
    return flatMenuTree.find((menu) => menu.targetUrl === path);
  };

  return mapTree(routes, (route) => {
    const menu = findMenuByPath(route.path);
    if (menu) {
      route.meta = route.meta || {};
      route.meta.affixTab = menu.isFixed;
    }
    return route;
  });
}

// 新增函数，将菜单树扁平化
function flattenMenuTree(menuTree: any[]): any[] {
  let result: any[] = [];
  menuTree.forEach((menu) => {
    result = [
      ...result,
      menu,
      ...(menu.children ? flattenMenuTree(menu.children) : []),
    ];
  });
  return result;
}

/**
 * Generate routes
 * @param mode
 * @param options
 */
async function generateRoutes(
  mode: AccessModeType,
  options: GenerateMenuAndRoutesOptions,
) {
  const { forbiddenComponent, roles, routes } = options;

  let resultRoutes: RouteRecordRaw[] = routes;
  switch (mode) {
    case 'backend': {
      resultRoutes = await generateRoutesByBackend(options);
      break;
    }
    case 'frontend': {
      resultRoutes = await generateRoutesByFrontend(
        routes,
        roles || [],
        forbiddenComponent,
      );
      break;
    }
  }

  /**
   * 调整路由树，做以下处理：
   * 1. 对未添加redirect的路由添加redirect
   * 2. 将懒加载的组件名称修改为当前路由的名称（如果启用了keep-alive的话）
   */
  resultRoutes = mapTree(resultRoutes, (route) => {
    // 重新包装component，使用与路由名称相同的name以支持keep-alive的条件缓存。
    if (
      route.meta?.keepAlive &&
      isFunction(route.component) &&
      route.name &&
      isString(route.name)
    ) {
      const originalComponent = route.component as () => Promise<{
        default: Component | DefineComponent;
      }>;
      route.component = async () => {
        const component = await originalComponent();
        if (!component.default) return component;
        return defineComponent({
          name: route.name as string,
          setup(props, { attrs, slots }) {
            return () => h(component.default, { ...props, ...attrs }, slots);
          },
        });
      };
    }

    // 如果有redirect或者没有子路由，则直接返回
    if (route.redirect || !route.children || route.children.length === 0) {
      return route;
    }
    const firstChild = route.children[0];

    // 如果子路由不是以/开头，则直接返回,这种情况需要计算全部父级的path才能得出正确的path，这里不做处理
    if (!firstChild?.path || !firstChild.path.startsWith('/')) {
      return route;
    }

    route.redirect = firstChild.path;
    return route;
  });

  return resultRoutes;
}

export { generateAccessible };
