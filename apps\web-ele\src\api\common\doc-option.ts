import { requestClient } from '#/api/request';

import { systemPath } from '../path';

/** 枚举返回值类型 */
export interface DocOptionTypeParams {
  /** 单据标识，精确查询 */
  docCode: string;
  /** 单据字段code，精确查询 */
  docFieldCode?: string;
}

export interface DocOptionItemList {
  /** 是否必填备注说明，选中此项是否必填备注说明 */
  isActiveDescRequired: boolean;
  /** 选项ID */
  optionId: number;
  /** 选项名称 */
  optionName: string;
}

export interface DocOptionType {
  /** 单据标识 */
  docCode: string;

  /** 单据字段code */
  docFieldCode: string;

  /** 单据字段名称 */
  docFieldName: string;

  /** 字段选项数量限制，0为不限制，1为单选，2为最多只能选2个 */
  optionItemLimit: number;

  /** 单据字段的选项列表，配置单据部分字段的选项列表 */
  optionItemList: DocOptionItemList[];
}

/** 查询单据备注选项列表*/
export async function getDocOptionList(params: DocOptionTypeParams) {
  return requestClient.get<DocOptionType[]>(
    `${systemPath}/doc/option/getDocOptionList`,
    {
      params,
    },
  );
}
