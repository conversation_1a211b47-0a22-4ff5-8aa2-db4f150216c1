<script setup lang="ts">
import type { ExpandTrigger } from 'element-plus';

import type { PropType } from 'vue';

import { computed, defineEmits, defineProps, onMounted, ref } from 'vue';

import { ElCascader } from 'element-plus';

import { getDeptStaffTree } from '#/api/components';

// 定义人员接口
type Staff = {
  staffCode: string;
  staffId: number;
  staffName: string;
};

// 定义岗位接口
type Position = {
  positionCode: string;
  positionId: number;
  positionName: string;
};

// 定义部门接口，支持多层级
type Dept = {
  children?: Dept[]; // 支持多层级
  deptAbbr: string;
  deptCode: string;
  deptDescribe: string;
  deptId: number;
  deptName: string;
  deptSort: number;
  parentId: number;
  parentName: string;
  positionList: Position[];
  serialNumber: number;
  staffList: Staff[];
};

// 定义级联选择器选项接口
type CascaderOption = {
  [key: string]: any;
  children: CascaderOption[];
  disabled?: boolean;
  label: string;
  leaf?: boolean;
  value: number | string;
};

// 定义组件属性
const props = defineProps({
  // 绑定的值，可以是数组、数字或字符串
  value: {
    type: [Array, Number, String] as PropType<
      Array<number | string> | number | string
    >,
    default: () => [],
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: false,
  },
  // 是否自动加载数据
  autoLoad: {
    type: Boolean,
    default: true,
  },
  // 折叠标签的最大数量
  maxCollapseTags: {
    type: Number,
    default: 2,
  },
  // 是否严格遵守父子节点不关联的选择逻辑
  checkStrictly: {
    type: Boolean,
    default: false,
  },
  // 是否返回选中节点的完整路径
  emitPath: {
    type: Boolean,
    default: false,
  },
  // 是否显示没有员工的部门
  showEmptyDept: {
    type: Boolean,
    default: false,
  },
  // 部门节点是否可选
  deptSelectable: {
    type: Boolean,
    default: true,
  },
  // 输入框中是否显示选中值的完整路径
  showAllLevels: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: '请选择人员',
  },
});

const emit = defineEmits(['update:value', 'loadSuccess', 'loadFail']);

const cascaderRef = ref<InstanceType<typeof ElCascader>>();
const deptList = ref<Dept[]>([]);
const loading = ref(false);

const selectedValue = computed({
  get() {
    return props.value;
  },
  set(value) {
    emit('update:value', value);
  },
});

const handleChange = (value: any) => {
  selectedValue.value = value;
};

const loadDeptList = async () => {
  loading.value = true;
  try {
    const res = await getDeptStaffTree({
      isQueryStaff: true,
    });
    deptList.value = res || [];
    emit('loadSuccess', deptList.value);
  } catch (error) {
    console.error('加载部门数据失败', error);
    emit('loadFail', error);
  } finally {
    loading.value = false;
  }
};

// 递归构建级联选择器选项，修复类型不匹配问题
const buildCascaderOptions = (depts: Dept[]): CascaderOption[] => {
  // 使用类型断言明确过滤后的类型
  return (
    depts
      .map((dept) => {
        // 如果不显示空部门且该部门没有员工和子部门，则跳过
        if (
          !props.showEmptyDept &&
          (!dept.staffList || dept.staffList.length === 0) &&
          (!dept.children || dept.children.length === 0)
        ) {
          return null;
        }

        const deptNode: CascaderOption = {
          value: `dept_${dept.deptId}`,
          label: dept.deptName,
          children: [],
          disabled: !props.deptSelectable,
        };

        // 递归处理子部门
        if (dept.children && dept.children.length > 0) {
          const childOptions = buildCascaderOptions(dept.children);
          deptNode.children.push(...childOptions);
        }

        // 处理当前部门的员工
        if (dept.staffList && dept.staffList.length > 0) {
          deptNode.children.push(
            ...dept.staffList.map((staff) => ({
              value: staff.staffId,
              label: staff.staffName,
              leaf: true,
              children: [],
            })),
          );
        }

        return deptNode;
      })
      // 过滤掉null值，并通过类型断言告诉TS此时已无null
      .filter((item): item is CascaderOption => item !== null)
  );
};

onMounted(() => {
  if (props.autoLoad) {
    loadDeptList();
  }
});

const cascaderOptions = computed(() => {
  if (!deptList.value || deptList.value.length === 0) return [];
  return buildCascaderOptions(deptList.value);
});

const cascaderProps = computed(() => ({
  expandTrigger: 'click' as ExpandTrigger,
  multiple: props.multiple,
  value: 'value',
  label: 'label',
  emitPath: props.emitPath,
  checkStrictly: props.checkStrictly,
  showCheckedStrategy: 'leaf',
}));
</script>

<template>
  <ElCascader
    v-model="selectedValue"
    :options="cascaderOptions"
    :props="cascaderProps"
    @change="handleChange"
    ref="cascaderRef"
    :loading="loading"
    filterable
    collapse-tags
    collapse-tags-tooltip
    :show-all-levels="showAllLevels"
    :max-collapse-tags="props.maxCollapseTags"
    :placeholder="placeholder"
  />
</template>
