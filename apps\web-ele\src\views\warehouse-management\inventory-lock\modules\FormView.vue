<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { unlock } from '#/api';

import LockDetailsForm from './lock-details-form/index.vue';
import UnLockDetailsForm from './unlock-details-form/index.vue';

/** 共享数据 */
const sharedData = ref();
const [ViewModal, viewModalApi] = useVbenModal({
  showConfirmButton: false,
  title: '查看',
  cancelText: '关闭',
  closeOnClickModal: false,
  onCancel() {
    viewModalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = viewModalApi.getData<Record<string, any>>();
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  viewModalApi.close();
};
/** 提交解锁 */
const submitUnlock = async (blockId: string) => {
  try {
    await ElMessageBox.confirm('确认解锁吗？', '提示', {
      type: 'warning',
    });
    await unlock(blockId);
    ElMessage.success('解锁成功');
    refreshList();
  } catch (error) {
    console.error(error);
  }
};
</script>

<template>
  <ViewModal>
    <LockDetailsForm :block-id="sharedData.blockId" />
    <UnLockDetailsForm
      v-if="!sharedData.isValid"
      :block-id="sharedData.blockId"
    />
    <template #prepend-footer>
      <ElButton
        type="primary"
        @click="submitUnlock(sharedData.blockId)"
        v-access:code="'wm:inventory:lock:unlock'"
      >
        解锁
      </ElButton>
    </template>
  </ViewModal>
</template>
