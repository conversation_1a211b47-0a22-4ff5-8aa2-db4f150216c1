import type { VbenFormSchema } from '@girant/adapter';

import type { dictItemListType } from '#/api/common';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

import { getDictItemList } from '#/api/common';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';
/** 出库单信息 */
export function useFormSchema(isSubmit: boolean = false): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'inOutReqDocNumber',
      label: '单据编号',
      componentProps: {
        disabled: true,
        placeholder: '系统默认自动生成',
      },
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('wmOtherOutReqType');
        },
        filterable: true,
        clearable: true,
        disabled: isSubmit,
      },
      fieldName: 'docCode',
      label: '出库类型',
      rules: 'selectRequired',
    },
    // {
    //   component: 'Checkbox',
    //   fieldName: 'isRectify',
    //   label: '',
    //   renderComponentContent: () => {
    //     return {
    //       default: () => ['补录'],
    //     };
    //   },
    // },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'materialUser',
      formItemClass: 'col-span-1',
      label: '使用人',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注说明',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
