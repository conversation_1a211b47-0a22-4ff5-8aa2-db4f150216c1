import type { OnActionClickFn, VxeTableGridOptions } from '@girant/adapter';

import type { SystemMenuApi } from '#/api';

export function getMenuTypeOptions() {
  return [
    {
      color: 'processing',
      label: '目录',
      value: 'catalog',
    },
    { color: 'default', label: '菜单', value: 'menu' },
    { color: 'error', label: '按钮', value: 'button' },
    {
      color: 'success',
      label: '内嵌',
      value: 'embedded',
    },
    { color: 'warning', label: '外链', value: 'link' },
  ];
}

export function useColumns(
  onActionClick: OnActionClickFn<SystemMenuApi.SystemMenu>,
): VxeTableGridOptions<SystemMenuApi.SystemMenu>['columns'] {
  return [
    {
      align: 'left',
      field: 'menuName',
      fixed: 'left',
      slots: { default: 'title' },
      title: '标题',
      treeNode: true,
      width: 250,
    },
    {
      field: 'openTypeLabel',
      title: '菜单打开方式',
      width: 200,
    },
    {
      align: 'left',
      field: 'targetUrl',
      title: '	菜单地址',
      width: 200,
    },
    {
      align: 'right',
      cellRender: {
        attrs: {
          nameField: 'name',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'append',
            text: '新增下级',
          },
          'edit', // 默认的编辑按钮
          'delete', // 默认的删除按钮
        ],
      },
      field: 'operation',
      fixed: 'right',
      headerAlign: 'center',
      showOverflow: false,
      title: '操作',
      width: 200,
    },
  ];
}
