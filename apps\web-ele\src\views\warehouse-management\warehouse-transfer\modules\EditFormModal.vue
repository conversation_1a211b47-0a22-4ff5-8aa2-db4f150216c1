<script setup lang="ts">
import type { TransferQueryApi } from '#/api/warehouse-management';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  delTransferDoc,
  saveOrModTransferDoc,
  submitTransferDoc,
} from '#/api/warehouse-management';

import AdjustmentDetails from '../components/adjustment-details/Edit.vue';
import AdjustmentInformation from '../components/adjustment-info/Edit.vue';

const AdjustmentInformationRef =
  ref<InstanceType<typeof AdjustmentInformation>>();

const AdjustmentDetailsRef = ref<InstanceType<typeof AdjustmentDetails>>();

/** 共享数据 */
const data = ref();

const warehouseId = ref('');

const handleChangeWarehouseId = (value: string) => {
  warehouseId.value = value;
};

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});

/** 刷新列表 */
const refreshList = () => {
  data.value?.refreshList();
  modalApi.setState({ loading: false }).close();
};

/** 获取调拨信息数据 */
const getInfoData = async () => {
  const infoData = await AdjustmentInformationRef.value?.getFormData();
  if (!infoData) {
    throw new Error('请检查调拨信息');
  }
  return infoData;
};

/** 获取调拨详情数据 */
const getDetailData = async () => {
  const detailData = await AdjustmentDetailsRef.value?.getFormData();
  if (!detailData) {
    throw new Error('请检查调拨详情');
  }

  if (detailData?.length === 0) {
    throw new Error('调拨详情表单不能为空');
  }

  return detailData;
};

/** 获取提交数据 */
const getSubmitData = async () => {
  try {
    const infoData = await getInfoData();
    const detailData = await getDetailData();
    const submitData: TransferQueryApi.SubmitTransferDocParams = {
      ...infoData,
      transferDocId: data.value.transferDocId,
      transferItemList: detailData,
    };

    return submitData;
  } catch (error) {
    ElMessage.error((error as Error).message);
    return false;
  }
};

/** 提交调拨 */
const submitHandle = async () => {
  const submitData = await getSubmitData();
  if (!submitData) {
    return;
  }

  await ElMessageBox.confirm('确定提交调拨吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        modalApi.setState({ loading: true });
        await submitTransferDoc(submitData);
        ElMessage.success('提交成功');
        refreshList();
      } catch {
        modalApi.setState({ loading: false });
        ElMessage.error('提交失败');
      }
    })
    .catch(() => {});
};

/** 暂存调拨 */
const saveHandle = async () => {
  const submitData = await getSubmitData();
  if (!submitData) {
    return;
  }

  await ElMessageBox.confirm('确定暂存调拨吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        modalApi.setState({ loading: true });
        await saveOrModTransferDoc(submitData);
        ElMessage.success('暂存成功');
        refreshList();
      } catch {
        modalApi.setState({ loading: false });
        ElMessage.error('暂存失败');
      }
    })
    .catch(() => {});
};

const deleteHandle = async () => {
  await ElMessageBox.confirm('确定删除单据吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        modalApi.setState({ loading: true });
        await delTransferDoc(data.value.transferDocId);
        ElMessage.success('删除成功');
        refreshList();
      } catch {
        modalApi.setState({ loading: false });
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {});
};
</script>

<template>
  <Modal>
    <AdjustmentInformation
      ref="AdjustmentInformationRef"
      :transfer-doc-id="data.transferDocId"
      :transfer-doc-number="data.transferDocNumber"
      :is-edit="data.isEdit"
      @change-warehouse-id="handleChangeWarehouseId"
    />

    <AdjustmentDetails
      ref="AdjustmentDetailsRef"
      :transfer-doc-id="data.transferDocId"
      :transfer-doc-number="data.transferDocNumber"
      :warehouse-id="warehouseId"
      :is-edit="data.isEdit"
    />

    <template #footer>
      <div class="flex items-center">
        <ElButton type="info" @click="modalApi.close()"> 取消 </ElButton>

        <ElButton
          type="primary"
          @click="saveHandle"
          v-if="data.docStatus !== 'reject'"
          v-access:code="'wm:stock:transfer:submit'"
        >
          暂存
        </ElButton>

        <ElButton
          type="primary"
          @click="submitHandle"
          v-access:code="'wm:stock:transfer:submit'"
        >
          {{ data.docStatus === 'reject' ? '重新提交' : '提交' }}
        </ElButton>

        <ElButton type="danger" @click="deleteHandle" v-if="data.transferDocId">
          删除单据
        </ElButton>
      </div>
    </template>
  </Modal>
</template>
