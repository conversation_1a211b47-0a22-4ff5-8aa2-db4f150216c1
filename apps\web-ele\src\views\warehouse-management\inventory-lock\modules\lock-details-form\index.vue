<script setup lang="ts">
import type { InventoryLock } from '#/api/warehouse-management';

import { defineAsyncComponent, onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '@girant/adapter';
import { ElButton, ElMessage, ElTag } from 'element-plus';

import { getInvcBlock } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 库存锁库id */
  blockId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 物料编号 */
const materialCode = ref('');
/** 物料id */
const materialId = ref('');
/** 仓库名称 */
const warehouseName = ref('');
/** 模态框组件*/
const [MaterialModal, materialModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(() => import('./MaterialModal.vue')),
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
  title: '物料详情',
});
/** 锁库原因选项列表*/
const remarkOptionList =
  ref<InventoryLock.InvcBlockDetail['remarkOptionList']>();
/** 查看物料信息 */
const onViewMaterial = () => {
  materialModalApi
    .setData({
      materialId: materialId.value,
      materialCode,
      warehouseName,
    })
    .open();
};
/** 锁库信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 xl:grid-cols-3',
});
/** 根据库存锁库id获取库存锁库详细信息 */
const getMaterialInfo = async (blockId: string) => {
  try {
    loading.value = true;
    const res = await getInvcBlock(blockId);
    materialCode.value = res.materialCode;
    materialId.value = res.materialId;
    warehouseName.value = res.warehouseName;
    remarkOptionList.value = res.remarkOptionList;
    formApi.setValues(res);
  } catch {
    ElMessage.error('获取库存锁库详细信息失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.blockId) {
    getMaterialInfo(props.blockId);
  }
});
defineExpose({
  Form,
  formApi,
});
</script>
<template>
  <!-- 模态框 -->
  <MaterialModal class="h-full w-10/12" />
  <FormCard title="锁库信息" :is-footer="false" class="min-w-[600px]">
    <Form v-loading="loading">
      <template #materialName="row">
        <ElButton link type="primary" @click="onViewMaterial">
          {{ row.value }}({{ materialCode }})
        </ElButton>
      </template>
      <template #remark="row">
        <div class="mb-2 flex w-full flex-wrap gap-2">
          <ElTag
            type="primary"
            effect="dark"
            v-for="item in remarkOptionList"
            :key="item.optionId"
          >
            {{ item.optionName }}
          </ElTag>
        </div>
        <span class="text-sm text-gray-500">{{ row.value }}</span>
      </template>
    </Form>
  </FormCard>
</template>
