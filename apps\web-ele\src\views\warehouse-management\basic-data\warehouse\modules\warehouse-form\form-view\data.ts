import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export type mapType = {
  label: string;
  value: string;
};

/** 基础资料 查看 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'warehouseCode',
      label: '仓库编号',
    },
    {
      component: 'div',
      fieldName: 'warehouseName',
      formItemClass: 'col-span-2',
      label: '仓库名称',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'managerUserName',
      label: '仓库负责人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'locationDesc',
      label: '所在位置',
      formItemClass: 'col-span-full',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full',
    },
  ];
}

/** 仓库策略 查看*/
export function useFormSchema2(): VbenFormSchema[] {
  return [
    {
      component: 'div',
      fieldName: 'limitMaterialTypeList',
      label: '物料类型限制',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'inOutStrategyLabel',
      label: '物料库存策略',
    },

    {
      component: 'div',
      fieldName: 'limitDocs',
      label: '操作限制单据',
      formItemClass: 'items-start',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'limitDocTypeLabel',
      label: '操作限制单据类型',
    },
  ];
}
