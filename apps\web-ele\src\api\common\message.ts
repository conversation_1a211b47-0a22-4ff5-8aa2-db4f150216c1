import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';

export interface MessageRecord {
  messageId?: string;
  messageTitle?: string;
  messageContent?: string;
  avatar?: string;
  sendTime?: string;
  isRead?: boolean;
  [key: string]: any;
}

export interface MessagePageResult {
  records: MessageRecord[];
  total: number;
  size: number;
  current: number;
}

// 获取我接收的站内信分页列表
export async function getReceiveMsgPage(params: Recordable<any>) {
  return requestClient.post<MessagePageResult>(
    `${baseDataPath}/base/msg/station/record/mi/getReceiveMsgPage`,
    { ...params },
  );
}
