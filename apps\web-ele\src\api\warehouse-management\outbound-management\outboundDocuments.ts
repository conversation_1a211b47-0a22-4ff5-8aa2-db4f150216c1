import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace OutboundDocumentsApi {
  export interface OutboundDocumentsPageParams {
    /** 出库单据号列表，多个用英文逗号分隔 */
    outBoundDocNumberList: string[];
    /** 申请人id列表，精确查询 */
    applyUserList: string[];
    /** 源单据类型标识列表，精确查询，根据查询来源单据配置列表接口的出参docCode字段。 */
    origDocTypeCodeList: string[];
    /** 源单据编号列表，精确查询 */
    origDocNumberList: string[];
    /** 是否补录，不填则展示全部，true-补录，false-非补录 */
    isRectify: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: string[];
    /** 使用人id，精确查询 */
    materialUser: string;
    /** 使用人部门id列表，精确查询 */
    materialUserDeptList: string[];
    /** 备料状态值列表，字典WmPreparationStateEnums */
    preparationStateList: string[];
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 单据状态值列表，字典wmOutDocStatus */
    docStatusList: string[];
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  export interface OutboundDocRecord {
    /* 出库单据ID */
    outBoundDocId: number;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: number;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: number;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: number;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 领料人ID */
    collectorUser: number;

    /* 领料人姓名 */
    collectorUserName: string;

    /* 领料人部门ID */
    collectorUserDeptId: number;

    /* 领料人部门名称 */
    collectorUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: number;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: number;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 使用人ID */
    materialUser: number;

    /* 使用人姓名 */
    materialUserName: string;

    /* 使用人部门ID */
    materialUserDeptId: number;

    /* 使用人部门名称 */
    materialUserDeptName: string;

    /* 领料人确认方式值，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethod: string;

    /* 领料人确认方式标签，枚举WmInOutConfirmMethodEnums */
    collectorConfirmMethodLabel: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 是否代领 */
    isProxyExec: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 执行出库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: Record<string, unknown>;

    /* 是否自动完成出库 */
    isAutoIo: boolean;

    /* 备料状态值，枚举WmPreparationStateEnums */
    preparationState: string;

    /* 备料状态标签，枚举WmPreparationStateEnums */
    preparationStateLabel: string;

    /* 单据状态值，字典wmOutDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmOutDocStatus */
    docStatusLabel: string;
  }
}

/**
 * 查询出库单据分页列表
 */
export async function getOutBoundDocPage(
  params: OutboundDocumentsApi.OutboundDocumentsPageParams,
) {
  return requestClient.post<OutboundDocumentsApi.OutboundDocRecord>(
    `${warehousePath}/wm/outBound/getOutBoundDocPage`,
    { ...params },
  );
}
