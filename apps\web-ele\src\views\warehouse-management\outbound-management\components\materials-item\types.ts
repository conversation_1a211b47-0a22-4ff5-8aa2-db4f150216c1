export namespace MaterialItemData {
  /* 库位信息列表 */
  export interface LocationItemType {
    locationId: string;
    locationCode: string;
    locationName: string;
    unitPrice?: number;
    batchNumber: string;
    quantity: number;
  }

  /* 仓库信息列表 */
  export interface WarehouseGroup {
    warehouseId: string;
    warehouseCode: string;
    warehouseName: string;
    locationList: LocationItemType[];
  }

  /* 明细子项数据 */
  export interface Item {
    /* 批次号 */
    batchNumber: string;

    /* 库位编号 */
    locationCode: string;

    /* 库位id */
    locationId: string;

    /* 库位名称 */
    locationName: string;

    /* 均价（单价），默认不可见 */
    unitPrice?: number;

    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库id */
    warehouseId: string;

    /* 仓库名称 */
    warehouseName: string;

    /*  数量 */
    quantity: number;

    /* 批次号库存量 */
    inventory?: number;
  }

  /** 单据子项数据 */
  export interface DocItem {
    /* 明细列表 */
    itemList: Item[];

    /* 总数 */
    quantitySum: number;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料ID */
    materialId: string;

    /* 物料名称 */
    materialName: string;

    /* 物料别名 */
    materialAlias: string;

    /* 规格型号 */
    materialSpecs: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料图片ID */
    pictureFileId?: string;

    /* 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard: boolean;
  }
}
