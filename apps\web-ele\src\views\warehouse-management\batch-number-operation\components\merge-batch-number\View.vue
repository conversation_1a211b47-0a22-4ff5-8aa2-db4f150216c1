<script setup lang="ts">
import type { BatchNumberOperationQueryApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElMessage } from 'element-plus';

import { getBatchnumDocDetail } from '#/api/warehouse-management';

import Box from '../box.vue';

const props = defineProps({
  warehouseId: {
    type: String,
    default: '',
  },
  materialId: {
    type: String,
    default: '',
  },
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
  isView: {
    type: Boolean,
    default: false,
  },
});

const loading = ref(false);

const batchnumDocDetail =
  ref<BatchNumberOperationQueryApi.GetBatchnumDocDetailResponse>(
    {} as BatchNumberOperationQueryApi.GetBatchnumDocDetailResponse,
  );

const getBatchnumDocDetailHandle = async () => {
  try {
    loading.value = true;
    const res = await getBatchnumDocDetail({
      batchnumDocId: props.batchnumDocId,
      batchnumDocNumber: props.batchnumDocNumber,
      isQueryItem: true,
    });

    batchnumDocDetail.value = res;
    return res;
  } catch {
    ElMessage.error('获取批次号处理单据详情失败');
    return null;
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (isEmpty(props.batchnumDocId) && isEmpty(props.batchnumDocNumber)) {
    ElMessage.error('批次号处理单据ID或编号不能为空');
    return;
  }

  await getBatchnumDocDetailHandle();
});
</script>

<template>
  <div v-loading="loading">
    <Box title="合并前">
      <template
        v-for="(item, index) in batchnumDocDetail?.batchnumItemList"
        :key="item.batchnumItemId"
      >
        <div class="mb-2 flex items-center justify-around">
          <span class="ml-10 w-[20px]">{{ index + 1 }}</span>
          <div class="flex flex-1 items-center justify-around text-left">
            <span>
              库位批次：{{ `${item.locationName} (${item.itemBatchNumber})` }}
            </span>

            <span>数量：{{ item.itemQuantity }}</span>
          </div>
        </div>
      </template>
    </Box>
    <div class="flex items-center justify-center">
      <IconFont
        name="icon-arrow-bottom2"
        :size="15"
        class="!text-primary-500 my-2"
      />
    </div>
    <Box title="合并后">
      <div class="flex items-center justify-around">
        <span>库位：{{ batchnumDocDetail?.locationName || '/' }}</span>
        <span>批次号：{{ batchnumDocDetail?.mergeBatchNumber || '/' }}</span>
        <span>数量：{{ batchnumDocDetail?.mergeQuantity || '/' }}</span>
      </div>
    </Box>
  </div>
</template>
