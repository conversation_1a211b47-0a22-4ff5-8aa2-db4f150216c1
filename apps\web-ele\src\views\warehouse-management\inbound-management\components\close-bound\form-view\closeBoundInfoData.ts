import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'inOutCancelDocNumber',
      label: '取消申请单',
      formItemClass: 'col-span-3',
    },

    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'createUserName',
      label: '取消申请人',
      formItemClass: 'col-span-2',
    },

    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'createTime',
      label: '取消提交时间',
      formItemClass: 'col-span-2',
    },

    {
      component: 'Input',
      fieldName: 'docStatusLabel',
      label: '取消单状态',
      formItemClass: 'hidden',
    },

    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'inOutBoundDocNumber',
      label: '入库单号',
      formItemClass: 'col-span-3',
    },
    {
      component: (props: any) => {
        const val = props.modelValue;
        const showText = val || '/';
        return h('div', null, showText);
      },
      fieldName: 'origDocTypeName',
      label: '入库类型',
      formItemClass: 'col-span-2',
    },

    {
      component: 'Input',
      fieldName: 'remarkOptionList',
      formItemClass: 'col-span-full [&>div]:!overflow-visible mb-2',
      label: '取消原因',
    },
    {
      component: h(UploadFiles, {
        mode: 'readMode',
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber', // 绑定serialNumber进行回显
      fieldName: 'serialNumber',
      formItemClass: 'col-span-full',
      label: '附件',
    },

    {
      component: 'Input',
      fieldName: 'docProcess',
      label: '单据流程',
      formItemClass: 'col-span-full',
    },
  ];
}
