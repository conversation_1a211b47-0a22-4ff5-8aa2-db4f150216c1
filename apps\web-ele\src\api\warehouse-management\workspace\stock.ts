import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

// 统计入库单据数量
export async function getInBoundDocNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getInBoundDocNum`,
    params,
  );
}

// 统计入库实际明细子项数量
export async function getActualItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getActualItemNum`,
    params,
  );
}

// 统计入库单据实际明细物料总数
export async function getActualMaterialNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getActualMaterialNum`,
    params,
  );
}

// 统计已入库明细物料项数
export async function getActualMaterialItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getActualMaterialItemNum`,
    params,
  );
}

// 统计已入库明细物料总数
export async function getActualMaterialItem(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/getActualMaterialItem`,
    params,
  );
}
