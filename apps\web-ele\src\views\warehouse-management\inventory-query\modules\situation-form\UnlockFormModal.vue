<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import UnlockForm from '#/views/warehouse-management/inventory-lock/unlock-table/List.vue';
/** 共享数据 */
const shareData = ref();
const [UnlockModal, unlockModalApi] = useVbenModal({
  title: '锁库列表',
  footer: true,
  showCancelButton: false,
  showConfirmButton: true,
  closeOnClickModal: false,
  confirmText: '关闭',
  closable: false,
  closeOnPressEscape: false,
  onConfirm: () => {
    shareData.value.refreshForm();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = unlockModalApi.getData<Record<string, any>>();
    }
  },
});
onMounted(() => {});
</script>

<template>
  <UnlockModal>
    <UnlockForm
      :material-id="shareData.materialId"
      :warehouse-id="shareData.warehouseId"
    />
  </UnlockModal>
</template>
