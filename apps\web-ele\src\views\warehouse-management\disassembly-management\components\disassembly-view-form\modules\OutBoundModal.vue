<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import OnboundViewForm from '../components/outbound-view-form/index.vue';

/** 共享数据 */
const shareData = ref();

const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  closeOnClickModal: false,
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <OnboundViewForm
      :out-bound-doc-id="shareData.outBoundDocId"
      :out-bound-doc-number="shareData.outBoundDocNumber"
      :doc-status="shareData.docStatus"
    />
  </Modal>
</template>
