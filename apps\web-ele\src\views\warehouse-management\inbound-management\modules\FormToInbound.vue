<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { computed, defineAsyncComponent, markRaw, onMounted, ref } from 'vue';

import { ElMessage } from 'element-plus';

import { getInBoundDocDetail } from '#/api/warehouse-management/index';

const props = defineProps({
  inBoundDocId: {
    type: String,
    default: '',
  },
  inBoundDocNumber: {
    type: String,
    default: '',
  },
  docStatus: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['inboundSuccess', 'inboundError', 'inboundCancel']);

const formRef = ref();

const currentDocStatus = ref(props.docStatus);

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: false,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

onMounted(async () => {
  if (!currentDocStatus.value) {
    const inBoundRes = await getInBoundDocDetailHandle();
    if (inBoundRes) {
      currentDocStatus.value = inBoundRes.docStatus;
    }
  }
});

const currentComponent = computed(() => {
  switch (currentDocStatus.value) {
    // 待入库
    case 'pending': {
      return markRaw(defineAsyncComponent(() => import('./EditForm.vue')));
    }

    // 已入库
    case 'stocked': {
      return markRaw(defineAsyncComponent(() => import('./ViewForm.vue')));
    }

    // 已关闭、取消审核中
    default: {
      return markRaw(defineAsyncComponent(() => import('./ViewForm.vue')));
    }
  }
});

// 根据组件类型动态生成事件监听器
const componentEventListeners = computed(() => {
  // 只有在待入库状态（使用EditForm）时才绑定事件监听器
  if (currentDocStatus.value === 'pending') {
    return {
      onInboundSuccess: () => emits('inboundSuccess'),
      onInboundError: () => emits('inboundError'),
      onInboundCancel: () => emits('inboundCancel'),
    };
  }
  // 其他状态（使用ViewForm）时返回空对象
  return {};
});

const execInboundHandle = async () => {
  await formRef.value?.submitAllForm();
};

defineExpose({
  execInboundHandle,
});
</script>

<template>
  <component
    v-if="currentDocStatus"
    :is="currentComponent"
    ref="formRef"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    :doc-status="currentDocStatus"
    v-bind="componentEventListeners"
  />
</template>
