<script lang="ts" setup>
import type { MaterialPendingApi } from '#/api/warehouse-management/index';

import { ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  delPrepDoc,
  saveOrModPrepDoc,
  submitPrepDoc,
} from '#/api/warehouse-management/index';

import OutboundInfo from './outbound-info/index.vue';
import PrepDocInfo from './prepDoc-info/index.vue';
import PrepMaterialsInfo from './prepMaterials-info/index.vue';
import TargetLocationId from './target-locationId/index.vue';

const props = defineProps<{
  outBoundDocId: string;
  outBoundDocNumber: string;
  prepDocId?: string;
}>();

const emits = defineEmits([
  'boundSuccess',
  'loadDataSuccess',
  'loadDataError',
  'boundLoading',
  'handleCancel',
]);

const prepDocInfoRef = ref<InstanceType<typeof PrepDocInfo>>();
const prepMaterialsInfoRef = ref<InstanceType<typeof PrepMaterialsInfo>>();
const targetLocationIdRef = ref<InstanceType<typeof TargetLocationId>>();
const outboundInfoRef = ref<InstanceType<typeof OutboundInfo>>();

const targetWarehouseListData = ref<
  {
    locationId?: string;
    warehouseCode: string;
    warehouseId: string;
    warehouseName: string;
  }[]
>([]);

const targetWarehouseListChange = (
  newVal: {
    warehouseCode: string;
    warehouseId: string;
    warehouseName: string;
  }[],
) => {
  targetWarehouseListData.value = newVal;
};

/** 执行备料单据 */
const submitAllForm = async () => {
  try {
    const targetLocationList =
      (await targetLocationIdRef.value?.getTargetLocationList()) || [];

    const formData = await prepMaterialsInfoRef.value?.getSubData();

    if (formData?.length === 0 || targetLocationList.length === 0) {
      return false;
    } else {
      const mergeData = formData?.map((materialItem) => {
        const {
          warehouseId,
          actualQuantity,
          batchNumber,
          locationId,
          materialId,
        } = materialItem;

        const targetLocationData = targetLocationList.find(
          (locationItem) => locationItem.warehouseId === warehouseId,
        );

        const { locationId: targetLocationId } = targetLocationData || {};

        return {
          materialId,
          warehouseId,
          oldLocationId: locationId,
          targetLocationId,
          batchNumber,
          transferQuantity: actualQuantity,
        };
      });

      return mergeData;
    }
  } catch (error: any) {
    ElMessage.error(error.message);
  }
};

const submitAllFormHandle = async (type: string) => {
  const mergeData = await submitAllForm();

  if (!mergeData || mergeData.length === 0) {
    return false;
  }

  const params = {
    prepDocId: props.prepDocId,
    outBoundDocId: props.outBoundDocId,
    prepItemList: mergeData as MaterialPendingApi.PrepDocPrepItem[],
  };

  try {
    emits('boundLoading', true);
    switch (type) {
      case 'save': {
        await saveOrModPrepDoc(params);
        break;
      }
      case 'submit': {
        await submitPrepDoc(params);
        break;
      }
    }
    ElMessage.success('操作成功');
    emits('boundLoading', false);
    emits('boundSuccess');
  } catch {
    emits('boundLoading', false);
    ElMessage.error('操作失败');
  }
};

/** 删除单据 */
async function cancelInbound(id?: string) {
  await ElMessageBox.confirm('确定删除暂存的备料单吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  });
  try {
    emits('boundLoading', true);
    await delPrepDoc(id || props.prepDocId || '');
    ElMessage.success('删除成功');
    emits('boundLoading', false);
    emits('boundSuccess');
  } catch {
    emits('boundLoading', false);
    ElMessage.error('删除失败');
  }
}

const handleCancel = () => {
  emits('handleCancel');
};

/** 暴露方法 */
defineExpose({
  submitAllForm,
});
</script>

<template>
  <div class="relative mb-12 h-full">
    <PrepDocInfo
      v-if="props.prepDocId"
      ref="prepDocInfoRef"
      :prep-doc-id="props.prepDocId || ''"
    />

    <TargetLocationId
      ref="targetLocationIdRef"
      :target-warehouse-list-data="targetWarehouseListData"
      :prep-doc-id="props.prepDocId"
    />

    <OutboundInfo
      ref="outboundInfoRef"
      :out-bound-doc-id="props.outBoundDocId"
      :out-bound-doc-number="props.outBoundDocNumber"
    />

    <PrepMaterialsInfo
      ref="prepMaterialsInfoRef"
      :out-bound-doc-id="props.outBoundDocId"
      :out-bound-doc-number="props.outBoundDocNumber"
      :prep-doc-id="props.prepDocId || ''"
      @target-warehouse-list-change="targetWarehouseListChange"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="submitAllFormHandle('submit')"
        v-access:code="'wm:outbound:prep:submit'"
      >
        提交
      </ElButton>
      <ElButton
        type="primary"
        @click="submitAllFormHandle('save')"
        v-access:code="'wm:outbound:prep:submit'"
      >
        暂存
      </ElButton>
      <ElButton type="danger" @click="cancelInbound()" v-if="props.prepDocId">
        删除单据
      </ElButton>
    </div>
  </div>
</template>
