<script setup lang="ts">
import { onMounted, ref } from 'vue';

import locationFormEdit from './location-form/form-edit/index.vue';
import locationFormView from './location-form/form-view/index.vue';
import WarehouseFormEdit from './warehouse-form/form-edit/index.vue';
import WarehouseFormView from './warehouse-form/form-view/index.vue';

const props = defineProps({
  /** 当前表单 */
  form: {
    type: String,
    default: 'warehouse',
  },
  /** 当前id */
  currentId: {
    type: String,
    default: '',
  },
  /** 当前编号 */
  currentCode: {
    type: String,
    default: '',
  },
  /** 当前选中的仓库id */
  warehouseId: {
    type: String,
    default: '',
  },

  /** 是否是查看 */
  isView: {
    type: Boolean,
    default: false,
  },
});
/** 提交成功 */
const emits = defineEmits(['formSubmitSuccess']);
/** 表单组件 */
const locationRef = ref();
const thisView = ref(props.isView);
onMounted(() => {
  if (props.warehouseId) {
    locationRef.value?.formApi.setValues({
      warehouseId: props.warehouseId,
    });
  }
});
defineExpose({
  props,
});
</script>

<template>
  <ElCard
    class="!border-primary flex h-full flex-col justify-between !pb-[30px]"
    shadow="never"
    body-class="h-full !p-[10px] w-full"
    style="border: 1px solid"
  >
    <WarehouseFormEdit
      v-if="!thisView && form === 'warehouse'"
      :warehouse-id="currentId"
      :warehouse-code="currentCode"
      @form-submit-success="emits('formSubmitSuccess')"
      @cancel="thisView = true"
    />
    <WarehouseFormView
      v-if="thisView && form === 'warehouse'"
      :warehouse-id="currentId"
      :warehouse-code="currentCode"
      @form-submit-success="emits('formSubmitSuccess')"
      @edit="thisView = false"
    />
    <locationFormEdit
      v-if="!thisView && form === 'location'"
      ref="locationRef"
      :location-id="currentId"
      :location-code="currentCode"
      :is-view="isView"
      @cancel="thisView = true"
      @form-submit-success="emits('formSubmitSuccess')"
    />
    <locationFormView
      v-if="thisView && form === 'location'"
      ref="locationRef"
      :location-id="currentId"
      :location-code="currentCode"
      :is-view="isView"
      @edit="thisView = false"
      @form-submit-success="emits('formSubmitSuccess')"
    />
  </ElCard>
</template>
