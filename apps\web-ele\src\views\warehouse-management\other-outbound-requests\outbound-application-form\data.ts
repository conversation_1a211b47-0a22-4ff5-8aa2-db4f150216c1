import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { dictItemListType, DocStatus } from '#/api/common';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getDictItemList, getDocStatusInfo } from '#/api/common';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

import { docStatusDict } from '../config/list';

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        placeholder: '请输入,多个编号用回车分隔',
      },
      fieldName: 'inOutReqDocNumberList',
      label: '单据编号',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'submitUserList',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'submitTime',
      label: '提交时间',
      formItemClass: 'col-span-2',
    },
    {
      component: h(DeptStaffTree, {
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'materialUserList',
      label: '使用人',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'finishTime',
      label: '完成时间',
      formItemClass: 'col-span-2',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: DocStatus.DocStatusItem[]) => {
          const warehouseList = data.map((item) => ({
            label: item.docStatusLabel,
            value: item.docStatusKey,
          }));
          // 过滤待提交 pending
          const filterList = warehouseList.filter(
            (item) => item.value !== 'pending',
          );
          return filterList;
        },
        api: () => {
          return getDocStatusInfo('WM0033');
        },
        multiple: true,
        maxCollapseTags: 2,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docStatusList',
      label: '单据状态',
      formItemClass: 'col-span-1',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: dictItemListType[]) => {
          const warehouseList = data.map((item) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));
          return warehouseList;
        },
        api: () => {
          return getDictItemList('wmOtherOutReqType');
        },
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
      },
      fieldName: 'docCodeList',
      label: '出库类型',
      formItemClass: 'col-span-1',
    },
  ];
}

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 50,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'relative',
            },
            [
              h('span', row.inOutReqDocNumber),
              row.isRectify
                ? h(
                    ElTag,
                    {
                      type: 'primary',
                      class: 'ml-2 absolute top-1/2 right-0 -translate-y-1/2',
                    },
                    { default: () => '补录' },
                  )
                : null,
            ],
          );
        },
      },
      title: '单据编号',
      field: 'inOutReqDocNumber',
      minWidth: 235,
    },
    {
      title: '出库类型',
      field: 'docCodeLabel',
      minWidth: 100,
    },

    {
      slots: {
        default: ({ row }) =>
          h(
            ElTag,
            {
              size: 'small',
              type: docStatusDict[row.docStatus],
            },
            {
              default: () => row.docStatusLabel,
            },
          ),
      },
      title: '单据状态',
      field: 'docStatusLabel',
      minWidth: 110,
    },
    {
      slots: {
        default: 'submitUserName',
      },
      title: '申请人',
      field: 'submitUserName',
      minWidth: 150,
    },
    {
      title: '提交时间',
      field: 'submitTime',
      minWidth: 115,
    },
    {
      slots: {
        default: 'materialUserName',
      },
      title: '使用人',
      field: 'materialUserName',
      minWidth: 150,
    },
    {
      title: '完成时间',
      field: 'finishTime',
      minWidth: 115,
    },

    {
      align: 'center',
      slots: {
        default: 'CellOperation',
      },
      field: 'operation',
      fixed: 'right',
      minWidth: 110,
      title: '操作',
    },
  ];
}
