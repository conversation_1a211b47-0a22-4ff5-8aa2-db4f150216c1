<script setup lang="ts">
import { ref } from 'vue';

import { useAccess } from '@vben/access';
import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { modMaterialConfig } from '#/api/warehouse-management';

import ConfigFormEdit from './config-form/form-edit/index.vue';
import MaterialForm from './material-form/index.vue';

const { hasAccessByCodes } = useAccess();

/** 共享数据 */
const sharedData = ref();
const [Modal, modalApi] = useVbenModal({
  confirmText: '提交',
  footer: true,
  showCancelButton: true,
  closeOnClickModal: false,
  showConfirmButton: hasAccessByCodes(['wm:material:config:edit']),
  onCancel() {
    modalApi.close();
  },
  onConfirm() {
    onSubmit();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};
const loading = ref(false);
const materialRef = ref<InstanceType<typeof MaterialForm>>();
const configRef = ref<InstanceType<typeof ConfigFormEdit>>();

/** 确认框 */
const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};
/** 校验表单 */
const validateForm = async () => {
  // 校验表单
  const [verification, verification2] = await Promise.all([
    materialRef.value?.formApi.validate(),
    configRef.value?.formApi.validate(),
  ]);
  if (!verification?.valid || !verification2?.valid) {
    ElMessage.error('请填写完整表单');
    return false;
  }
  // 获取表单数据
  const configData = await configRef.value?.getAllFormValues();
  // 校验安全预警仓库的选择是否重复
  const hasDuplicate = configData?.safetyInventoryWarnList?.some(
    (item: { warehouseId: string }, index: any, arr: any[]) => {
      return arr.findIndex((i) => i.warehouseId === item.warehouseId) !== index;
    },
  );
  // 校验呆滞期分析仓库的选择是否重复
  const hasDuplicate2 = configData?.slowMovingAnalysisList?.some(
    (item: { warehouseId: string }, index: any, arr: any[]) => {
      return arr.findIndex((i) => i.warehouseId === item.warehouseId) !== index;
    },
  );
  if (hasDuplicate) {
    ElMessage.error('安全预警仓库不能重复');
    return false;
  }
  if (hasDuplicate2) {
    ElMessage.error('呆滞期分析仓库不能重复');
    return false;
  }
  return true;
};
/** 提交表单 */
const onSubmit = async () => {
  try {
    // 校验表单
    if (!(await validateForm())) return;
    // 获取表单数据
    const [data, data2] = await Promise.all([
      materialRef.value?.formApi.getValues(),
      configRef.value?.getAllFormValues(),
    ]);
    const isUpdate = !!sharedData.value.materialId;
    const confirmText = isUpdate ? '确认提交编辑吗？' : '确认提交新增吗？';
    if (await confirm(confirmText, '提示')) {
      loading.value = true;
      await modMaterialConfig({
        materialId: isUpdate ? sharedData.value.materialId : data!.materialId,
        ...data2,
      });
      ElMessage.success(isUpdate ? '编辑成功' : '添加成功');
      refreshList();
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

defineExpose({
  onSubmit,
  validateForm,
  materialRef,
  configRef,
});
</script>

<template>
  <Modal>
    <div v-loading="loading">
      <MaterialForm ref="materialRef" :material-id="sharedData.materialId" />
      <ConfigFormEdit
        ref="configRef"
        :material-id="sharedData.materialId"
        :material-config-data="sharedData.materialConfigData"
      />
    </div>
  </Modal>
</template>
