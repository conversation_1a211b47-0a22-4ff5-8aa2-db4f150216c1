<script setup lang="ts">
import type { PropType } from 'vue';

import type { DocOptionType } from '#/api';

import { onMounted, ref, watch } from 'vue';

import { ElCheckbox, ElCheckboxGroup } from 'element-plus';

import { getDocOptionList } from '#/api';

defineOptions({
  name: 'RemarkOptionSelect',
});
const props = defineProps({
  /**
   * 用于双向数据绑定的当前选中值
   */
  modelValue: {
    type: Array,
    default: () => [],
  },
  /** 单据标识 */
  docCode: {
    type: String,
    default: '',
  },
  /** 单据字段code */
  docFieldCode: {
    type: String,
    default: 'remark',
  },
  border: {
    type: Boolean,
    default: true,
  },
  size: {
    type: String as PropType<'default' | 'large' | 'small'>,
    default: 'small',
  },
});
const emits = defineEmits(['update:modelValue', 'change']);

/** 接口数据 */
const remarkOptionList = ref<DocOptionType[]>([]);
/** 原因子项数据 */
const remarkOption = ref<DocOptionType>();
/** 选项结果 */
const optionIdList = ref<number[]>();
/** 最大选择项 */
const selectedMax = ref(0);

onMounted(async () => {
  if (props.docCode) {
    await fetchRemarkOption();
  }
});

watch(
  () => props.modelValue,
  () => {
    optionIdList.value = props.modelValue as number[];
  },
);

/** 获取单据备注选项 */
const fetchRemarkOption = async () => {
  try {
    const res = await getDocOptionList({
      docCode: props.docCode,
      docFieldCode: props.docFieldCode,
    });
    remarkOptionList.value = res;
    remarkOption.value = remarkOptionList.value[0];

    selectedMax.value =
      remarkOption.value?.optionItemLimit === 0
        ? remarkOption.value?.optionItemList.length || 0
        : remarkOption.value?.optionItemLimit || 0;

    return res;
  } catch (error) {
    console.error(error);
  }
};

const refresh = () => {
  fetchRemarkOption();
  optionIdList.value = [];
  emits('update:modelValue', []);
  emits('change', [], false);
};

watch(() => props.docCode, refresh);

const onChange = (optionIds: any[]) => {
  emits('update:modelValue', optionIds);
  const isDescRequired = remarkOption.value?.optionItemList
    .filter((item) => optionIds.includes(item.optionId))
    .some((item) => item.isActiveDescRequired);

  emits('change', optionIds, isDescRequired);
};

defineExpose({
  /** 单据备注选项 */
  remarkOptionList,
});
</script>

<template>
  <ElCheckboxGroup
    v-model="optionIdList"
    :size="size"
    @change="onChange"
    :max="selectedMax"
    class="remark-option-select"
  >
    <ElCheckbox
      v-for="item in remarkOption?.optionItemList"
      :key="item.optionId"
      :label="item.optionName"
      :value="item.optionId"
      :border="border"
    />
  </ElCheckboxGroup>
</template>
<style scoped lang="scss">
.remark-option-select {
  .el-checkbox {
    padding: 0 11px !important;
    margin-right: 15px !important;

    &:last-of-type {
      margin-right: 0 !important;
    }

    ::v-deep(.el-checkbox__input) {
      display: none !important;
    }

    ::v-deep(.el-checkbox__label) {
      padding: 0 !important;
    }
  }
}
</style>
