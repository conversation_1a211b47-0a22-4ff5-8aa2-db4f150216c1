<script lang="ts" setup>
import { defineProps } from 'vue';

defineProps({
  title: {
    default: '标题',
    type: String,
  },
});
</script>
<template>
  <div
    class="rounded-lg border border-gray-200 bg-white p-3 transition-all duration-300 hover:shadow-[0px_0px_10px_5px_rgba(156,163,175,0.15)]"
  >
    <div class="mb-1 flex h-6 items-center" v-if="title">
      <div class="min-w-0 flex-1">
        <slot name="title">
          <span class="font-black">{{ title }}</span>
        </slot>
      </div>
      <div class="ml-2 shrink-0">
        <slot name="titleMore"></slot>
      </div>
    </div>
    <div>
      <slot name="default"></slot>
    </div>
  </div>
</template>
