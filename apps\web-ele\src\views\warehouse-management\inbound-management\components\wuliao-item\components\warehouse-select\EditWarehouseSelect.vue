<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../../types';

import { ref, watch } from 'vue';

const props = defineProps({
  warehouseItemData: {
    type: Object as PropType<MaterialItem.WarehouseItemDataType>,
    default: () => ({}),
  },
  selectWarehouseList: {
    type: Array as PropType<MaterialItem.SelectWarehouseListType[]>,
    default: () => [],
  },
  inChangeWarehouse: {
    type: Boolean,
    default: true,
  },
});

const emits = defineEmits(['warehouseChange']);

// 当前选择的仓库id
const currentWarehouseId = ref<string>(props.warehouseItemData.warehouseId);

watch(
  () => currentWarehouseId.value,
  (newVal) => {
    emits('warehouseChange', newVal);
  },
);
</script>

<template>
  <div class="flex w-full items-center">
    <div class="text-sm text-gray-700">仓库：</div>
    <ElSelect
      v-model="currentWarehouseId"
      class="!w-full flex-1"
      placeholder="请选择仓库"
      :disabled="!inChangeWarehouse"
    >
      <ElOption
        v-for="item in selectWarehouseList"
        :key="item.value"
        :label="item.label"
        :value="item.value"
        :disabled="item.disabled"
      />
    </ElSelect>
  </div>
</template>
