<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../types';

import { ImageViewer } from '@girant-web/img-view-component';

const props = defineProps({
  isShowQuantity: {
    type: Boolean,
    default: true,
  },
  materialItemInfo: {
    type: Object as PropType<MaterialItem.MaterialInfo>,
    default: () => ({}),
  },
});

const emits = defineEmits(['handleMaterialCode']);

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemInfo.materialId);
};
</script>

<template>
  <div class="ml-4 flex flex-1 justify-between">
    <div class="flex items-center space-x-2">
      <div>
        <span class="text-primary-500 text-lg font-medium">
          {{ materialItemInfo.materialName }}
        </span>

        <span
          class="text-primary-500 ml-2 mr-2 text-sm font-medium"
          v-if="materialItemInfo.materialAlias"
        >
          {{ `(${materialItemInfo.materialAlias})` }}
        </span>
      </div>

      <ElButton type="info" link class="underline" @click="handleMaterialCode">
        {{ materialItemInfo.materialCode }}
      </ElButton>

      <ImageViewer
        v-if="materialItemInfo.pictureFileId"
        :img-id="materialItemInfo.pictureFileId"
        img-css="size-7 flex-shrink-0"
        :show-thumbnail="false"
      />

      <ElTag
        class="!border-blue-500 !bg-blue-500 text-white"
        size="small"
        effect="dark"
        v-if="materialItemInfo.materialType === '20'"
      >
        原料
      </ElTag>

      <ElTag
        type="danger"
        size="small"
        effect="dark"
        v-if="!materialItemInfo.isStandard"
      >
        非标
      </ElTag>

      <span class="text-sm text-gray-500">
        规格型号： {{ materialItemInfo.materialSpecs || '/' }}
      </span>
    </div>
    <div class="text-sm text-gray-600">
      <slot name="extra" :material-item-info="materialItemInfo"></slot>
      <template v-if="!$slots.extra && isShowQuantity">
        <div>
          <span>入库合计：</span>
          <span class="font-medium">{{ materialItemInfo.quantitySum }}</span>
          <span class="text-gray-500">
            ({{ materialItemInfo.baseUnitLabel || '/' }})
          </span>
        </div>
      </template>
    </div>
  </div>
</template>
