import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';

export namespace BaseDataDeptApi {
  export interface BaseDataDept {
    [key: string]: any;
  }
  export interface deptTreeType {
    /** 子部门 */
    children?: deptTreeType[];
    /** 部门编号 */
    deptCode: string;
    /** 部门id */
    deptId: string;
    /** 部门名称 */
    deptName: string;
    /** 是否启用 */
    isEnable: boolean;
    /** 岗位列表 */
    positionList?: {
      /** 岗位编码 */
      positionCode: string;
      /** 岗位id */
      positionId: string;
      /** 岗位名称 */
      positionName: string;
    }[];
    /** 员工信息列表 */
    staffList?: {
      /** 员工编号 */
      staffCode: string;
      /** 员工id */
      staffId: number;
      /** 员工姓名 */
      staffName: string;
    }[];
  }
}

/**  查询所有部门树 */
export async function getAllDeptTree(data: any) {
  return requestClient.post<BaseDataDeptApi.deptTreeType[]>(
    `${baseDataPath}/base/dept/getAllDeptTree`,
    data,
  );
}
