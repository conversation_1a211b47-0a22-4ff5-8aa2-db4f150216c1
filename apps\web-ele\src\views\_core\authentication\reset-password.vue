<script setup lang="ts">
import { z } from '@vben/common-ui';
import { $t } from '@vben/locales';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import { resetPassword } from '#/api';

defineOptions({ name: 'ResetPassword' });
const emits = defineEmits(['passwordResetSuccess']);
const [Form, formApi] = useVbenForm({
  commonConfig: {
    // 所有表单项
    componentProps: {
      class: 'w-full',
    },
  },
  // 提交函数
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: '请输入当前密码',
        autocomplete: 'current-password',
      },
      fieldName: 'oldPassword',
      label: '当前密码',
      rules: z.string().min(1, { message: '请输入当前密码' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        passwordStrength: true,
        placeholder: '请输入新密码',
        autocomplete: 'new-password',
      },
      fieldName: 'newPassword',
      label: '新密码',
      rules: z
        .string()
        .min(6, { message: '最少输入6个字符' })
        .max(64, { message: '最多输入64个字符' }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: '请输入确认密码',
        autocomplete: 'confirm-password',
      },
      dependencies: {
        rules(values) {
          const { newPassword } = values;
          return z
            .string({ required_error: '请输入确认密码' })
            .min(1, { message: '请输入确认密码' })
            .refine((value) => value === newPassword, {
              message: $t('authentication.confirmPasswordTip'),
            });
        },
        triggerFields: ['newPassword'],
      },
      fieldName: 'confirmPassword',
      label: '确认密码',
    },
  ],
  showDefaultActions: false,
});

async function onSubmit(values: Record<string, any>) {
  ElMessageBox.confirm('确定修改密码吗？', '提示', {
    type: 'warning',
  })
    .then(async () => {
      await resetPassword({
        oldPassword: values.oldPassword,
        newPassword: values.newPassword,
      });

      ElMessage.success('密码修改成功');
      emits('passwordResetSuccess');
      // ElMessageBox.alert('密码修改成功，请重新登录', '提示', {
      //   confirmButtonText: '重新登录',
      //   showClose: false,
      //   callback: async () => {
      //     const authStore = useAuthStore();
      //     await authStore.logout();
      //   },
      // });
    })
    .catch(() => {});
}

defineExpose({
  formApi,
});
</script>
<template>
  <Form />
</template>
