<script setup lang="ts">
import { computed, ref } from 'vue';

import { ElMessage, ElTable, ElTableColumn } from 'element-plus';

import {
  getInvcByWarehouseIdAndMaterialId,
  getInventoryChangeList,
  getOutBoundDocDetailByOrigDoc,
  getWarehouseDetail,
} from '#/api';
import TipsPopover from '#/components/tips-popover/index.vue';

const props = defineProps({
  /** 库存调拨数据 */
  transferDocDetail: {
    type: [Object, null],
    default: null,
  },
  /** 物料Id */
  materialId: {
    type: String,
    default: '',
  },
  /** 调拨数量 */
  transferQuantity: {
    type: Number,
    default: 0,
  },
  /** 调出仓库 */
  oldWarehouseId: {
    type: String,
    default: '',
  },
  /** 调入仓库 */
  targetWarehouseId: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 仓库库存数据 */
const tableData = ref<any>([]);
/** 调出仓库名称 */
const outWarehouseName = ref('');
/** 调入仓库名称 */
const inWarehouseName = ref('');
/** 调出仓库状态 */
const outWarehouseState = ref('');
/** 调入仓库状态 */
const inWarehouseState = ref('');
/** 调入仓库标题 */
const outWarehouseLabel = computed(() => {
  return outWarehouseState.value
    ? `${outWarehouseName.value}(${outWarehouseState.value})`
    : outWarehouseName.value;
});
/** 调入仓库标题 */
const inWarehouseLabel = computed(() => {
  return inWarehouseState.value
    ? `${inWarehouseName.value}(${inWarehouseState.value})`
    : inWarehouseName.value;
});
/** 空表格提示 */
const emptyText = ref('');

/** 清空表格内容 */
const clearTableContent = () => {
  tableData.value = [];
  outWarehouseName.value = '';
  inWarehouseName.value = '';
  outWarehouseState.value = '';
  inWarehouseState.value = '';
};

/** 查询仓库详情信息 */
const queryWarehouseDetails = async (
  oldWarehouseId: string,
  targetWarehouseId: string,
) => {
  const oldWarehouse = await getWarehouseDetail(oldWarehouseId);
  outWarehouseName.value = oldWarehouse?.warehouseName ?? '';
  const targetWarehouse = await getWarehouseDetail(targetWarehouseId);
  inWarehouseName.value = targetWarehouse?.warehouseName ?? '';
};

/** 查询的当前库存 */
const queryCurrentInventory = async (
  materialId: string,
  oldWarehouseId: string,
  targetWarehouseId: string,
  showTransfer: boolean = false,
) => {
  // 获取调出仓库库存
  const outWarehouse = await getInvcByWarehouseIdAndMaterialId(
    oldWarehouseId,
    materialId,
    false,
    false,
    false,
  );
  // 获取调入仓库库存
  const inWarehouse = await getInvcByWarehouseIdAndMaterialId(
    targetWarehouseId,
    materialId,
    false,
    false,
    false,
  );

  tableData.value = [
    {
      indicator: '当前库存量/可用量',
      warehouseA: {
        stock: outWarehouse?.inventory ?? 0,
        available: outWarehouse?.availableInventory ?? 0,
      },
      warehouseB: {
        stock: inWarehouse?.inventory ?? 0,
        available: inWarehouse?.availableInventory ?? 0,
      },
    },
  ];

  if (showTransfer) {
    tableData.value = [
      ...tableData.value,
      {
        indicator: '调拨后库存量/可用量',
        warehouseA: {
          stock: (outWarehouse?.inventory ?? 0) - props.transferQuantity,
          available:
            (outWarehouse?.availableInventory ?? 0) - props.transferQuantity,
          change: -props.transferQuantity,
        },
        warehouseB: {
          stock: (inWarehouse?.inventory ?? 0) + props.transferQuantity,
          available:
            (inWarehouse?.availableInventory ?? 0) + props.transferQuantity,
          change: props.transferQuantity,
        },
      },
    ];
  }
};

/** 查询待入库变动库存 */
const queryStockPendingInventory = async () => {
  // 获取调出仓库库存
  const outWarehouse = await getInvcByWarehouseIdAndMaterialId(
    props.transferDocDetail?.oldWarehouseId,
    props.materialId,
    false,
    false,
    false,
  );
  // 获取调入仓库库存
  const inWarehouse = await getInvcByWarehouseIdAndMaterialId(
    props.transferDocDetail?.targetWarehouseId,
    props.materialId,
    false,
    false,
    false,
  );
  // 获取调出仓库历史变动
  const outWarehouses = await getInventoryChangeList({
    materialIdList: [props.materialId],
    warehouseIdList: [props.transferDocDetail?.oldWarehouseId],
    origDocIdList: [props.transferDocDetail?.transferDocId],
  });

  // 赋值
  tableData.value = [
    {
      indicator: '当前库存量/可用量',
      warehouseA: {
        stock: outWarehouse?.inventory ?? 0,
        available: outWarehouse?.availableInventory ?? 0,
      },
      warehouseB: {
        stock: inWarehouse?.inventory ?? 0,
        available: inWarehouse?.availableInventory ?? 0,
      },
    },
    {
      indicator: '调拨前库存量/可用量',
      warehouseA: {
        stock: outWarehouses[0]?.oldInventory ?? 0,
        available: outWarehouses[0]?.oldAvailableInventory ?? 0,
      },
      warehouseB: {
        show: false,
      },
    },
    {
      indicator: '调拨后库存量/可用量',
      warehouseA: {
        stock: outWarehouses[0]?.newInventory ?? 0,
        available: outWarehouses[0]?.newAvailableInventory ?? 0,
        change: -props.transferQuantity,
      },
      warehouseB: {
        stock: (inWarehouse?.inventory ?? 0) + props.transferQuantity,
        available:
          (inWarehouse?.availableInventory ?? 0) + props.transferQuantity,
        change: props.transferQuantity,
      },
    },
  ];
};

/** 查询历史变动库存 */
const queryHistoricalInventory = async () => {
  // 获取调出仓库历史变动
  const outWarehouses = await getInventoryChangeList({
    materialIdList: [props.materialId],
    warehouseIdList: [props.transferDocDetail?.oldWarehouseId],
    origDocIdList: [props.transferDocDetail?.transferDocId],
  });
  // 获取调入仓库历史变动
  const inWarehouses = await getInventoryChangeList({
    materialIdList: [props.materialId],
    warehouseIdList: [props.transferDocDetail?.targetWarehouseId],
    origDocIdList: [props.transferDocDetail?.transferDocId],
  });
  // 赋值
  tableData.value = [
    {
      indicator: '调拨前库存量/可用量',
      warehouseA: {
        stock: outWarehouses[0]?.oldInventory ?? 0,
        available: outWarehouses[0]?.oldAvailableInventory ?? 0,
      },
      warehouseB: {
        stock: inWarehouses[0]?.oldInventory ?? 0,
        available: inWarehouses[0]?.oldAvailableInventory ?? 0,
      },
    },
    {
      indicator: '调拨后库存量/可用量',
      warehouseA: {
        stock: outWarehouses[0]?.newInventory ?? 0,
        available: outWarehouses[0]?.newAvailableInventory ?? 0,
        change: -props.transferQuantity,
      },
      warehouseB: {
        stock: inWarehouses[0]?.newInventory ?? 0,
        available: inWarehouses[0]?.newAvailableInventory ?? 0,
        change: props.transferQuantity,
      },
    },
  ];
};

/** 查询取消、关闭入库变动库存 */
const queryCancelInventory = async () => {
  // 获取调出仓库历史变动
  const outWarehouses = await getInventoryChangeList({
    materialIdList: [props.materialId],
    warehouseIdList: [props.transferDocDetail?.oldWarehouseId],
    origDocIdList: [props.transferDocDetail?.transferDocId],
  });

  // 赋值
  tableData.value = [
    {
      indicator: '调拨前库存量/可用量',
      warehouseA: {
        stock: outWarehouses[0]?.oldInventory ?? 0,
        available: outWarehouses[0]?.oldAvailableInventory ?? 0,
      },
      warehouseB: {
        show: false,
      },
    },
    {
      indicator: '调拨后库存量/可用量',
      warehouseA: {
        stock: outWarehouses[0]?.newInventory ?? 0,
        available: outWarehouses[0]?.newAvailableInventory ?? 0,
        change: -props.transferQuantity,
      },
      warehouseB: {
        show: false,
      },
    },
  ];
};

/** 获取出库单数据 */
const getOutBoundDoc = async () => {
  const res = await getOutBoundDocDetailByOrigDoc(
    props.transferDocDetail?.transferDocId,
    props.transferDocDetail?.transferDocNumber,
  );

  if (res && res.docStatus === 'collected') {
    // 已完成，查询历史变动库存
    outWarehouseState.value = '已出库';
    inWarehouseState.value = '未入库';
    queryCancelInventory();
  } else {
    outWarehouseState.value = '未出库';
    inWarehouseState.value = '未入库';
    tableData.value = [
      {
        indicator: '调拨前库存量/可用量',
        warehouseA: {
          show: false,
        },
        warehouseB: {
          show: false,
        },
      },
      {
        indicator: '调拨后库存量/可用量',
        warehouseA: {
          show: false,
        },
        warehouseB: {
          show: false,
        },
      },
    ];
  }
};

/** 库存调拨物料仓库分析 */
const showParentStock = async () => {
  try {
    loading.value = true;
    if (props.transferDocDetail) {
      // 已有单据数据
      outWarehouseName.value = props.transferDocDetail.oldWarehouseName;
      inWarehouseName.value = props.transferDocDetail.targetWarehouseName;

      const status = props.transferDocDetail.docStatus;
      if (['awaitOut', 'checking'].includes(status)) {
        await queryCurrentInventory(
          props.materialId,
          props.transferDocDetail.oldWarehouseId,
          props.transferDocDetail.targetWarehouseId,
          true,
        );
      } else if (['awaitWarehousing'].includes(status)) {
        outWarehouseState.value = '已出库';
        inWarehouseState.value = '未入库';
        await queryStockPendingInventory();
      } else if (['finish'].includes(status)) {
        outWarehouseState.value = '已出库';
        inWarehouseState.value = '已入库';
        await queryHistoricalInventory();
      } else if (['cancelAudit', 'closed'].includes(status)) {
        // 根据出库单据状态显示是否已出库
        await getOutBoundDoc();
      }
    } else {
      // 新增单据
      if (
        !props.materialId ||
        !props.oldWarehouseId ||
        !props.targetWarehouseId
      ) {
        clearTableContent();
        if (!props.materialId) {
          emptyText.value = '请选择物料';
        } else if (!props.oldWarehouseId) {
          emptyText.value = '请选择调出仓库';
        } else if (!props.targetWarehouseId) {
          emptyText.value = '请选择调入仓库';
        }
        return;
      }
      // 获取仓库名
      await queryWarehouseDetails(
        props.oldWarehouseId,
        props.targetWarehouseId,
      );
      // 获取仓库实时库存情况
      await queryCurrentInventory(
        props.materialId,
        props.oldWarehouseId,
        props.targetWarehouseId,
      );
    }
  } catch {
    ElMessage.error('加载库存信息失败');
  } finally {
    loading.value = false;
  }
};
</script>

<template>
  <TipsPopover
    @show="showParentStock"
    placement="bottom-start"
    icon-size="24px"
    width="430px"
  >
    <div v-loading="loading">
      <ElTable
        :data="tableData"
        header-align="center"
        style="width: 100%"
        size="small"
      >
        <template #empty>
          <span v-if="emptyText" class="text-red-500">{{ emptyText }}</span>
          <span v-else>无调拨数据</span>
        </template>
        <!-- 指标列（行标题）-->
        <ElTableColumn label="" prop="indicator" width="160" />
        <!-- 仓库列：调出仓库 -->
        <ElTableColumn :label="outWarehouseLabel" prop="warehouseA" width="120">
          <template #default="scope">
            <div v-if="scope.row.warehouseA.show ?? true">
              <span>
                <span
                  :class="{
                    'text-red-500': scope.row.warehouseA.stock <= 0,
                  }"
                >
                  {{ scope.row.warehouseA.stock }}
                </span>
                /
                <span
                  :class="{
                    'text-red-500': scope.row.warehouseA.available <= 0,
                  }"
                >
                  {{ scope.row.warehouseA.available }}
                </span>
              </span>
              <template v-if="scope.row.warehouseA.change !== undefined">
                (
                <span
                  class="change-tag"
                  :class="{
                    'text-red-500': scope.row.warehouseA.change < 0,
                    'text-green-500': scope.row.warehouseA.change > 0,
                  }"
                >
                  {{ scope.row.warehouseA.change > 0 ? '+' : '' }}
                  {{ scope.row.warehouseA.change }}
                </span>
                )
              </template>
            </div>
            <div v-else>-</div>
          </template>
        </ElTableColumn>
        <!-- 仓库列：调入仓库 -->
        <ElTableColumn :label="inWarehouseLabel" prop="warehouseB" width="120">
          <template #default="scope">
            <div v-if="scope.row.warehouseB.show ?? true">
              <span>
                <span
                  :class="{
                    'text-red-500': scope.row.warehouseB.stock <= 0,
                  }"
                >
                  {{ scope.row.warehouseB.stock }}
                </span>
                /
                <span
                  :class="{
                    'text-red-500': scope.row.warehouseB.available <= 0,
                  }"
                >
                  {{ scope.row.warehouseB.available }}
                </span>
              </span>
              <template v-if="scope.row.warehouseB.change !== undefined">
                (
                <span
                  class="change-tag"
                  :class="{
                    'text-red-500': scope.row.warehouseB.change < 0,
                    'text-green-500': scope.row.warehouseB.change > 0,
                  }"
                >
                  {{ scope.row.warehouseB.change > 0 ? '+' : '' }}
                  {{ scope.row.warehouseB.change }}
                </span>
                )
              </template>
            </div>
            <div v-else>-</div>
          </template>
        </ElTableColumn>
      </ElTable>
    </div>
  </TipsPopover>
</template>
