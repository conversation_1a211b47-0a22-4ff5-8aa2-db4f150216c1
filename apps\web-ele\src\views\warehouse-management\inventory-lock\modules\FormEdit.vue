<script setup lang="ts">
import { ref } from 'vue';

import { useAccess } from '@vben/access';
import { useVbenModal } from '@vben/common-ui';

import LockForm from './lock-form/index.vue';

const { hasAccessByCodes } = useAccess();
/** 锁库表单ref */
const lockFormRef = ref();
/** 共享数据 */
const sharedData = ref();
const [EditModal, editModalApi] = useVbenModal({
  title: '新增锁库',
  showConfirmButton: hasAccessByCodes(['wm:inventory:lock:lock']),
  cancelText: '取消',
  confirmText: '提交',
  closeOnClickModal: false,
  onConfirm: () => {
    lockFormRef.value?.onSubmit();
  },
  onCancel() {
    editModalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = editModalApi.getData<Record<string, any>>();
    }
  },
});
/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  editModalApi.close();
};
/** 提交表单 新增 */
const onSubmit = async () => {
  lockFormRef.value!.formApi.validateAndSubmitForm();
};

defineExpose({
  onSubmit,
});
</script>

<template>
  <EditModal>
    <LockForm ref="lockFormRef" @submit-success="refreshList()" />
  </EditModal>
</template>
