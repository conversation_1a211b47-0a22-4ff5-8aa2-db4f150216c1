<script setup lang="ts">
import ViewInboundInfo from '../components/view-inbound-info/index.vue';
import ViewMaterialsList from './materials-list/ViewForm/index.vue';

defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  docStatus: {
    default: '',
    type: String,
  },
});
</script>
<template>
  <ViewInboundInfo
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    :doc-status="docStatus"
  />

  <ViewMaterialsList
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
  />
</template>
