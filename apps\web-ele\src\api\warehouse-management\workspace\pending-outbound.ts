import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

// 统计出库申请明细项
export async function getOutApplyItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getApplyItemNum`,
    params,
  );
}

// 统计出库申请明细物料项
export async function getOutApplyMaterialItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getApplyMaterialItemNum`,
    params,
  );
}

// 统计出库申请明细物料总数
export async function getOutApplyMaterialNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getApplyMaterialNum`,
    params,
  );
}
