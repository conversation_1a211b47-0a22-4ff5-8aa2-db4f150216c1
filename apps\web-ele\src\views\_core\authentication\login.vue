<script lang="ts" setup>
import type { VbenFormSchema } from '@vben/common-ui';
import type { Recordable } from '@vben/types';

import { computed, onMounted, ref } from 'vue';

import { AuthenticationLogin, z } from '@vben/common-ui';
import { $t } from '@vben/locales';
import { preferences } from '@vben/preferences';

import { getCaptchaApi } from '#/api';
import { useAuthStore } from '#/store';

defineOptions({ name: 'Login' });
const title = computed(() => preferences.login.title);
const subTitle = computed(() => preferences.login.subTitle);
const showForgetPassword = computed(() => preferences.login.showForgetPassword);
const showRegister = computed(() => preferences.login.showRegister);
const showRememberMe = computed(() => preferences.login.showRememberMe);

const authStore = useAuthStore();

// 定义验证码图片的base64数据
const captchaImage = ref<string>('');
// 定义验证码ID，用于验证时的标识
const captchaId = ref<string>('');

// 获取验证码图片的方法
async function fetchCaptcha() {
  try {
    const { captchaImg, captchaKey } = await getCaptchaApi();
    captchaImage.value = `data:image/jpg;base64,${captchaImg}`;
    captchaId.value = captchaKey;
  } catch (error) {
    console.error('获取验证码失败', error);
  }
}

// 在组件挂载时获取验证码
onMounted(() => {
  fetchCaptcha();
});

const formSchema = computed((): VbenFormSchema[] => {
  return [
    {
      component: 'VbenInput',
      componentProps: {
        placeholder: $t('authentication.usernameTip'),
      },
      fieldName: 'username',
      label: $t('authentication.username'),
      rules: z.string().min(1, { message: $t('authentication.usernameTip') }),
    },
    {
      component: 'VbenInputPassword',
      componentProps: {
        placeholder: $t('authentication.password'),
      },
      fieldName: 'password',
      label: $t('authentication.password'),
      rules: z.string().min(1, { message: $t('authentication.passwordTip') }),
    },
    // 新增的验证码输入框
    // {
    //   component: 'VbenInput',
    //   componentProps: {
    //     placeholder: '请输入验证码',
    //   },
    //   suffix: () =>
    //     h(ImageComponent, {
    //       src: captchaImage.value,
    //       onClick: fetchCaptcha,
    //     }),
    //   fieldName: 'captcha',
    //   label: '验证码',
    //   rules: z.string().min(1, { message: '请输入验证码' }),
    // },
  ];
});

// 处理登录表单提交
const handleSubmit = (values: Recordable<any>) => {
  // 增加 captchaId 入参
  const newValues = { ...values, captchaKey: captchaId.value };
  authStore.authLogin(newValues);
};
</script>

<template>
  <AuthenticationLogin
    :form-schema="formSchema"
    :loading="authStore.loginLoading"
    :show-forget-password="showForgetPassword"
    :show-register="showRegister"
    :show-remember-me="showRememberMe"
    :title="title"
    :sub-title="subTitle"
    :show-third-party-login="false"
    :show-code-login="false"
    :show-qrcode-login="false"
    @submit="handleSubmit"
  />
</template>
