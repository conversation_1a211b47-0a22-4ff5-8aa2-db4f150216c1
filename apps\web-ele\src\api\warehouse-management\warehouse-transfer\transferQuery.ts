import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace TransferQueryApi {
  export interface TransferQueryPageParams {
    /** 仓内调拨单据编号列表 */
    transferDocNumberList: string;
    /** 所属仓库ID列表 */
    warehouseIdList: string;
    /** 提交人ID列表 */
    submitUserList: string;
    /** 执行人ID列表 */
    executorUserList: string;
    /** 关闭人ID列表 */
    closeUserList: string;
    /** 提交起始时间，时间格式：yyyy-MM-dd HH:mm */
    submitStartTime: string;
    /** 提交终止时间，时间格式：yyyy-MM-dd HH:mm */
    submitEndTime: string;
    /** 完成起始时间，时间格式：yyyy-MM-dd HH:mm */
    finishStartTime: string;
    /** 完成终止时间，时间格式：yyyy-MM-dd HH:mm */
    finishEndTime: string;
    /** 关闭起始时间，时间格式：yyyy-MM-dd HH:mm */
    closeStartTime: string;
    /** 关闭终止时间，时间格式：yyyy-MM-dd HH:mm */
    closeEndTime: string;
    /** 单据状态值，字典publicDocStatus */
    docStatus: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  export interface TransferQueryRecord {
    /* 仓内调拨单据ID */
    transferDocId: string;

    /* 仓内调拨单据编号 */
    transferDocNumber: string;

    /* 所属仓库ID */
    warehouseId: string;

    /* 所属仓库编号 */
    warehouseCode: string;

    /* 所属仓库名称 */
    warehouseName: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 执行人ID */
    executorUser: string;

    /* 执行人姓名 */
    executorUserName: string;

    /* 关闭人ID */
    closeUser: string;

    /* 关闭人姓名 */
    closeUserName: string;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;

    /* 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;

    /* 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: string;

    /* 审核状态，写入当前流程所在节点 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例id */
    processInstanceId: string;

    /* 单据状态值，字典publicDocStatus */
    docStatus: string;

    /* 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }

  export interface TransferItem {
    materialId: string;
    transferQuantity: number;
    targetLocationId: string;
    oldLocationId: string;
    batchNumber: string;
  }

  export interface SubmitTransferDocParams {
    transferDocId?: number;
    warehouseId: number;
    remark: string;
    remarkOptionList: string[];
    serialNumber: string;
    transferItemList: TransferItem[];
  }

  export interface GetTransferDocDetailParams {
    transferDocId?: string;
    transferDocNumber?: string;
    isQueryItem?: boolean;
  }

  export interface GetTransferDocDetailResponse {
    /* 仓内调拨单据ID */
    transferDocId: string;

    /* 仓内调拨单据编号 */
    transferDocNumber: string;

    /* 是否提交 */
    isSubmit: boolean;

    /* 所属仓库ID */
    warehouseId: string;

    /* 所属仓库编号 */
    warehouseCode: string;

    /* 所属仓库名称 */
    warehouseName: string;

    /* 创建人ID */
    createUser: string;

    /* 创建人姓名 */
    createUserName: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 执行人ID */
    executorUser: string;

    /* 执行人姓名 */
    executorUserName: string;

    /* 关闭人ID */
    closeUser: string;

    /* 关闭人姓名 */
    closeUserName: string;

    /* 创建时间，时间格式：yyyy-MM-dd HH:mm */
    createTime: string;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;

    /* 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;

    /* 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: string;

    /* 审核状态，写入当前流程所在节点 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例id */
    processInstanceId: string;

    /* 单据状态值，字典publicDocStatus */
    docStatus: string;

    /* 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;

    /* 备注(原因) */
    remark: string;

    /* 附件流水号 */
    serialNumber: string;

    /* 取消原因选项列表 */
    remarkOptionList: {
      /* 选项ID */
      optionId: string;

      /* 选项名称 */
      optionName: string;
    }[];

    /* 仓内调拨子项列表 */
    transferItemList: {
      /* 基本单位值，字典baseMaterialUnit */
      baseUnit: string;

      /* 基本单位标签，字典baseMaterialUnit */
      baseUnitLabel: string;

      /* 批次号 */
      batchNumber: string;

      /* 物料编号 */
      materialCode: string;

      /* 物料ID */
      materialId: string;

      /* 物料名称 */
      materialName: string;

      /* 规格型号 */
      materialSpecs: string;

      /* 原库位编号 */
      oldLocationCode: string;

      /* 原库位ID */
      oldLocationId: string;

      /* 原库位名称 */
      oldLocationName: string;

      /* 物料图片ID */
      pictureFileId: string;

      /* 目标库位编号 */
      targetLocationCode: string;

      /* 目标库位ID */
      targetLocationId: string;

      /* 目标库位名称 */
      targetLocationName: string;

      /* 仓内调拨子项ID */
      transferItemId: string;

      /* 调拨数量 */
      transferQuantity: number;

      /* 仓库编号 */
      warehouseCode: string;

      /* 仓库ID */
      warehouseId: string;

      /* 仓库名称 */
      warehouseName: string;
    }[];
  }

  export interface GetMyDraftDocPageParams {
    warehouseIdList: string;
    modifyStartTime: string;
    modifyEndTime: string;
    pageNum: number;
    pageSize: number;
  }

  export interface GetMyDraftDocPageResponse {
    /* 仓内调拨单据ID */
    transferDocId: string;

    /* 仓内调拨单据编号 */
    transferDocNumber: string;

    /* 所属仓库ID */
    warehouseId: string;

    /* 所属仓库编号 */
    warehouseCode: string;

    /* 所属仓库名称 */
    warehouseName: string;

    /* 创建人ID */
    createUser: string;

    /* 创建人姓名 */
    createUserName: string;

    /* 最后修改时间，时间格式：yyyy-MM-dd HH:mm */
    modifyTime: Record<string, unknown>;

    /* 单据状态值，字典publicDocStatus */
    docStatus: string;

    /* 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }
}

/**
 * 查询出库单据分页列表
 */
export async function getTransferDocPage(
  params: TransferQueryApi.TransferQueryPageParams,
) {
  return requestClient.post<Array<TransferQueryApi.TransferQueryRecord>>(
    `${warehousePath}/wm/stock/transfer/getTransferDocPage`,
    { ...params },
  );
}

/** 提交仓内调拨单据 */
export async function submitTransferDoc(
  params: TransferQueryApi.SubmitTransferDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/stock/transfer/submitTransferDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 暂存仓内调拨单据 */
export async function saveOrModTransferDoc(
  params: TransferQueryApi.SubmitTransferDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/stock/transfer/saveOrModTransferDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 获取仓内调拨单据详细信息 */
export async function getTransferDocDetail(
  params: TransferQueryApi.GetTransferDocDetailParams,
) {
  return requestClient.get<TransferQueryApi.GetTransferDocDetailResponse>(
    `${warehousePath}/wm/stock/transfer/getTransferDocDetail`,
    { params },
  );
}

/**
 * 查询待我提交的仓内调拨单据分页列表
 */
export async function getMyTransferDocPage(
  params: TransferQueryApi.GetMyDraftDocPageParams,
) {
  return requestClient.post<Array<TransferQueryApi.GetMyDraftDocPageResponse>>(
    `${warehousePath}/wm/stock/transfer/getMyDraftDocPage`,
    { ...params },
  );
}

/** 删除仓内调拨单据 （待提交）*/
export async function delTransferDoc(transferDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/stock/transfer/delTransferDoc/${transferDocId}`,
  );
}

/** 执行仓内调拨单据 */
export async function execTransferDoc(transferDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/stock/transfer/execTransferDoc/${transferDocId}`,
  );
}

/** 关闭仓内调拨单据 */
export async function closeTransferDoc(transferDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/stock/transfer/closeTransferDoc/${transferDocId}`,
  );
}

/** 导出仓内调拨单据列表*/
export async function exportTransferDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/stock/transfer/exportTransferDoc`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
