export namespace MaterialItem {
  /* 编辑状态右边库位数据类型 */
  export interface LocationItemType {
    locationId: string;
    unitPrice: number;
    batchNumber: string;
    quantity: number;
    locationCode?: string;
    locationName?: string;
  }

  /* 编辑状态仓库+右边库位数据类型 */
  export interface WarehouseItemDataType {
    warehouseId: string;
    locationList?: LocationItemType[];
    warehouseCode?: string;
    warehouseName?: string;
    timestamp: number;
  }

  export interface SelectWarehouseListType {
    disabled: boolean;
    label: string;
    value: string;
    /* 可用量 */
    availableInventory: number;
    /* 库存量 */
    inventory: number;
  }

  /* 共有的子项数据 */
  export interface Item {
    /* 批次号 */
    batchNumber?: string;

    /* 库位编号 */
    locationCode: string;

    /* 库位id */
    locationId: string;

    /* 库位名称 */
    locationName: string;

    /* 均价（单价），默认不可见 */
    unitPrice?: number;

    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库id */
    warehouseId: string;

    /* 仓库名称 */
    warehouseName: string;

    /* 数量 */
    quantity: number;
  }

  /** 入库单据子项 */
  export interface MaterialInfo {
    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料别名 */
    materialAlias: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 物料规格 */
    materialSpecs: string;

    /* 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard: boolean;

    /* 总数 */
    quantitySum: number;

    /* 明细列表 */
    itemList: Item[];
  }

  export interface MaterialItemData {
    /* 入库单据ID */
    inBoundDocId: string;

    /* 入库单据编号 */
    inBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: string;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: string;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: string;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: string;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 关闭人ID */
    closeUser: string;

    /* 关闭人姓名 */
    closeUserName: string;

    /* 关闭人部门ID */
    closeUserDeptId: string;

    /* 关闭人部门名称 */
    closeUserDeptName: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 是否自动完成入库 */
    isAutoIo: boolean;

    /* 是否允许变更仓库 */
    inChangeWarehouse: boolean;

    /* 入库数量策略值，枚举WmInOutQuantityLimit */
    inQuantityLimit: string;

    /* 入库数量策略标签，枚举WmInOutQuantityLimit */
    inQuantityLimitLabel: string;

    /* 申请时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: string;

    /* 执行入库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: string;

    /* 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: string;

    /* 单据状态值，字典wmInDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmInDocStatus */
    docStatusLabel: string;

    /* 备注 */
    remark: string;

    /* 附件流水号 */
    serialNumber: string;

    /* 入库单据子项 */
    inBoundItemList: MaterialInfo[];
  }
}
