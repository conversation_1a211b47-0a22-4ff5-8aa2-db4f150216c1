import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';

/** @description 获取部门人员树*/
export async function getDeptStaffList(params: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/dept/getDeptList`, {
    ...params,
  });
}

/** @description 查询可用的部门树*/
export async function getDeptStaffTree(params: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/dept/getDeptTree`, {
    ...params,
  });
}
