<script setup lang="ts">
import type { MaterialItemData } from '../../../components/materials-item/types';

import type {
  MaterialPendingApi,
  OutboundPendingApi,
  WarehouseListForMaterialListApi,
} from '#/api/warehouse-management/index';

import { onBeforeUnmount, onMounted, ref, watch } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElMessage } from 'element-plus';

import {
  getActiveWarehouseListByMaterialList,
  getOutboundDocDetail,
  getPrepDocDetail,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';

import MaterialsItem from '../../../components/materials-item/index.vue';

const props = defineProps({
  prepDocId: {
    type: String,
    default: '',
  },
  outBoundDocId: {
    type: String,
    default: '',
  },
  outBoundDocNumber: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['targetWarehouseListChange']);

const outboundData = ref<OutboundPendingApi.OutboundDocDetail>(
  {} as OutboundPendingApi.OutboundDocDetail,
);
const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

const docTypeCode = ref<string>('');

const prepItemList = ref<MaterialItemData.DocItem[]>([]);

const allWarehouseListForMaterialList = ref<
  WarehouseListForMaterialListApi.WarehouseListForMaterialList[]
>([]);

/** 获取出库数据 */
const getOutboundDocDetailHandle = async () => {
  try {
    const outboundRes = await getOutboundDocDetail({
      outBoundDocId: props.outBoundDocId,
      outBoundDocNumber: props.outBoundDocNumber,
      isQueryItem: true,
    });

    outboundData.value = outboundRes;
    return outboundRes;
  } catch {
    ElMessage.error('获取出库单据失败');
    return {} as OutboundPendingApi.OutboundDocDetail;
  }
};

/** 获取备料数据 */
const getPrepDocDetailHandle = async (prepDocId: string) => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId,
      isQueryItem: true,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
    return {} as MaterialPendingApi.PrepDocDetail;
  }
};

const materialsItemRef = ref<InstanceType<typeof MaterialsItem>[]>([]);

onMounted(async () => {
  if (props.outBoundDocId || props.outBoundDocNumber) {
    const data = await getOutboundDocDetailHandle();
    if (data) {
      docTypeCode.value = data.origDocTypeCode;
    }
  } else {
    ElMessage.error('出库单据ID或出库单号不能为空');
    return;
  }

  switch (isEmpty(props.prepDocId)) {
    case false: {
      if (props.prepDocId) {
        const data = await getPrepDocDetailHandle(props.prepDocId);
        if (data) {
          docTypeCode.value = data.origDocTypeCode;
        }
      }

      if (!prepDocDetail.value?.prepItemList) {
        prepItemList.value = [];
        return;
      }

      const newItemList =
        prepDocDetail.value.prepItemList.map(
          (item: MaterialPendingApi.PrepItemList) => {
            return {
              ...item,
              itemList: item.itemList.map(
                (subItem: MaterialPendingApi.ItemList) => {
                  const {
                    batchNumber,
                    oldLocationId,
                    oldLocationCode,
                    oldLocationName,
                    warehouseCode,
                    warehouseId,
                    warehouseName,
                    transferQuantity,
                  } = subItem;
                  return {
                    batchNumber: batchNumber || '',
                    locationCode: oldLocationCode || '',
                    locationId: oldLocationId || '',
                    locationName: oldLocationName || '',
                    warehouseCode: warehouseCode || '',
                    warehouseId: warehouseId || '',
                    warehouseName: warehouseName || '',
                    quantity: transferQuantity || 0,
                  };
                },
              ),
              quantitySum:
                outboundData.value.outBoundItemList?.find(
                  (outboundItem) => outboundItem.materialId === item.materialId,
                )?.applyQuantitySum || 0,
            };
          },
        ) || [];

      prepItemList.value = newItemList;

      break;
    }
    case true: {
      prepItemList.value = outboundData.value.outBoundItemList.map(
        (item: OutboundPendingApi.OutboundItem) => {
          return {
            ...item,
            quantitySum: item.applyQuantitySum,
            itemList: [],
          };
        },
      );
      break;
    }
  }
});

watch(
  () => prepItemList.value,
  async (newVal) => {
    if (newVal) {
      const params = {
        materialIdList: newVal.map((item) => item.materialId).join(','),
        docTypeCode: docTypeCode.value,
      };

      // 批量获取物料可入库的仓库列表
      const WForMListRes = await getActiveWarehouseListByMaterialList(params);

      allWarehouseListForMaterialList.value = WForMListRes;
    }
  },
);

// 获取所有表单数据
const getSubData = async (): Promise<
  {
    actualQuantity: number;
    batchNumber: string;
    locationId: string;
    materialId: string;
    warehouseId: string;
  }[]
> => {
  try {
    const subData = await Promise.all(
      materialsItemRef.value.flatMap((item) => {
        return item.getItemData();
      }),
    );
    return subData.flat();
  } catch (error) {
    ElMessage.error((error as Error).message);
    return [];
  }
};

const targetWarehouseList = ref<
  {
    warehouseCode: string;
    warehouseId: string;
    warehouseName: string;
  }[]
>([]);

let debounceTimer: null | ReturnType<typeof setTimeout> = null;

// 自定义防抖函数
const targetWarehouseListChange = () => {
  const data = materialsItemRef.value.map((item) => {
    if (item.MaterialsItemRef?.currentSelectedWarehouseList) {
      return item.MaterialsItemRef?.currentSelectedWarehouseList;
    }
    return [];
  });

  // 更新当前数据,先判断数据字段是否为空，再需要去重
  const map = new Map();
  data.flat().forEach((item) => {
    if (!map.has(item.warehouseId) && item.warehouseId) {
      map.set(item.warehouseId, item);
    }
  });
  targetWarehouseList.value = [...map.values()];

  // 清除已有定时器
  if (debounceTimer) clearTimeout(debounceTimer);
  // 设置新定时器
  debounceTimer = setTimeout(() => {
    emits('targetWarehouseListChange', targetWarehouseList.value);
    debounceTimer = null; // 执行后重置定时器
  }, 500); // 防抖延迟时间
};

// 组件卸载时清除定时器
onBeforeUnmount(() => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
    debounceTimer = null;
  }
});

defineExpose({
  getSubData,
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>备料明细</span>
    </template>

    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ prepItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template #default>
      <template
        v-for="(materialItem, index) in prepItemList"
        :key="materialItem.materialId"
      >
        <TriangleCard :number="index + 1" title="" class="mb-5">
          <template #content>
            <MaterialsItem
              ref="materialsItemRef"
              :material-item-data="materialItem"
              :orig-doc-type-code="prepDocDetail.origDocTypeCode"
              item-status="showEdit"
              :warehouse-list-for-material="
                allWarehouseListForMaterialList.find(
                  (item) => item.materialId === materialItem.materialId,
                )
              "
              @current-selected-warehouse-list-data-change="
                targetWarehouseListChange
              "
            />
          </template>
        </TriangleCard>
      </template>
    </template>
  </FormCard>
</template>
