<script lang="ts" setup>
import { defineProps } from 'vue';

defineProps({
  title: {
    default: '标题',
    type: String,
  },
  /** 是否显示footer */
  isFooter: {
    default: true,
    type: Boolean,
  },
});
</script>
<template>
  <div>
    <div
      class="bg-primary-50 text-primary mb-2 flex items-center justify-between rounded px-4 py-3"
    >
      <slot name="title">
        <span>{{ title }}</span>
      </slot>
      <slot name="titleMore"></slot>
    </div>
    <div>
      <slot name="default"></slot>
    </div>
    <div v-if="isFooter">
      <slot name="footer"></slot>
    </div>
  </div>
</template>
