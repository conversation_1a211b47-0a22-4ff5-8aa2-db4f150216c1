<script setup lang="ts">
import type { MaterialPendingApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getPrepDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  prepDocId: {
    type: String,
    default: '',
  },
});

const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 获取数据 */
const getPrepDocDetailHandle = async () => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId: props.prepDocId,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
    return {} as MaterialPendingApi.PrepDocDetail;
  }
};

onMounted(async () => {
  if (props.prepDocId) {
    const data = await getPrepDocDetailHandle();
    prepDocDetail.value = data;
    formApi.setValues(data);
  } else {
    ElMessage.error('备料单ID不能为空');
  }
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>备料单信息</span>
    </template>
    <template #default>
      <Form>
        <template #docProcess>
          <StepProgress
            v-if="prepDocDetail.prepDocNumber"
            :doc-number="prepDocDetail.prepDocNumber"
            class="min-h-[65px] overflow-x-auto"
          />
          <div v-else>/</div>
        </template>
      </Form>
    </template>
  </FormCard>
</template>
