<script setup lang="ts">
import { ref, watch } from 'vue';

import { ElDatePicker } from 'element-plus';

import { isAfter, isBefore, isSameDate, stringToDate } from '#/utils/dateUtils';

defineProps({
  /** 值的格式化字符串 */
  valueFormat: {
    type: String,
    default: 'YYYY-MM-DD HH:mm',
  },
  /** 显示的格式化字符串 */
  format: {
    type: String,
    default: 'YYYY-MM-DD HH:mm',
  },
  /** 时间的格式化字符串 */
  timeFormat: {
    type: String,
    default: 'HH:mm',
  },
});

// 定义默认的v-model，类型为包含开始时间和结束时间
const modelValue = defineModel<{
  // 结束时间
  endTime: Date | string | undefined;
  // 开始时间
  startTime: Date | string | undefined;
}>({
  default: () => ({}),
});

// 本地状态用于处理日期选择器的双向绑定
const localStartTime = ref<Date | string | undefined>(
  modelValue.value.startTime,
);
const localEndTime = ref<Date | string | undefined>(modelValue.value.endTime);

// 同步本地状态与modelValue
watch(localStartTime, (newVal) => {
  modelValue.value = { ...modelValue.value, startTime: newVal };
});

watch(localEndTime, (newVal) => {
  modelValue.value = { ...modelValue.value, endTime: newVal };
});

// 当外部modelValue变化时同步到本地状态
watch(modelValue, (newVal) => {
  localStartTime.value = newVal.startTime;
  localEndTime.value = newVal.endTime;
});

// 转换为Date对象的工具函数
const toDate = (value: Date | string | undefined): Date | null => {
  if (!value) return null;
  return typeof value === 'string' ? stringToDate(value) : value;
};

// 开始时间的日期禁用逻辑（与结束时间联动）
const disabledStartDate = (time: Date) => {
  const endTime = localEndTime.value || new Date('2099-12-31');
  if (isSameDate(time, endTime)) return false;
  return isAfter(time, endTime);
};

// 开始时间的小时禁用逻辑（与结束时间联动）
const disabledStartHours = () => {
  const start = toDate(localStartTime.value);
  const end = toDate(localEndTime.value);

  // 如果开始时间和结束时间是同一天，禁用结束时间之后的小时
  if (start && end && isSameDate(start, end)) {
    const endHour = end.getHours();
    // 禁用 endHour 之后的所有小时（例如结束时间是15点，则禁用16-23点）
    return Array.from({ length: 23 - endHour }, (_, i) => endHour + 1 + i);
  }

  return []; // 不同天则不禁用任何小时
};

// 开始时间的分钟禁用逻辑（与结束时间联动）
const disabledStartMinutes = (selectedHour: number) => {
  const start = toDate(localStartTime.value);
  const end = toDate(localEndTime.value);

  // 开始时间为空或与结束时间不同天/不同小时 → 不禁用分钟
  if (
    !start ||
    !end ||
    !isSameDate(start, end) ||
    end.getHours() !== selectedHour
  ) {
    return [];
  }

  // 同一天且同一小时 → 禁用结束时间之后的分钟
  const endMinute = end.getMinutes();
  return Array.from({ length: 59 - endMinute }, (_, i) => endMinute + 1 + i);
};

// 开始时间的秒禁用逻辑（与结束时间联动）
const disabledStartSeconds = (selectedHour: number, selectedMinute: number) => {
  const start = toDate(localStartTime.value);
  const end = toDate(localEndTime.value);

  // 开始时间为空或与结束时间不同天/不同小时/不同分钟 → 不禁用秒
  if (
    !start ||
    !end ||
    !isSameDate(start, end) ||
    end.getHours() !== selectedHour ||
    end.getMinutes() !== selectedMinute
  ) {
    return [];
  }

  // 同一天且同一小时且同一分钟 → 禁用结束时间之后的秒
  const endSecond = end.getSeconds();
  return Array.from({ length: 59 - endSecond }, (_, i) => endSecond + 1 + i);
};

// 结束时间的日期禁用逻辑（与开始时间联动）
const disabledEndDate = (time: Date) => {
  const startTime = localStartTime.value || new Date('1999-01-01');
  if (isSameDate(time, startTime)) return false;
  return isBefore(time, startTime);
};

// 结束时间的小时禁用逻辑（与开始时间联动）
const disabledEndHours = () => {
  const start = toDate(localStartTime.value);
  const end = toDate(localEndTime.value);

  // 如果开始时间和结束时间是同一天，禁用开始时间之前的小时
  if (start && end && isSameDate(start, end)) {
    const startHour = start.getHours();
    // 禁用 startHour 之前的所有小时（例如开始时间是10点，则禁用0-9点）
    return Array.from({ length: startHour }, (_, i) => i);
  }

  return []; // 不同天则不禁用任何小时
};

// 结束时间的分钟禁用逻辑（与开始时间联动）
const disabledEndMinutes = (selectedHour: number) => {
  const start = toDate(localStartTime.value);
  if (!start) return []; // 开始时间为空 → 不禁用分钟

  const end = toDate(localEndTime.value);
  // 不同天或结束时间小时 > 开始时间小时 → 不禁用分钟
  if (!end || !isSameDate(start, end) || selectedHour > start.getHours()) {
    return [];
  }

  // 同一小时 → 禁用开始时间之前的分钟
  if (selectedHour === start.getHours()) {
    const startMinute = start.getMinutes();
    return Array.from({ length: startMinute }, (_, i) => i);
  }

  return [];
};

// 结束时间的秒禁用逻辑（与开始时间联动）
const disabledEndSeconds = (selectedHour: number, selectedMinute: number) => {
  const start = toDate(localStartTime.value);
  if (!start) return []; // 开始时间为空 → 不禁用秒

  const end = toDate(localEndTime.value);
  // 不同天/不同小时/不同分钟 → 不禁用秒
  if (
    !end ||
    !isSameDate(start, end) ||
    selectedHour !== start.getHours() ||
    selectedMinute !== start.getMinutes()
  ) {
    return [];
  }

  // 同一小时且同一分钟 → 禁用开始时间之前的秒
  const startSecond = start.getSeconds();
  return Array.from({ length: startSecond }, (_, i) => i);
};
</script>

<template>
  <div class="flex items-center">
    <ElDatePicker
      v-model="localStartTime"
      type="datetime"
      placeholder="开始日期"
      :disabled-date="disabledStartDate"
      :disabled-hours="disabledStartHours"
      :disabled-minutes="disabledStartMinutes"
      :disabled-seconds="disabledStartSeconds"
      :value-format="valueFormat"
      :format="format"
      :time-format="timeFormat"
      class="!w-full"
    />
    <span class="px-[10px] text-[16px]">-</span>
    <ElDatePicker
      v-model="localEndTime"
      type="datetime"
      placeholder="结束日期"
      :disabled-date="disabledEndDate"
      :disabled-hours="disabledEndHours"
      :disabled-minutes="disabledEndMinutes"
      :disabled-seconds="disabledEndSeconds"
      :value-format="valueFormat"
      :format="format"
      :time-format="timeFormat"
      :default-time="new Date(2000, 1, 1, 23, 59, 59)"
      class="!w-full"
    />
  </div>
</template>
