<script setup lang="ts">
import type { VbenFormSchema } from '@girant/adapter';

import type { TransferQueryApi } from '#/api/warehouse-management';

import { h, onMounted, ref } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getTransferDocDetail } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

const props = defineProps({
  transferDocId: {
    type: String,
    default: '',
  },
  transferDocNumber: {
    type: String,
    default: '',
  },
  isEdit: {
    type: Boolean,
    default: false,
  },
});

defineEmits(['changeWarehouseId']);

const transferDocDetail = ref<TransferQueryApi.GetTransferDocDetailResponse>(
  {} as TransferQueryApi.GetTransferDocDetailResponse,
);

const schema: VbenFormSchema[] = [
  {
    component: (props: any) => {
      const val = props.modelValue;
      const showText = val || '/';
      return h('div', null, showText);
    },
    fieldName: 'transferDocNumber',
    label: '调拨单号',
  },
  {
    component: (props: any) => {
      const val = props.modelValue;
      const showText = val || '/';
      return h('div', null, showText);
    },
    fieldName: 'warehouseName',
    label: '仓库名称',
  },

  {
    component: (props: any) => {
      const val = props.modelValue;
      const showText = val || '/';
      return h('div', null, showText);
    },
    fieldName: 'warehouseId',
    label: '仓库id',
    formItemClass: 'hidden',
  },

  {
    component: 'Input',
    fieldName: 'remarkOptionList',
    formItemClass: 'col-span-full [&>div]:!overflow-visible mb-2',
    label: '调拨原因',
  },
  {
    component: h(UploadFiles, {
      mode: 'readMode',
      tableProps: {
        maxHeight: '300',
      },
      class: 'w-full',
    }),
    modelPropName: 'serialNumber', // 绑定serialNumber进行回显
    fieldName: 'serialNumber',
    formItemClass: 'col-span-full',
    label: '附件',
  },
  {
    component: 'Input',
    fieldName: 'docProcess',
    label: '单据流程',
    formItemClass: 'col-span-full',
  },
];

const [Form, formApi] = useVbenForm({
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

const getFormData = async (): Promise<
  false | Omit<TransferQueryApi.SubmitTransferDocParams, 'transferItemList'>
> => {
  const validateRes = await formApi.validate();
  if (!validateRes.valid) {
    return false;
  }
  const data = await formApi.getValues();

  return data as Omit<
    TransferQueryApi.SubmitTransferDocParams,
    'transferItemList'
  >;
};

// 获取详情
const getTransferDocDetailHandle = async () => {
  try {
    const transferDocDetailRes = await getTransferDocDetail({
      transferDocId: props.transferDocId,
      transferDocNumber: props.transferDocNumber,
      isQueryItem: false,
    });
    transferDocDetail.value = transferDocDetailRes;
    return transferDocDetailRes;
  } catch {
    ElMessage.error('获取调拨单据失败');
    return {} as TransferQueryApi.GetTransferDocDetailResponse;
  }
};

onMounted(async () => {
  if (props.transferDocId || props.transferDocNumber) {
    await getTransferDocDetailHandle();
    formApi.setValues(transferDocDetail.value);
  } else {
    ElMessage.error('调拨单号或调拨单ID不能为空');
  }
});

/** 单据状态 dictKey*/
const docStatusDict: { [key: string]: any } = {
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: '!text-red-500',
  },
  /** 已完成 */
  finish: {
    name: 'yiwancheng',
    color: '!text-lime-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: '!text-gray-300',
  },
};

defineExpose({
  getFormData,
});
</script>

<template>
  <IconFont
    v-if="docStatusDict[transferDocDetail.docStatus]"
    :name="docStatusDict[transferDocDetail.docStatus].name"
    :size="150"
    class="absolute right-20 top-14 z-40"
    :class="docStatusDict[transferDocDetail.docStatus].color"
  />
  <FormCard :is-footer="false" title="调拨信息">
    <Form>
      <template #remarkOptionList>
        <template
          v-for="item in transferDocDetail.remarkOptionList"
          :key="item.optionId"
        >
          <ElTag type="primary">
            {{ item.optionName }}
          </ElTag>
        </template>

        <span class="absolute bottom-[-20px] text-sm text-gray-500">{{
          transferDocDetail.remark
        }}</span>
      </template>

      <template #docProcess>
        <StepProgress
          v-if="transferDocDetail.transferDocNumber"
          :doc-number="transferDocDetail.transferDocNumber"
          class="pt-[10px]"
        />
        <div v-else>/</div>
      </template>
    </Form>
  </FormCard>
</template>
