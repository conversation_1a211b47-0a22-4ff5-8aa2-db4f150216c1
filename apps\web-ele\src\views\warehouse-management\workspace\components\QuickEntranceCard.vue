<script lang="ts">
import type { Ref } from 'vue';

import { defineComponent, ref } from 'vue';
import { useRouter } from 'vue-router';

import { getDoneTaskCount, getTodoTaskCount } from '#/api/warehouse-management';
import PanelCard from '#/components/panel-card/Index.vue';

// 基础徽章接口（用于最终渲染）
interface BadgeOption {
  show: boolean;
  num: () => number;
}

// 扩展徽章配置，包含异步获取函数
interface BadgeConfig extends Omit<BadgeOption, 'num'> {
  fetch: () => Promise<number>; // 异步获取数据的函数（仅配置阶段使用）
}

// 最终渲染用的入口项类型
interface EntranceItem {
  id: string;
  title: string;
  route: string;
  badge?: BadgeOption; // 仅包含渲染所需的徽章属性
}

// 完整入口项配置（包含可能的异步配置）
interface EntranceConfig extends Omit<EntranceItem, 'badge'> {
  badge?: BadgeConfig; // 配置阶段使用的徽章类型（包含fetch）
}

export default defineComponent({
  components: { PanelCard },
  setup() {
    const router = useRouter();
    const badgeCounts = ref<Record<string, number>>({});

    const entranceConfigs: EntranceConfig[] = [
      {
        id: 'inbound',
        title: '入库单',
        route: '/warehouse-management/inbound-management/inbound-documents',
      },
      {
        id: 'outbound',
        title: '出库单',
        route: '/warehouse-management/outbound-management/outbound-documents',
      },
      {
        id: 'OtherInbound',
        title: '入库申请',
        route: '/warehouse-management/other-inbound-requests/warehousing-entry',
      },
      {
        id: 'OtherOutbound',
        title: '出库申请',
        route:
          '/warehouse-management/other-outbound-requests/outbound-application-form',
      },
      // {
      //   id: 'inventoryAdjustment',
      //   title: '库存调整',
      //   route: '/warehouse-management/inventory-adjustment/query',
      // },
      // {
      //   id: 'baseInfo',
      //   title: '物料配置',
      //   route: '/warehouse-management/basic-data/material',
      // },
      {
        id: 'pendingProcessing',
        title: '待审核',
        route: '/task/pending-processing',
        badge: {
          show: true,
          fetch: getTodoTaskCount,
        },
      },
      {
        id: 'processed',
        title: '已审核',
        route: '/task/processed',
        badge: {
          show: true,
          fetch: () =>
            getDoneTaskCount({
              applyTimePeriodType: '01',
              applyTimePeriodUnit: '20',
            }),
        },
      },
    ];

    // 自动初始化所有徽章的异步请求
    entranceConfigs.forEach((item) => {
      if (item.badge) {
        badgeCounts.value[item.id] = 0;
        item.badge
          .fetch()
          .then((count) => {
            badgeCounts.value[item.id] = count;
          })
          .catch((error) => {
            console.error(`${item.title}数量获取失败：`, error);
            badgeCounts.value[item.id] = 0;
          });
      }
    });

    // 构建最终的入口列表（关键修正：明确转换为BadgeOption类型，排除fetch）
    const entranceList: Ref<EntranceItem[]> = ref(
      entranceConfigs.map((item) => ({
        ...item,
        badge: item.badge
          ? {
              show: item.badge.show, // 只保留BadgeOption需要的属性
              num: () => badgeCounts.value[item.id] ?? 0, // 确保返回number（避免undefined）
            }
          : undefined,
      })),
    );

    const handleCardClick = (route: string) => {
      router.push(route);
    };

    return {
      entranceList,
      handleCardClick,
    };
  },
});
</script>

<template>
  <PanelCard title="快捷入口">
    <template #default>
      <div class="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-2">
        <div
          @click="handleCardClick(item.route)"
          v-for="item in entranceList"
          :key="item.id"
          class="transition-transform duration-200 hover:scale-[1.02]"
        >
          <el-badge
            v-if="item.badge && item.badge.show"
            :value="item.badge.num()"
            class="!w-full"
          >
            <div
              class="hover:bg-primary-100 bg-primary-50 flex h-[70px] cursor-pointer items-center justify-center rounded-lg text-center font-medium transition-colors duration-200"
            >
              {{ item.title }}
            </div>
          </el-badge>

          <div
            v-else
            class="hover:bg-primary-100 bg-primary-50 flex h-[70px] cursor-pointer items-center justify-center rounded-lg text-center font-medium transition-colors duration-200"
          >
            {{ item.title }}
          </div>
        </div>
      </div>
    </template>
  </PanelCard>
</template>
