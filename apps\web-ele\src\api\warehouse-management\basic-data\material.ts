import type { Recordable } from '@vben/types';

import { baseDataPath, warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';
/** 物料库存配置接口返回 */
export namespace materialConfig {
  /** 库存配置分析列表 */
  export interface safetyInventoryWarnListType {
    /**	物料库存配置ID */
    invcConfigId: string;
    /**	仓库ID */
    warehouseId: string;
    /**	仓库编号 */
    warehouseCode: string;
    /**	仓库名称 */
    warehouseName: string;
    /**	是否参与安全库存预警 */
    isSafetyInventoryWarn: boolean;
    /** 是否参与呆滞分析 */
    isSlowMovingAnalysis: boolean;
    /** 安全库存 */
    safetyInventory: number;
    /**	呆滞期(天) */
    obsoletePeriod: number;
  }
  /** 物料库存配置分页records数据 */
  export interface materialPageRecords {
    /**	物料ID */
    materialId: string;
    /** 物料编码 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 物料图片ID */
    pictureFileId: string;
    /** 基本单位值，字典baseMaterialUnit */
    baseUnit: string;
    /** 基本单位标签，字典baseMaterialUnit*/
    baseUnitLabel: string;
    /** 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;
    /** 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;
    /** 物料大类值，字典baseMaterialType */
    materialType: string;
    /** 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料规格 */
    materialSpecs: string;
    /**	默认仓库ID */
    warehouseId: string;
    /** 默认仓库编号*/
    warehouseCode: string;
    /** 默认仓库名称 */
    warehouseName: string;
    /** 	默认库位ID */
    locationId: string;
    /** 默认库位编号 */
    locationCode: string;
    /** 默认库位名称 */
    locationName: string;
    /** 库存配置分析列表 */
    safetyInventoryWarnList: safetyInventoryWarnListType[];
  }
  /** 物料库存配置分页 */
  export interface materialPage {
    [key: string]: any;
    records: materialPageRecords[];
    total: string;
  }
  /** 与物料相关的安全库存预警设置列表 */
  export interface safetyInventoryWarnType {
    /**	物料库存配置的唯一标识 */
    invcConfigId: string;
    /** 所属仓库的唯一标识 */
    warehouseId: string;
    /** 是否参与安全库存预警*/
    isSafetyInventoryWarn: boolean;
    /** 安全库存数量 */
    safetyInventory: number;
    /** 仓库名 */
    warehouseName: string;
  }
  /** 与物料相关的呆滞期设置列表 */
  export interface slowMovingAnalysisType {
    /** 物料库存配置的唯一标识*/
    invcConfigId: string;
    /** 所属仓库的唯一标识*/
    warehouseId: string;
    /** 是否参与呆滞期分析*/
    isSlowMovingAnalysis: boolean;
    /** 呆滞期天数*/
    obsoletePeriod: number;
    /** 仓库名 */
    warehouseName: string;
  }
  /** 物料库存分析配置 */
  export interface materialInvcConfig {
    [key: string]: any;
    /** 物料id */
    materialId: string;
    /** 默认仓库ID */
    warehouseId: string;
    /** 默认仓库编号 */
    warehouseCode: string;
    /** 默认仓库名 */
    warehouseName: string;
    /** 默认库位ID */
    locationId: string;
    /** 默认库位编号 */
    locationCode: string;
    /** 默认库位名 */
    locationName: string;
    /** 与物料相关的安全库存预警设置列表 */
    safetyInventoryWarnList: safetyInventoryWarnType[];
    /** 与物料相关的呆滞期设置列表 */
    slowMovingAnalysisList: slowMovingAnalysisType[];
  }
  /** 物料配置信息 */
  export interface materialConfig {
    /**	默认仓库ID */
    warehouseId: string;
    /** 默认仓库编号*/
    warehouseCode: string;
    /** 默认仓库名称 */
    warehouseName: string;
    /** 	默认库位ID */
    locationId: string;
    /** 默认库位编号 */
    locationCode: string;
    /** 默认库位名称 */
    locationName: string;
  }
  /** 物料详细信息 */
  export interface materialDetail {
    /** 物料id */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 物料别名 */
    materialAlias: string;
    /** 物料规格 */
    materialSpecs: string;
    /**	默认库位id */
    defaultLocationId: string;
    /** 仓库名称*/
    warehouseName: string;
    /** 库位名称 */
    locationName: string;
    /** 基本单位值 */
    baseUnit: string;
    /** 基本单位标签 */
    baseUnitLabel: string;
    /** 物料属性值 */
    materialAttribute: string;
    /** 物料属性标签 */
    materialAttributeLabel: string;
    /** 物料细类 */
    materialCategory: string;
    /** 物料大类值 */
    materialType: string;
    /** 物料大类标签 */
    materialTypeLabel: string;
    /** 图片ID */
    pictureFileId: string;
    /**	附件流水号 */
    serialNumber: string;
    /** 是否标准物料 */
    isStandard: boolean;
    /** 是否不推荐使用 */
    isDeprecated: boolean;
    /** 可用状态*/
    isEnable: string;
    /** 批次号生成规则编号 */
    batchNumRule: string;
    /**	是否启用序列号 */
    isEnableSerialnum: boolean;
    /** 序列号生成规则编号 */
    serialnumRule: string;
    /**	来料检验配置值 */
    checkOption: string;
    /** 来料检验配置标签 */
    checkOptionLabel: string;
    /** 采购入库配置值 */
    warehousingOption: string;
    /** 采购入库配置标签 */
    warehousingOptionLabel: string;
    /** 采购验收配置值 */
    acceptOption: string;
    /** 采购验收配置标签 */
    acceptOptionLabel: string;
    /** 备注 */
    remark: string;
  }
}

/** 根据物料id修改物料配置信息*/
export async function modMaterialConfig(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/material/config/modMaterialConfig`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 根据物料id获取物料库存分析配置信息*/
export async function getMaterialInvcConfig(materialId: string) {
  return requestClient.get<materialConfig.materialInvcConfig>(
    `${warehousePath}/wm/material/config/invc/getMaterialInvcConfig/${materialId}`,
  );
}

/** 查询物料库存配置分页列表 */
export async function getMaterialConfigPage(params: Recordable<any>) {
  return requestClient.post<materialConfig.materialPage>(
    `${warehousePath}/wm/material/config/getMaterialConfigPage`,
    params,
  );
}

/** 导入物料库存配置列表*/
export async function importMaterialConfig(data: { file: Blob | File }) {
  return requestClient.upload(
    `${warehousePath}/wm/material/config/importMaterialConfig`,
    data,
  );
}

/** 导出物料库存配置模板 */
export async function exportMaterialConfigTemplate() {
  return requestClient.get(`${warehousePath}/wm/material/config/download`, {
    responseReturn: 'raw',
    responseType: 'blob', // 设置响应类型为 blob
  });
}

/** 导出物料库存配置列表 */
export async function exportMaterialConfig(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/material/config/exportMaterialConfig`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 根据物料id获取物料配置信息 */
export async function getMaterialConfig(materialId: string) {
  return requestClient.get<materialConfig.materialConfig>(
    `${warehousePath}/wm/material/config/getMaterialConfig/${materialId}`,
  );
}

/** 根据物料id或编号查询物料详细信息 */
export async function getMaterialDetail(
  materialId: string,
  materialCode?: string,
) {
  return requestClient.get<materialConfig.materialDetail>(
    `${baseDataPath}/base/material/getMaterialDetail`,
    {
      params: {
        materialId,
        materialCode,
      },
    },
  );
}

/** 查询物料信息分页列表*/
export async function getMaterialPage(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/getMaterialPage`,
    params,
  );
}

/** 查询可用母件信息分页列表 */ export async function getEnableProdBomPage(
  params: Recordable<any>,
) {
  return requestClient.post(
    `${baseDataPath}/base/material/getEnableProdBomPage`,
    params,
  );
}

/** 根据生产bomId查询物料的生产BOM信息 */
export async function getProdBomDetailById(bomId: string) {
  return requestClient.get(
    `${baseDataPath}/base/material/getProdBomDetailById/${bomId}`,
  );
}

/** 根据母件id查询物料的生产BOM信息 */
export async function getProdBomDetail(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/getProdBomDetail`,
    params,
  );
}
