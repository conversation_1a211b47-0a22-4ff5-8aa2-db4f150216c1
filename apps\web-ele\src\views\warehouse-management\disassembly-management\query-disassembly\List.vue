<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { StaffInfoType } from '#/api/common/staff';

import { computed, onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCard,
  ElMessage,
  ElMessageBox,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  closeAssemblyDoc,
  execAssemblyDoc,
  exportAssemblyDoc,
  getAssemblyDocPage,
} from '#/api';
import { getStaffInfo } from '#/api/common/staff';
import { docStatusDict } from '#/views/warehouse-management/disassembly-management/utils/index';

import EditForm from '../modules/EditForm.vue';
import ViewForm from '../modules/ViewForm.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const exportLoading = ref(false);
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();
/** 判断是否显示完成拆卸按钮 */
const isAutoIo = computed(() => {
  return (row: any) => {
    return shouldShowAutoIo(row, staffData.value);
  };
});

onMounted(async () => {
  /** 初始化搜索条件 */
  await gridApi.formApi.setValues({
    assemblyDocNumberList:
      props.params?.assemblyDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    docStatusList: props.params?.docStatusList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
    productMaterialCodeList:
      props.params?.productMaterialCodeList?.split(',') || [],
    productMaterialName: props.params?.productMaterialName || '',
  });
  /** 获取当前员工信息 */
  staffData.value = await getStaffInfo();
});

/** 模态框组件*/
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: EditForm,
  destroyOnClose: true,
});
const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: ViewForm,
  destroyOnClose: true,
});

/** 获取筛选器值 */
const getFilterValue = async () => {
  const formValues = await gridApi.formApi.getValues();
  const { submitTime, finishTime, ...restFormValues } = formValues;
  return {
    ...restFormValues,
    submitStartTime: submitTime?.startTime,
    submitEndTime: submitTime?.endTime,
    finishStartTime: finishTime?.startTime,
    finishEndTime: finishTime?.endTime,
  };
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    schema: useGridFormSchema(),
    resetButtonOptions: { content: '筛选重置' },
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await getFilterValue();
          const res = await getAssemblyDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 判断是否显示完成拆卸按钮 */
const shouldShowAutoIo = (row: any, staffData?: StaffInfoType) => {
  // 无当前员工数据，默认不显示
  if (!staffData) return false;
  // 无拆卸单据数据，默认不显示
  if (!row) return false;
  // 自动出入库未开启，不显示
  if (row.isAutoIo === false) return false;
  // 单据不为待领料，不显示
  if (row.docStatus !== 'awaitOut') return false;
  // 执行人不为当前用户，不显示
  if (row.executorUser !== staffData.staffId) return false;

  return true;
};

/** 判断是否显示取消按钮 */
const shouldShowClose = (row: any) => {
  // 无拆卸单据数据，默认不显示
  if (!row) return false;
  // 自动出入库未开启，不显示
  if (row.isAutoIo === false) return false;
  // 单据不为待领料，不显示
  if (row.docStatus !== 'awaitOut') return false;

  return true;
};

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

/** 新增 */
const onAdd = () => {
  editModalApi
    .setState({ title: '新增' })
    .setData({
      assemblyDocId: '',
      assemblyDocNumber: '',
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 导出数据 */
const exportHandle = async () => {
  try {
    exportLoading.value = true;
    const formValues = await getFilterValue();
    const response = await exportAssemblyDoc({
      ...formValues,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 查看 */
const onView = (row: any) => {
  viewModalApi
    .setState({ title: '拆卸单详情' })
    .setData({
      assemblyDocId: row.assemblyDocId,
      assemblyDocNumber: row.assemblyDocNumber,
      processInstanceId: row.processInstanceId,
      isShowAutoIo: isAutoIo.value(row),
      isShowClose: shouldShowClose(row),
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 公共弹窗 */
const dialog = async (content: string, title: string) => {
  try {
    await ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });
    return true;
  } catch {
    return false;
  }
};

/** 自动完成出入库 */
const autoExecute = async (assemblyDocId: string) => {
  try {
    if (await dialog('确定执行吗？', '提示')) {
      await execAssemblyDoc(assemblyDocId);
      ElMessage.success('执行成功');
      refreshList();
    }
  } catch {
    ElMessage.error('执行失败');
  }
};

/** 关闭拆卸单据 */
const closeDoc = async (assemblyDocId: string) => {
  try {
    if (await dialog('确定取消吗？', '提示')) {
      await closeAssemblyDoc(assemblyDocId);
      ElMessage.success('取消成功');
      refreshList();
    }
  } catch {
    ElMessage.error('取消失败');
  }
};
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <EditModal class="h-full w-10/12" />
    <ViewModal class="h-full w-10/12" />

    <ElCard class="h-full" body-class="h-full !p-[10px]">
      <Grid>
        <template #toolbar-actions>
          <ElButton
            v-access:code="['wm:assembly:split:submit']"
            type="primary"
            @click="onAdd"
          >
            新增
          </ElButton>
        </template>
        <template #productMaterialName="{ row }">
          <span>{{ row.productMaterialName }}</span>
          <span v-if="row.productMaterialCode">
            ({{ row.productMaterialCode }})
          </span>
        </template>
        <template #quantity="{ row }">
          <span>{{ row.quantity }}</span>
          <span>{{ row.baseUnitLabel }}</span>
        </template>
        <template #docStatusLabel="{ row }">
          <ElTag size="small" :type="docStatusDict[row.docStatus]">
            {{ row.docStatusLabel }}
          </ElTag>
        </template>
        <template #CellOperation="{ row }">
          <div>
            <ElButton link size="small" @click="onView(row)" type="info">
              查看
            </ElButton>
            <ElButton
              v-access:code="['wm:assembly:split:exec']"
              v-if="isAutoIo(row)"
              link
              size="small"
              @click="autoExecute(row.assemblyDocId)"
              type="primary"
            >
              完成拆卸
            </ElButton>
            <ElButton
              v-access:code="['wm:assembly:split:close']"
              v-if="shouldShowClose(row)"
              link
              size="small"
              @click="closeDoc(row.assemblyDocId)"
              type="danger"
            >
              取消单据
            </ElButton>
          </div>
        </template>
        <template #toolbar-tools>
          <ElTooltip
            class="box-item"
            effect="light"
            content="导出数据"
            placement="top-start"
          >
            <ElButton
              v-access:code="['wm:assembly:split:export']"
              :loading="exportLoading"
              circle
              @click="exportHandle"
            >
              <template #icon><IconFont name="xiazai" /></template>
            </ElButton>
          </ElTooltip>
        </template>
      </Grid>
    </ElCard>
  </Page>
</template>
