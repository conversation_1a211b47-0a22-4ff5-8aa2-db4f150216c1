<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import CloseBound from '../../components/close-bound/form-view/index.vue';
import MaterialsInfo from './materials-info/index.vue';
import OutboundInfo from './outbound-info/index.vue';

defineProps<{
  outBoundDocId?: string;
  outBoundDocNumber?: string;
}>();

const emits = defineEmits(['handleCancel']);

const inOutCancelDocId = ref<string>('');
const inOutCancelDocNumber = ref<string>('');

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  confirmText: '查看取消出库',
  destroyOnClose: true,
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const handleViewInOutCancelDoc = (data: {
  inOutCancelDocId: string;
  inOutCancelDocNumber: string;
}) => {
  closeModalApi.open();
  inOutCancelDocId.value = data.inOutCancelDocId;
  inOutCancelDocNumber.value = data.inOutCancelDocNumber;
};

const handleCancel = () => {
  emits('handleCancel');
};
</script>

<template>
  <div class="relative h-full">
    <CloseModal class="h-full w-10/12">
      <CloseBound :in-out-cancel-doc-id="inOutCancelDocId" />
    </CloseModal>

    <OutboundInfo
      :out-bound-doc-id="outBoundDocId"
      @view-in-out-cancel-doc="handleViewInOutCancelDoc"
    />

    <MaterialsInfo :out-bound-doc-id="outBoundDocId" />
    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
    </div>
  </div>
</template>
