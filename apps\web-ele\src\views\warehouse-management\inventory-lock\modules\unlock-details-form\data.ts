import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

/** 解锁信息 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'unblockUserName',
      label: '解锁人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'unblockTime',
      label: '解锁时间',
    },
  ];
}
