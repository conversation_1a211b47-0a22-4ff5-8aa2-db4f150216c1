import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace PendingSubmitApi {
  export interface PendingSubmitPageParams {
    batchnumDocNumber: string;
    docCode: string;
    materialIdList: string;
    materialCodeList: string;
    materialName: string;
    warehouseIdList: string;
    locationIdList: string;
    batchNumber: string;
    modifyStartTime: string;
    modifyEndTime: string;
    pageNum: number;
    pageSize: number;
  }

  export interface PendingSubmitRecord {
    /* 批次号处理单据ID */
    batchnumDocId: string;

    /* 批次号合并单据编号 */
    batchnumDocNumber: string;

    /* 批次号处理类型值，枚举WmBatchNumHandleTypeEnums */
    docCode: string;

    /* 批次号处理类型标签，枚举WmBatchNumHandleTypeEnums */
    docCodeLabel: string;

    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料别名 */
    materialAlias: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 规格型号 */
    materialSpecs: string;

    /* 物料属性值, 字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签, 字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值, 字典baseMaterialType */
    materialType: string;

    /* 物料大类标签, 字典baseMaterialType */
    materialTypeLabel: string;

    /* 基本单位值, 字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签, 字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 所属仓库ID */
    warehouseId: string;

    /* 所属仓库编号 */
    warehouseCode: string;

    /* 所属仓库名称 */
    warehouseName: string;

    /* 所属库位ID */
    locationId: string;

    /* 所属库位编号 */
    locationCode: string;

    /* 所属库位名称 */
    locationName: string;

    /* 合并后的批次号 */
    mergeBatchNumber: string;

    /* 合并拆分批次数量 */
    mergeQuantity: number;

    /* 合并拆分批次均价（单价） */
    mergeUnitPrice: number;

    /* 最后修改时间，时间格式：yyyy-MM-dd HH:mm */
    modifyTime: string;

    /* 单据审核状态 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例ID */
    processInstanceId: string;

    /* 单据状态值，字典publicDocStatus */
    docStatus: string;

    /* 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }

  export interface BatchItem {
    itemBatchNumber: string;
    itemQuantity: number;
    locationId: string;
  }

  export interface BatchnumDocParams {
    batchnumDocId?: string;
    materialId: string;
    warehouseId: string;
    locationId: string;
    remark: string;
    serialNumber: string;
    batchItemList: BatchItem[];
  }

  export interface SubmitMergeBatchnumDocParams extends BatchnumDocParams {
    mergeBatchNumber: string;
  }

  export interface SubmitSplitBatchnumDocParams extends BatchnumDocParams {
    splitBatchNumber: string;
  }
}

/**
 * 查询批次号处理单据分页列表
 */
export async function getMyPendingSubmitForBatchNumber(
  params: PendingSubmitApi.PendingSubmitPageParams,
) {
  return requestClient.post<Array<PendingSubmitApi.PendingSubmitRecord>>(
    `${warehousePath}/wm/batchnum/getMyDraftDocPage`,
    { ...params },
  );
}

/** 提交批次号合并单据 */
export async function submitMergeBatchnumDoc(
  params: PendingSubmitApi.SubmitMergeBatchnumDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/batchnum/submitMergeBatchnumDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 暂存或修改批次号合并单据 */
export async function saveMergeBatchnumDoc(
  params: PendingSubmitApi.SubmitMergeBatchnumDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/batchnum/saveMergeBatchnumDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 提交批次号拆分单据 */
export async function submitSplitBatchnumDoc(
  params: PendingSubmitApi.SubmitSplitBatchnumDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/batchnum/submitSplitBatchnumDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/** 提交批次号拆分单据 */
export async function saveSplitBatchnumDoc(
  params: PendingSubmitApi.SubmitSplitBatchnumDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/batchnum/saveSplitBatchnumDoc`,
    params,
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}
