<script setup lang="ts">
import type { MaterialPendingApi } from '#/api/warehouse-management/index';

import { h, onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getPrepDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';

const props = defineProps<{
  prepDocId: string;
}>();

const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

const uniqueTargetWarehouseList = ref<
  { targetLocationName: string; warehouseName: string }[]
>([]);

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 60 },
  schema: [],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/** 获取备料数据 */
const getPrepDocDetailHandle = async () => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId: props.prepDocId,
      isQueryItem: true,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
  }
};

/** 处理备料数据 合并warehouseName和targetLocationName ，数据结构[{warehouseName:',targetLocationName:''}]*/
const handlePrepDocDetail = () => {
  const targetWarehouseList = prepDocDetail.value.prepItemList.map(
    (prepItem) => {
      return prepItem.itemList.map((item) => {
        return {
          warehouseName: item.warehouseName,
          targetLocationName: item.targetLocationName,
        };
      });
    },
  );

  // 扁平化，去重
  const flatTargetWarehouseList = targetWarehouseList.flat();
  const uniqueTargetWarehouseList = flatTargetWarehouseList.filter(
    (item, index, self) =>
      index === self.findIndex((t) => t.warehouseName === item.warehouseName),
  );

  return uniqueTargetWarehouseList;
};

onMounted(async () => {
  if (props.prepDocId) {
    await getPrepDocDetailHandle();

    if (prepDocDetail.value?.prepItemList?.length > 0) {
      uniqueTargetWarehouseList.value = handlePrepDocDetail();

      formApi.setState((prev) => {
        const currentSchema = prev?.schema ?? [];

        const newSchema = uniqueTargetWarehouseList.value.map((item) => {
          return [
            {
              component: () => {
                return h('div', null, item.warehouseName || '/');
              },
              fieldName: 'warehouseName',
              label: '仓库',
            },
            {
              component: () => {
                return h('div', null, item.targetLocationName || '/');
              },
              fieldName: 'targetLocationName',
              label: '目标库位',
            },
          ];
        });

        return {
          schema: [...currentSchema, ...newSchema.flat()],
        };
      });

      formApi.setValues(uniqueTargetWarehouseList.value);
    } else {
      ElMessage.error('备料单据没有备料项');
    }
  } else {
    ElMessage.error('缺少备料单据id');
  }
});
</script>

<template>
  <FormCard :is-footer="false" title="备料目标库位">
    <template #default>
      <Form />
    </template>
  </FormCard>
</template>
