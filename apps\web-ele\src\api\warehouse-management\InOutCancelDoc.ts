import { requestClient } from '#/api/request';

import { warehousePath } from '../path';

export namespace InOutCancelDocApi {
  export interface InOutCancelDocParams {
    inOutCancelDocId?: string;
    inOutBoundDocId: string;
    remarkOptionList?: string[];
    remark?: string;
    serialNumber?: string;
  }

  export interface InOutCancelDocDetailParams {
    inOutCancelDocId: string;
    inOutCancelDocNumber: string;
  }

  export interface InOutCancelDocDetailByInOutDocParams {
    inOutBoundDocId: string;
    inOutBoundDocNumber: string;
  }

  export interface InOutCancelDocDetail {
    /* 出入库取消申请单ID */
    inOutCancelDocId: string;

    /* 出入库取消申请单编号 */
    inOutCancelDocNumber: string;

    /* 出入库单据ID */
    inOutBoundDocId: string;

    /* 出入库单据编号 */
    inOutBoundDocNumber: string;

    /* 出入库类型值，枚举WmInOrOutEnums */
    inOrOut: string;

    /* 出入库类型标签，枚举WmInOrOutEnums */
    inOrOutLabel: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 创建人ID */
    createUser: string;

    /* 创建人姓名 */
    createUserName: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 提交人部门ID */
    submitUserDeptId: string;

    /* 提交人部门名称 */
    submitUserDeptName: string;

    /* 创建时间，时间格式：yyyy-MM-dd HH:mm */
    createTime: string;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;

    /* 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;

    /* 取消原因选项列表 */
    remarkOptionList: {
      /* 选项ID */
      optionId: string;

      /* 选项名称 */
      optionName: string;
    }[];

    /* 取消原因说明 */
    remark: string;

    /* 审核状态 */
    auditStatus: string;

    /* 审核状态名称 */
    auditStatusName: string;

    /* 审核流程实例ID */
    processInstanceId: string;

    /* 附件流水号 */
    serialNumber: string;

    /* 单据状态值，字典wmInOutCancelDocStatus */
    docStatus: string;

    /* 单据状态标签，字典wmInOutCancelDocStatus */
    docStatusLabel: string;
  }
}

/**
 * 提交出入库取消申请单
 */
export async function submitInOutCancelDoc(
  params: InOutCancelDocApi.InOutCancelDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/inOutBound/cancel/submitInOutCancelDoc`,
    { ...params },
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/**
 * 获取出入库取消申请单详细信息
 */
export function getInOutCancelDocDetail(
  params: InOutCancelDocApi.InOutCancelDocDetailParams,
) {
  const { inOutCancelDocId, inOutCancelDocNumber } = params;
  return requestClient.get<InOutCancelDocApi.InOutCancelDocDetail>(
    `${warehousePath}/wm/inOutBound/cancel/getInOutCancelDocDetail`,
    {
      params: {
        inOutCancelDocId,
        inOutCancelDocNumber,
      },
    },
  );
}

/**
 * 根据出入库单据获取出入库取消申请单详细信息
 */
export function getInOutCancelDocListByInOutDoc(
  params: InOutCancelDocApi.InOutCancelDocDetailByInOutDocParams,
) {
  const { inOutBoundDocId, inOutBoundDocNumber } = params;
  return requestClient.get<InOutCancelDocApi.InOutCancelDocDetail[]>(
    `${warehousePath}/wm/inOutBound/cancel/getInOutCancelDocListByInOutDoc`,
    {
      params: {
        inOutBoundDocId,
        inOutBoundDocNumber,
      },
    },
  );
}
