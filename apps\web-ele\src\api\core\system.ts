import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, systemPath } from '../path';

export namespace SystemRoleApi {
  export interface SystemRole {
    [key: string]: any;
    records: string;
    total: string;
  }
}

export async function getDeptTree(params: Recordable<any>) {
  return requestClient.post(`${baseDataPath}/base/dept/getDeptTree`, {
    ...params,
  });
}

/**
 * 获取用户列表数据
 */
export async function getUserPage(params: Recordable<any>) {
  return requestClient.post<Array<SystemRoleApi.SystemRole>>(
    `${systemPath}/user/getUserPage`,
    { ...params },
  );
}

/**
 * 获取角色列表数据
 */
export async function getRoleList(params: Recordable<any>) {
  return requestClient.post<Array<SystemRoleApi.SystemRole>>(
    `${systemPath}/role/rolePage`,
    { ...params },
  );
}
/**
 * 创建角色
 * @param data 角色数据
 */
export async function createRole(
  data: Omit<SystemRoleApi.SystemRole, 'roleId'>,
) {
  return requestClient.post(`${systemPath}/role/addRole`, data);
}

/**
 * 更新角色
 *
 * @param roleId 角色 ID
 * @param data 角色数据
 */
export async function updateRole(
  roleId: string,
  data: Omit<SystemRoleApi.SystemRole, 'roleId'>,
) {
  return requestClient.put(`${systemPath}/system/role/${roleId}`, data);
}

/**
 * 删除角色
 * @param roleId 角色 ID
 */
export async function deleteRole(roleId: string) {
  return requestClient.delete(`${systemPath}/system/role/${roleId}`);
}
