import type { Recordable } from '@vben/types';

import { workflowPath } from '#/api/path';
import { requestClient } from '#/api/request';

// 我的待处理任务数
export async function getTodoTaskCount(params?: Recordable<any>) {
  return requestClient.post(`${workflowPath}/task/mi/getTodoTaskCount`, params);
}

// 我的已处理任务数
export async function getDoneTaskCount(params?: Recordable<any>) {
  return requestClient.post(`${workflowPath}/task/mi/getDoneTaskCount`, params);
}
