<script lang="ts">
import { defineComponent } from 'vue';

import {
  add,
  divide,
  formatAmount,
  multiply,
  subtract,
} from '#/utils/numberUtils';

export default defineComponent({
  setup() {
    const amount = 1_234_567.891_23;

    const formattedAmount1 = formatAmount(amount); // 默认两位小数
    const formattedAmount2 = formatAmount(amount, 4); // 四位小数
    const formattedAmount3 = formatAmount(amount, 2, { useGrouping: false }); // 无千位分隔符

    const num1 = 3.004;
    const num2 = 43.213;
    const addNum = add(num1, num2);
    const addNumFixed2 = add(num1, num2, 2);

    const subtractNum = subtract(num2, num1);
    const subtractNumFixed2 = subtract(num2, num1, 2);

    const multiplyNum = multiply(num2, num1);
    const multiplyFixed2 = multiply(num2, num1, 2);

    const divideNum = divide(num2, num1);
    const divideFixed2 = divide(num2, num1, 2);
    return {
      amount,
      formattedAmount1,
      formattedAmount2,
      formattedAmount3,
      num1,
      num2,
      addNum,
      addNumFixed2,
      subtractNum,
      subtractNumFixed2,
      multiplyNum,
      multiplyFixed2,
      divideNum,
      divideFixed2,
    };
  },
});
</script>

<template>
  <ElCard>
    <div>
      <p>原始数据: {{ amount }}</p>
      <p>默认保留两位小数: {{ formattedAmount1 }}</p>
      <p>自定义保留四位小数: {{ formattedAmount2 }}</p>
      <p>无千位分隔符: {{ formattedAmount3 }}</p>
    </div>
  </ElCard>
  <ElCard>
    <p>num1： {{ num1 }}，num2：{{ num2 }}</p>
    <p>加： {{ num1 }}+{{ num2 }}={{ addNum }}</p>
    <p>加(指定保留两位小数)： {{ num1 }}+{{ num2 }}={{ addNumFixed2 }}</p>
  </ElCard>
  <ElCard>
    <p>num1： {{ num1 }}，num2：{{ num2 }}</p>
    <p>减： {{ num2 }}-{{ num1 }}={{ subtractNum }}</p>
    <p>减(指定保留两位小数)： {{ num2 }}-{{ num1 }}={{ subtractNumFixed2 }}</p>
  </ElCard>
  <ElCard>
    <p>num1： {{ num1 }}，num2：{{ num2 }}</p>
    <p>乘： {{ num1 }}*{{ num2 }}={{ multiplyNum }}</p>
    <p>乘(指定保留两位小数)： {{ num1 }}*{{ num2 }}={{ multiplyFixed2 }}</p>
  </ElCard>
  <ElCard>
    <p>num1： {{ num1 }}，num2：{{ num2 }}</p>
    <p>除： {{ num2 }}/{{ num1 }}={{ divideNum }}</p>
    <p>除(指定保留两位小数)： {{ num2 }}/{{ num1 }}={{ divideFixed2 }}</p>
  </ElCard>
</template>
