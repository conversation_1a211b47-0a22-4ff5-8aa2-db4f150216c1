<script lang="ts" setup>
import { useVbenForm } from '@girant/adapter';

// 使用 useVbenForm 创建表单
// Form 为表单组件
// formApi 为表单的方法
const [Form] = useVbenForm({
  // 提交函数
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      defaultValue: 'hidden value',
      dependencies: {
        show: false,
        // 随意一个字段改变时，都会触发
        triggerFields: ['field1Switch'],
      },
      fieldName: 'hiddenField',
      label: '隐藏字段',
    },
    {
      component: 'Switch',
      defaultValue: true,
      fieldName: 'field1Switch',
      help: '通过Dom控制销毁',
      label: '显示字段1',
    },
    // 其他表单字段配置...
  ],
});

// 提交表单的处理函数
async function onSubmit(_values: any) {
  // try {
  //   // 这里可以进行表单提交的逻辑，比如发送请求到服务器
  //   ElMessage.success('表单提交成功');
  // } catch {
  //   ElMessage.error('表单提交失败');
  // }
}
</script>

<template>
  <Form />
</template>
