<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type {
  InventoryQueryApi,
  MaterialCategoryTreeType,
  WarehouseInfoApi,
} from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCascader,
  ElDatePicker,
  ElInput,
  ElMessage,
  ElTooltip,
} from 'element-plus';

import {
  exportInvcDetail,
  getInventoryPageDetail,
  getMaterialCategoryTree,
  getWarehouseList,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import Form from '../modules/Form.vue';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});
/** 导出加载 */
const exportLoading = ref(false);
/** 物料细类选择的数据 */
const materialCategory = ref(
  props.params?.materialCategoryList?.split(',') || [],
);
/** 物料细类数据 */
const materialCategoryData = ref<any[]>([]);
/** 第一次加载控制级联数据 搜索计数 */
let count = 0;
/** 最后入库时间*/
const lastInTime = ref({
  lastInStartTime: props.params?.lastInStartTime,
  lastInEndTime: props.params?.lastInEndTime,
});
/** 最后出库时间*/
const lastOutTime = ref({
  lastOutStartTime: props.params?.lastOutStartTime,
  lastOutEndTime: props.params?.lastOutEndTime,
});
/** 选择器配置 */
const propsConfig = {
  // 多选
  multiple: true,
  // 只保存叶子节点数据
  emitPath: false,
};
const inventoryQuantity = ref({
  minInventory: props.params?.minInventory,
  maxInventory: props.params?.maxInventory,
});
/** 库位列表（含库位） */
const locationList = ref<any[]>([]);
/** 选择的仓库/库位数据 */
const selectedLocation = ref<any[]>([
  ...(props.params?.warehouseIdList?.split(',') || []),
  ...(props.params?.locationIdList?.split(',') || []),
]);
/** 仓库/库位组件ref */
const locationIdListRef = ref();
/** 操作 */
const onActionClick = (e: any) => {
  switch (e.code) {
    case 'view': {
      onView(e.row);
      break;
    }
  }
};
/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();
    // 处理重置不了的字段
    inventoryQuantity.value = {
      minInventory: '',
      maxInventory: '',
    };
    lastInTime.value = {
      lastInStartTime: '',
      lastInEndTime: '',
    };
    lastOutTime.value = {
      lastOutStartTime: '',
      lastOutEndTime: '',
    };
    materialCategory.value = [];
    selectedLocation.value = [];
    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};
// 表格
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || true,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    resetButtonOptions: {
      content: '筛选重置',
    },
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getInventoryPageDetail({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
            minInventory: inventoryQuantity.value.minInventory,
            maxInventory: inventoryQuantity.value.maxInventory,
            materialCategoryList: materialCategory.value,
            locationIdList:
              count === 0
                ? props.params?.locationIdList
                : getSelectedLocation().locationIdList,
            warehouseIdList:
              count === 0
                ? props.params?.warehouseIdList
                : getSelectedLocation().warehouseIdList,
            ...lastInTime.value,
            ...lastOutTime.value,
          });
          count++;
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<InventoryQueryApi.InventoryDetailPage>,
});
/** 模态框组件*/
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: Form,
  destroyOnClose: true,
});
/** 查看 */
const onView = (row: any) => {
  formModalApi
    .setState({
      title: '查看',
    })
    .setData({
      materialId: row.materialId,
      warehouseId: row.warehouseId,
    })
    .open();
};
/** 数据导出 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportInvcDetail({
      ...formValues,
      minInventory: inventoryQuantity.value.minInventory,
      maxInventory: inventoryQuantity.value.maxInventory,
      materialCategoryList: materialCategory.value,
      locationIdList: getSelectedLocation().locationIdList,
      warehouseIdList: getSelectedLocation().warehouseIdList,
      ...lastInTime.value,
      ...lastOutTime.value,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch (error: any) {
    ElMessage({
      type: 'error',
      message: error.msg || '数据导出失败',
      grouping: true,
    });
  } finally {
    exportLoading.value = false;
  }
};

/** 处理数据 物料细类数据转换成树形结构*/
const convertMaterialData = (item: any) => {
  return {
    label: item.categoryName,
    value: item.categoryCode,
    children: item.children
      ? item.children.map((child: MaterialCategoryTreeType) =>
          convertMaterialData(child),
        )
      : [],
  };
};

/** 处理数据 仓库*/
const convertWarehouseListData = (item: WarehouseInfoApi.WarehouseList) => {
  return {
    label: item.warehouseName,
    value: item.warehouseId,
    type: 'warehouse',
    children:
      item.locationList.length > 0
        ? convertLocationListData(item.locationList)
        : [],
  };
};

/** 处理数据 库位列表*/
const convertLocationListData = (item: WarehouseInfoApi.locationListType[]) => {
  return item.map((location) => ({
    label: location.locationName,
    value: location.locationId,
  }));
};

/** 获取数据 */
const getData = async () => {
  try {
    // 获取物料细类数据
    const data = await getMaterialCategoryTree();
    // 获取仓库/库位数据
    const data2 = await getWarehouseList({
      isLoc: true,
    });
    // 执行转换
    materialCategoryData.value = data.map((item) => convertMaterialData(item));
    locationList.value = data2.map((item) => convertWarehouseListData(item));
  } catch {
    ElMessage.error('获取数据失败');
  }
};
/** 获取当前选中的仓库/库位数据 并按照仓库 库位 分别储存*/
const getSelectedLocation = () => {
  const data: any = {
    warehouseIdList: [],
    locationIdList: [],
  };
  const nodes = locationIdListRef.value.getCheckedNodes();
  nodes.forEach((node: any) => {
    if (node.data.type === 'warehouse') {
      data.warehouseIdList.push(node.value);
    } else {
      data.locationIdList.push(node.value);
    }
  });
  return data;
};
onMounted(async () => {
  await gridApi.formApi.setValues({
    materialCodeList: props.params?.materialCodeList?.split(',') || [],
    materialTypeList: props.params?.materialTypeList?.split(',') || [],
  });
  await getData();
});
defineExpose({
  gridApi,
  Grid,
  getSelectedLocation,
});
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <FormModal class="h-full w-10/12" />
    <Grid>
      <template #form-lastInTime>
        <ElDatePicker
          v-model="lastInTime.lastInStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, lastInTime.lastInEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="lastInTime.lastInEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                lastInTime.lastInStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
      <template #form-lastOutTime>
        <ElDatePicker
          v-model="lastOutTime.lastOutStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(
                time,
                lastOutTime.lastOutEndTime || new Date('2099-12-31'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="lastOutTime.lastOutEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                lastOutTime.lastOutStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-locationIdList>
        <ElCascader
          ref="locationIdListRef"
          class="w-full"
          :props="propsConfig"
          v-model="selectedLocation"
          :options="locationList"
          collapse-tags
          collapse-tags-tooltip
          :show-all-levels="false"
          :max-collapse-tags="1"
          clearable
          filterable
        />
      </template>
      <template #form-materialCategoryList>
        <ElCascader
          class="w-full"
          :props="propsConfig"
          v-model="materialCategory"
          :options="materialCategoryData"
          collapse-tags
          collapse-tags-tooltip
          :max-collapse-tags="1"
          filterable
          clearable
        />
      </template>
      <template #form-inventoryQuantity>
        <ElInput
          v-model="inventoryQuantity.minInventory"
          type="number"
          placeholder="请输入最小值"
          clearable
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElInput
          v-model="inventoryQuantity.maxInventory"
          type="number"
          placeholder="请输入最大值"
          clearable
        />
      </template>
      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            :loading="exportLoading"
            circle
            @click="exportHandle"
            v-access:code="'wm:inventory:export:list:item'"
          >
            <template #icon><IconFont name="xiazai" /></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
