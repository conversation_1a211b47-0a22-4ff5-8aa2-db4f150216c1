<script lang="ts">
import { computed, defineAsyncComponent, defineComponent, markRaw } from 'vue';

import PanelCard from '#/components/panel-card/Index.vue';

export default defineComponent({
  components: {
    PanelCard,
  },
  props: {
    activeCardId: {
      type: String,
      default: '',
    },
    activeCardTitle: {
      type: String,
      default: '',
    },
    params: {
      type: Object,
      default: () => ({}),
    },
    attrs: {
      type: Object,
      default: () => ({
        wrapperClass:
          'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-4',
        collapsed: true,
        collapsedRows: 2,
        showCollapseButton: true,
      }),
    },
  },
  setup(props) {
    const searchValue = computed(() => {
      return props.params;
    });
    const attr = computed(() => {
      return props.attrs;
    });
    const componentKey = computed(() => {
      return JSON.stringify(searchValue.value);
    });
    const currentComponent = computed(() => {
      switch (props.activeCardId) {
        // 已取消入库明细
        case 'cancel-inbound-details': {
          return {
            title: '已取消入库明细列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inbound-management/modules/inbound-details/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已取消入库单据
        case 'cancel-inbound-documents': {
          return {
            title: '已取消入库单据列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inbound-management/inbound-documents/List.vue'
                  ),
              ),
            ),
          };
        }
        // 取消出库待审核
        case 'cancel-outbound-approve': {
          return {
            title: '取消出库待审核列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-documents/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已取消出库明细
        case 'cancel-outbound-details': {
          return {
            title: '已取消出库明细列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/components/outbound-details/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已取消出库单据
        case 'cancel-outbound-documents': {
          return {
            title: '已取消出库单据列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-documents/List.vue'
                  ),
              ),
            ),
          };
        }
        // 取消入库待审核
        case 'cancel-stock-approve': {
          return {
            title: '取消入库待审核列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inbound-management/inbound-documents/List.vue'
                  ),
              ),
            ),
          };
        }
        // 呆料
        case 'idle-material': {
          return {
            title: '呆料列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inventory-query/obsolete/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已入库明细
        case 'inbound-details': {
          return {
            title: '已入库明细列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inbound-management/inbound-already-details/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已入库单据
        case 'inbound-documents': {
          return {
            title: '已入库单据列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inbound-management/stock-already/List.vue'
                  ),
              ),
            ),
          };
        }
        // 库存情况
        case 'inventory': {
          return {
            title: '库存列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inventory-query/inventory/List.vue'
                  ),
              ),
            ),
          };
        }
        // 库存预警
        case 'inventory-alert': {
          return {
            title: '库存预警列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inventory-query/safety/List.vue'
                  ),
              ),
            ),
          };
        }
        // 锁库
        case 'lock-library': {
          return {
            title: '锁库列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inventory-lock/lock-query/List.vue'
                  ),
              ),
            ),
          };
        }
        // 待备料
        case 'material-preparation': {
          return {
            title: '待备料列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-pending/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已备料
        case 'material-preparation-already': {
          return {
            title: '已备料列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-pending/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已出库明细
        case 'outbound-details': {
          return {
            title: '已出库明细列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-already-details/List.vue'
                  ),
              ),
            ),
          };
        }
        // 已出库单据
        case 'outbound-documents': {
          return {
            title: '已出库单据列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-already/List.vue'
                  ),
              ),
            ),
          };
        }
        // 待确认出库
        case 'pending-outbound': {
          return {
            title: '待确认出库列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/outbound-management/outbound-pending/List.vue'
                  ),
              ),
            ),
          };
        }

        // 待确认入库
        case 'pending-storage': {
          return {
            title: '待确认入库列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inbound-management/stock-pending/List.vue'
                  ),
              ),
            ),
          };
        }
        default: {
          return {
            title: '库存列表',
            component: markRaw(
              defineAsyncComponent(
                () =>
                  import(
                    '#/views/warehouse-management/inventory-query/inventory/List.vue'
                  ),
              ),
            ),
          };
        }
      }
    });

    return {
      currentComponent,
      searchValue,
      attr,
      componentKey, // 导出key
    };
  },
});
</script>

<template>
  <div>
    <PanelCard title="">
      <div class="font-black">{{ currentComponent.title }}</div>
      <div class="middle-panel-container -mb-6 w-full">
        <component
          :is="currentComponent.component"
          :params="searchValue"
          :attr="attr"
          :key="componentKey"
        />
      </div>
    </PanelCard>
  </div>
</template>
<style scoped>
.middle-panel-container11 {
  max-height: 910px;
  margin-bottom: 5px;
  overflow-y: auto;
}

.middle-panel-container {
  height: calc(100vh - 150px);
  max-height: 860px;
  margin-bottom: 2px;
  overflow-y: auto;
  scrollbar-color: rgb(156 163 175 / 20%) transparent;
  scrollbar-width: thin;
}
</style>
