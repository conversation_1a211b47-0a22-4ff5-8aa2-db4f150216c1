<script lang="ts" setup>
import type { VxeGridProps } from '@girant/adapter';

import { useVbenVxeGrid } from '@girant/adapter';

import { getRoleList } from '#/api';

interface RowType {
  roleName: string;
  roleId: string;
}

const gridOptions: VxeGridProps<RowType> = {
  checkboxConfig: {
    highlight: true,
    labelField: 'name',
  },
  columns: [
    { title: '序号', cellRender: { name: 'CellSequence' }, width: 50 },
    { align: 'left', title: 'Name', type: 'checkbox', width: 100 },
    { field: 'roleName', title: 'Category' },
    { field: 'roleId', title: 'Color' },
  ],
  exportConfig: {},
  keepSource: true,
  proxyConfig: {
    ajax: {
      query: async ({ page }) => {
        const data = await getRoleList({
          page: page.currentPage,
          pageSize: page.pageSize,
        });
        return data;
      },
    },
    response: {
      total: 'total',
      result: 'records',
    },
  },
  toolbarConfig: {
    custom: true,
    export: true,
    refresh: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions,
});
</script>

<template>
  <div class="vp-raw w-full">
    <Grid>
      <template #toolbar-tools>
        <ElButton class="mr-2" type="primary" @click="() => gridApi.query()">
          刷新当前页面
        </ElButton>
        <ElButton type="primary" @click="() => gridApi.reload()">
          刷新并返回第一页
        </ElButton>
      </template>
    </Grid>
  </div>
</template>
