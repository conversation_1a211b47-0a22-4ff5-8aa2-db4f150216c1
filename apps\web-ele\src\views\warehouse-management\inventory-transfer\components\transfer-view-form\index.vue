<script setup lang="ts">
import type { TransferApi } from '#/api';
import type { StaffInfoType } from '#/api/common/staff';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@vben/common-ui';

import { useClipboard } from '@vueuse/core';
import { ElButton, ElMessage, ElScrollbar, ElTag } from 'element-plus';

import { getExecCode, getWareTransferDocDetail } from '#/api';
import { getStaffInfo } from '#/api/common/staff';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { docStatusDict } from '../../utils/index';
import { useFormSchema } from './data';

const props = defineProps({
  /** 库存调拨单据ID */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 库存调拨单据编号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();
/** 拆卸单数据 */
const data = ref<TransferApi.TransferDocDetail>();
/** 领料码 */
const execCode = ref<number>();
/** 当前单据状态 */
const docStatus = ref('');

/** 调拨单据状态图片 dictKey*/
const docStatusIconDict: { [key: string]: any } = {
  /** 已完成 */
  finish: {
    name: 'yiwancheng',
    color: 'text-lime-500',
  },
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: 'text-red-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: 'text-slate-500 !text-gray-300',
  },
};

onMounted(() => {
  if (props.transferDocId || props.transferDocNumber) {
    getData();
  }
});

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    staffData.value = await getStaffInfo();
    const res = await getWareTransferDocDetail(
      props.transferDocId,
      props.transferDocNumber,
    );
    data.value = res;
    docStatus.value = res?.docStatus;
    formApi.setState({
      schema: useFormSchema(
        docStatus.value,
        data.value.docCode,
        staffData.value.staffId === data.value.submitUser,
      ),
    });
    // 赋值
    formApi.setValues(res);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 拆卸信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: [],
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});

/** 获取领料码 */
const getExecCodeData = async () => {
  try {
    execCode.value = await getExecCode(undefined, props.transferDocId);
  } catch {
    ElMessage.error('获取领料码失败');
  }
};

/** 复制领料码 */
const { copy } = useClipboard({
  legacy: true,
  source: execCode.value?.toString,
});
</script>

<template>
  <div class="relative" v-loading="loading">
    <IconFont
      v-if="
        docStatus === 'finish' ||
        docStatus === 'reject' ||
        docStatus === 'closed'
      "
      :name="docStatusIconDict[docStatus].name"
      :size="150"
      color="dark:bg-gray-800"
      class="absolute right-20 top-14 z-40"
      :class="docStatusIconDict[docStatus].color"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>库存调拨信息</span>
      </template>
      <Form>
        <template #execCode>
          <div v-if="execCode" class="flex">
            <span>
              <b>{{ execCode }}</b>
            </span>
            <ElButton
              link
              type="primary"
              @click="
                copy(execCode.toString()).then(() => {
                  ElMessage.success('复制成功');
                })
              "
              class="ml-2"
            >
              点击复制
            </ElButton>
          </div>
          <ElButton v-else link type="primary" @click="getExecCodeData">
            点击查看
          </ElButton>
        </template>
        <template #docStatusLabel="slotProps">
          <ElTag size="small" :type="docStatusDict[docStatus]">
            {{ slotProps.modelValue ?? '/' }}
          </ElTag>
        </template>
        <template #remarkOptionList>
          <div class="flex flex-col">
            <div>
              <template
                v-for="item in data?.remarkOptionList"
                :key="item.optionId"
              >
                <ElTag type="primary" class="mr-2">
                  {{ item.optionName }}
                </ElTag>
              </template>
            </div>

            <span class="mt-2 text-sm text-gray-500">
              {{ data?.remark }}
            </span>
          </div>
        </template>
        <template #documentProcess>
          <ElScrollbar>
            <StepProgress
              :doc-number="
                props.transferDocNumber || data?.transferDocNumber || ''
              "
              class="min-w-[750px] pt-[10px]"
            />
          </ElScrollbar>
        </template>
      </Form>
    </FormCard>
  </div>
</template>
