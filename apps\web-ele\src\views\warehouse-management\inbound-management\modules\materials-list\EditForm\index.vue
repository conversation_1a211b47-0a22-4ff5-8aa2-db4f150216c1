<script setup lang="ts">
import type { MaterialItem } from '../../../components/wuliao-item/types/index';

import type {
  InBoundDocApi,
  WarehouseListForMaterialListApi,
} from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import {
  getActiveWarehouseListByMaterialList,
  getInBoundDocDetail,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';

import MaterialInfo from '../../../components/wuliao-item/components/MaterialInfo.vue';
import EditItem from '../../../components/wuliao-item/EditItem.vue';
import MaterialInfoDetailModal from '../components/MaterialInfoDetailModal.vue';
import { transformInBoundData } from '../utils';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
});

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

const formatData = ref<MaterialItem.MaterialItemData>(
  {} as MaterialItem.MaterialItemData,
);

const allWarehouseListForMaterialList = ref<
  WarehouseListForMaterialListApi.WarehouseListForMaterialList[]
>([]);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: true,
    });
    inBoundData.value = inBoundRes || ({} as InBoundDocApi.InBoundDocDetail);
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

const [Modal, ModalApi] = useVbenModal({
  connectedComponent: MaterialInfoDetailModal,
  destroyOnClose: true,
});

const handleMaterialCode = (materialId: string) => {
  ModalApi?.setState({ title: '物料信息' })
    .setData({
      materialId,
    })
    .open();
};

const editItemRef = ref<InstanceType<typeof EditItem>[]>([]);

onMounted(async () => {
  const inBoundRes = await getInBoundDocDetailHandle();

  if (inBoundRes) {
    formatData.value = transformInBoundData(inBoundRes);

    const params = {
      materialIdList: formatData.value.inBoundItemList
        .map((item) => item.materialId)
        .join(','),
      docTypeCode: inBoundRes?.origDocTypeCode,
    };
    // 批量获取物料可入库的仓库列表
    const WForMListRes = await getActiveWarehouseListByMaterialList(params);

    allWarehouseListForMaterialList.value = WForMListRes;
  }
});

const getSubmitFormDataList = async () => {
  const submitFormDataList = await Promise.all(
    editItemRef.value.map((item) => item.getSubmitFormData()),
  );

  if (submitFormDataList.some((item) => !item)) {
    return false;
  }

  return submitFormDataList.flat();
};

defineExpose({
  getSubmitFormDataList,
});
</script>

<template>
  <Modal class="w-10/12" />

  <FormCard :is-footer="false" title="入库明细">
    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ inBoundData?.inBoundItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template #default>
      <template
        v-for="(itemData, index) in formatData.inBoundItemList"
        :key="itemData.materialId"
      >
        <TriangleCard :number="index + 1" title="" class="mb-2 pt-2">
          <template #content>
            <MaterialInfo
              :material-item-info="itemData"
              @handle-material-code="handleMaterialCode"
              class="mb-2"
              :is-show-quantity="false"
            />

            <EditItem
              ref="editItemRef"
              v-if="allWarehouseListForMaterialList.length > 0"
              :material-item-data="itemData"
              :warehouse-list-for-material="
                allWarehouseListForMaterialList.find(
                  (item) => item.materialId === itemData.materialId,
                )
              "
              :in-change-warehouse="inBoundData.inChangeWarehouse"
              :in-quantity-limit="inBoundData.inQuantityLimit"
            />
          </template>
        </TriangleCard>
      </template>
    </template>
  </FormCard>
</template>
