import type { OnActionClickParams, VxeGridProps } from '@girant/adapter';

import type { Ref } from 'vue';

import { h } from 'vue';

import { ElInputNumber } from 'element-plus';

import MaterialInventorySelect from '#/components/material-inventory-select/Index.vue';

/** 物料信息 */
export const useGridOptions = ({
  onActionClick,
  warehouseId,
}: {
  onActionClick: (e: OnActionClickParams) => void;
  warehouseId: Ref<string>;
}): VxeGridProps => {
  return {
    columns: [
      { title: '序号', type: 'seq', width: 50 },
      {
        editRender: {},
        field: 'materialId',
        slots: {
          default: ({ row }) => {
            return row.materialId?.materialName;
          },
          edit: ({ $table, row }) => {
            if (!warehouseId.value) {
              return h('span', null, '请先选择仓库');
            }
            return h(MaterialInventorySelect, {
              modelValue: row.materialId,
              warehouseId: warehouseId.value,
              onChange: async (materialId, selectedItems) => {
                if (!materialId) {
                  $table.setRow(row, {
                    materialId: undefined,
                    materialSpecs: undefined,
                    baseUnitLabel: undefined,
                    locationAbatchNumber: undefined,
                    oldLocationId: undefined,
                    batchNumber: undefined,
                    inventory: undefined,
                    targetLocationId: undefined,
                    transferQuantity: undefined,
                    materialCode: undefined,
                  });
                  return;
                }

                $table.setRow(row, {
                  targetLocationId: undefined,
                });

                const materialDetailRes = selectedItems;
                const rowData = {
                  ...materialDetailRes,
                  materialId: {
                    materialId,
                    materialName: materialDetailRes?.materialName,
                  },
                };
                $table.setRow(row, rowData);
              },
              valueKey: 'materialId',
            });
          },
        },
        title: '物料选择',
        width: 180,
      },
      {
        field: 'materialCode',
        title: '物料编号',
        width: 140,
      },
      {
        field: 'materialSpecs',
        title: '规格型号',
        width: 180,
        showOverflow: false,
      },
      {
        field: 'baseUnitLabel',
        title: '基本单位',
        width: 90,
      },
      {
        slots: { default: 'locationAbatchNumber' },
        field: 'locationAbatchNumber',
        title: '原库位-批次号',
        minWidth: 430,
      },
      {
        field: 'oldLocationId',
        title: '原库位ID',
        visible: false,
      },
      {
        field: 'batchNumber',
        title: '批次号',
        visible: false,
      },
      {
        field: 'inventory',
        title: '库存',
        visible: false,
      },
      {
        slots: { default: 'targetLocationId' },
        field: 'targetLocationId',
        title: '目标库位',
        width: 180,
      },

      {
        editRender: {},
        slots: {
          default: ({ row }) => {
            return row.transferQuantity;
          },
          edit: ({ $table, row }) => {
            return h(ElInputNumber, {
              modelValue: row.transferQuantity,
              max: row.inventory,
              min: 0,
              controlsPosition: 'right',
              disabled: !row.materialId?.materialId || !row.inventory,
              onChange: (value) => {
                $table.setRow(row, { transferQuantity: value });
              },
            });
          },
        },
        field: 'transferQuantity',
        title: '仓内调拨数量',
        width: 160,
      },
      {
        align: 'center',
        cellRender: {
          attrs: {
            onClick: onActionClick,
          },
          name: 'CellOperation',
          options: ['delete'],
        },
        title: '操作',
        width: 60,
        fixed: 'right',
      },
    ],
    editRules: {
      materialId: [
        { message: '物料不能为空', required: true, trigger: 'blur' },
      ],
      locationAbatchNumber: [
        { message: '原库位-批次号不能为空', required: true, trigger: 'blur' },
      ],
      targetLocationId: [
        { message: '目标库位不能为空', required: true, trigger: 'blur' },
      ],
      transferQuantity: [
        { message: '数量不能为空', required: true, trigger: 'blur' },
        {
          validator: ({ cellValue }) => {
            if (cellValue === null) {
              return new Error('数量不能为空');
            }

            if (cellValue === 0) {
              return new Error('数量不能为0');
            }
          },
          trigger: 'blur',
        },
      ],
    },
    minHeight: 170,
    showOverflow: false,
  };
};
