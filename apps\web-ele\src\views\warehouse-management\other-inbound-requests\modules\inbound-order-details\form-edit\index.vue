<script setup lang="ts">
import type { OnActionClickParams } from '@girant/adapter';

import { onMounted, ref, unref } from 'vue';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElInputNumber, ElMessage } from 'element-plus';

import { getInOutReqDocDetailInBound } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import MaterialAnalysis from '#/views/warehouse-management/other-outbound-requests/modules/material-analysis/index.vue';

import { useGridOptions } from './data';

const props = defineProps({
  /** 其它出入库申请单编号 */
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单ID */
  inOutReqDocId: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 表格ref */
const gridTable = ref<DynamicTable>();

/** 操作 */
function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
  }
}

/** 编辑表单表格*/
const gridOptions = useGridOptions(onActionClick);
/** 校验 */
const validateForm = async () => {
  const isValid = await gridTable.value.tableValidate();
  if (isValid) {
    return false;
  }
  return true;
};
/** 获取表单数据 */
const getFormData = async () => {
  const data = await gridTable.value.getTableFullData();
  // 提取出需要的字段
  const result = data?.map((item: any) => ({
    materialId: item.materialId?.materialId,
    applyQuantity: item.applyQuantity,
  }));
  return result;
};
/** 获取表单数据 原始 */
const getFormDataOrigin = async () => {
  const data = await gridTable.value.getTableFullData();
  const result = data?.map((item: any) => ({
    ...item,
    materialId: item.materialId?.materialId,
  }));
  return result;
};
/** 根据其它出入库申请单编号id获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInOutReqDocDetailInBound(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
      true,
    );
    // 处理数据
    data.reqItemList?.forEach((item: any) => {
      item.materialId = {
        materialId: item.materialId,
        materialName: item.materialName,
      };
      item.applyQuantity = item.applyQuantitySum;
    });
    // 赋值
    await gridTable.value.setTableData(data.reqItemList);
  } catch (error) {
    console.error(error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 加载解析的数据到列表 */
const returnResult = async (data: any[]) => {
  // 处理返回的数据
  data.forEach((item: any) => {
    item.materialId = {
      materialId: item.materialId,
      materialName: item.materialName,
    };
  });
  const gridApi = gridTable.value.getGridTableIns()[1];
  const grid = gridApi.grid;
  if (grid) {
    await grid.loadData(data);
  }
};
onMounted(() => {
  if (props.inOutReqDocId || props.inOutReqDocNumber) {
    getData();
  }
});
defineExpose({
  getFormData,
  validateForm,
});
const popoverRef = ref();
const buttonRef = ref();
const onClickOutside = () => {
  unref(popoverRef).popperRef?.delayHide?.();
};
</script>
<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>入库申请明细</span>
    </template>
    <ElPopover
      ref="popoverRef"
      :virtual-ref="buttonRef"
      trigger="click"
      title="With title"
      virtual-triggering
    >
      <span> Some content </span>
    </ElPopover>
    <MaterialAnalysis
      class="mb-4"
      :get-exist-material-list="getFormDataOrigin"
      @return-result="returnResult"
    />
    <DynamicTable
      ref="gridTable"
      :grid-options="gridOptions"
      class="border"
      :table-options="{
        gridClass: 'pr-0 pl-0 pt-0',
      }"
    >
      <template #applyQuantity="{ row }">
        <ElInputNumber v-model="row.applyQuantity" :min="1" />
      </template>
      <template #CellOperation="scope">
        <ElButton link >
          删除
        </ElButton>
      </template>
    </DynamicTable>
  </FormCard>
</template>
<style scoped>
:deep(.vxe-grid--bottom-wrapper > div:first-child) {
  display: flex;
  justify-content: center;
  margin-top: 0;
}
</style>
