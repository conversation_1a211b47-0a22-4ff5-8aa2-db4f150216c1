<script setup lang="ts">
import { computed, defineAsyncComponent, markRaw } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import ViewOrigDoc from '#/views/warehouse-management/other-inbound-requests/modules/FormView.vue';

import ViewCloseBoundModal from './ViewCloseBoundModal.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  docStatus: {
    default: '',
    type: String,
  },
});

/** 关闭出入库弹窗 */
const [CloseModal, closeModalApi] = useVbenModal({
  connectedComponent: ViewCloseBoundModal,
  destroyOnClose: true,
});

/** 申请单弹窗 */
const [OrigDocModal, origDocModalApi] = useVbenModal({
  connectedComponent: ViewOrigDoc,
  destroyOnClose: true,
});

const InboundInfo = computed(() => {
  switch (props.docStatus) {
    // 取消审核中/已关闭
    case 'cancelAudit':
    case 'closed': {
      return markRaw(defineAsyncComponent(() => import('./closed/index.vue')));
    }
    // 待入库
    case 'pending': {
      return markRaw(defineAsyncComponent(() => import('./pending/index.vue')));
    }

    // 已入库
    case 'stocked': {
      return markRaw(defineAsyncComponent(() => import('./already/index.vue')));
    }

    default: {
      return markRaw(defineAsyncComponent(() => import('./pending/index.vue')));
    }
  }
});

const handleViewInOutCancelDoc = (data: any) => {
  closeModalApi
    ?.setState({ title: '取消单详情' })
    .setData({
      inOutCancelDocId: data.inOutCancelDocId,
      inOutCancelDocNumber: data.inOutCancelDocNumber,
    })
    .open();
};

const handleViewOrigDoc = (data: any) => {
  origDocModalApi
    ?.setState({ title: '申请单详情' })
    .setData({
      inOutReqDocId: data.origDocId,
      inOutReqDocNumber: data.origDocNumber,
      showDelBtn: false,
    })
    .open();
};
</script>

<template>
  <component
    :is="InboundInfo"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    @view-in-out-cancel-doc="handleViewInOutCancelDoc"
    @view-orig-doc="handleViewOrigDoc"
    v-bind="$attrs"
  />

  <CloseModal class="h-full w-10/12" />

  <OrigDocModal class="h-full w-10/12" />
</template>
