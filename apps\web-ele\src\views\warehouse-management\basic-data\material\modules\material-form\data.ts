import type { VbenFormSchema } from '@vben/common-ui';

import { h } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { UploadFiles } from '@girant-web/upload-files-component';
/** 物料信息 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue);
      },
      fieldName: 'materialName',
      label: '物料名称',
      formItemClass: 'col-span-1',
    },
    {
      component: 'Input',
      fieldName: 'materialCode',
      label: '物料编号',
      formItemClass: 'col-span-1',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialAlias',
      label: '	物料别名',
      formItemClass: 'col-span-1',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialSpecs',
      label: '物料规格',
      formItemClass: 'col-span-full',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'baseUnit',
      label: '基本单位',
      formItemClass: 'col-span-1',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialAttributeLabel',
      label: '物料属性',
      formItemClass: 'col-span-1',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialTypeLabel',
      label: '物料大类',
      formItemClass: 'col-span-1',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'materialCategory',
      label: '物料细类',
      formItemClass: 'col-span-1',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '备注',
      formItemClass: 'col-span-full',
    },
    {
      component: h(ImageViewer, {
        imgCss: 'size-40',
        imgFit: 'cover',
        class: '!w-[160px]',
      }),
      modelPropName: 'imgId',
      fieldName: 'pictureFileId',
      label: '图片',
      formItemClass: 'col-span-full items-start',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', null, '暂无附件');
        }
        return h(UploadFiles, {
          serialNumber: props.modelValue,
          mode: 'readMode',
          tableProps: {
            maxHeight: '300',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start items-baseline',
    },
  ];
}
