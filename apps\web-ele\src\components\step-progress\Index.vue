<script setup lang="ts">
import { ref } from 'vue';

import { ElMessage } from 'element-plus';

import { getDocStatusList } from '#/api';

const props = defineProps<{
  docNumber: string;
}>();

// 定义步骤数据接口
interface stepItem {
  recordId: string;
  docStatus?: string;
  docStatusLabel?: string;
  docStatusType?: string;
  docStatusTypeLabel?: string;
  parentRecordId?: string;
  relationDesc?: string;
  triggerUserId?: string;
  triggerUserName?: string;
  triggerUserType?: string;
  triggerUserTypeLabel?: string;
  triggerTime?: string;
  isCurrent?: boolean;
  isHandle?: boolean;
  [key: string]: any;
}

// 步骤数据
const steps = ref<stepItem[]>([]);
const loading = ref(false);

const loadData = async (docNumber: string) => {
  try {
    loading.value = true;
    const data = await getDocStatusList(docNumber);
    steps.value = data;
  } catch (error) {
    console.error('加载单据流程数据失败:', error);
    ElMessage.error('加载单据流程数据失败');
  } finally {
    loading.value = false;
  }
};

if (props.docNumber) {
  loadData(props.docNumber);
}

// 获取当前步骤索引
const getCurrentStepIndex = () => {
  return steps.value.findIndex((step) => step.isCurrent === true);
};

const currentStep = ref(getCurrentStepIndex());

// 获取步骤颜色和样式类的函数
const getStepClasses = (step: stepItem) => {
  if (step.isHandle && !step.isCurrent) {
    return {
      textClass: 'text-green-500',
      borderBgClass: 'border-green-500 bg-green-100',
      bgClass: 'bg-green-500',
      fontBold: false,
    };
  } else if (!step.isHandle && step.isCurrent) {
    return {
      textClass: 'text-yellow-500',
      borderBgClass: 'border-yellow-500 bg-yellow-100',
      bgClass: 'bg-yellow-500',
      fontBold: true,
    };
  } else {
    return {
      textClass: 'text-gray-400',
      borderBgClass: 'border-gray-300 bg-gray-100',
      bgClass: 'bg-gray-400',
      fontBold: false,
    };
  }
};

// 获取分割线颜色类的函数
const getDividerClasses = (index: number) => {
  const isHandled = currentStep.value >= index + 1;
  return {
    textClass: isHandled ? 'text-green-500' : 'text-gray-400',
    bgClass: isHandled ? 'bg-green-500' : 'bg-gray-300',
  };
};
</script>

<template>
  <div class="step-progress flex items-center space-x-4" v-loading="loading">
    <template v-for="(step, index) in steps" :key="index">
      <!-- 步骤项 -->
      <div
        v-if="index > 0"
        class="-mt-6 flex flex-col items-center"
        :class="getDividerClasses(index).textClass"
      >
        <p class="mb-1 h-4 text-xs" :class="getDividerClasses(index).textClass">
          {{ steps[index]?.relationDesc || ' ' }}
        </p>
        <div class="h-0.5 w-32" :class="getDividerClasses(index).bgClass"></div>
        <p class="mt-1 h-4 text-xs" :class="getDividerClasses(index).textClass">
          {{ steps[index]?.triggerTime || ' ' }}
        </p>
      </div>
      <div
        class="flex flex-col items-center"
        :class="getStepClasses(step).textClass"
      >
        <div
          class="flex h-6 w-6 items-center justify-center rounded-full border-4"
          :class="getStepClasses(step).borderBgClass"
        >
          <div
            class="h-3 w-3 rounded-full"
            :class="getStepClasses(step).bgClass"
          ></div>
        </div>
        <p
          class="mt-1 text-xs"
          :class="{ 'font-bold': getStepClasses(step).fontBold }"
        >
          {{ step.docStatusLabel }}
        </p>
      </div>
    </template>
  </div>
</template>

<style scoped></style>
