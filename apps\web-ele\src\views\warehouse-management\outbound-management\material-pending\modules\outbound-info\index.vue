<script setup lang="ts">
import type { OutboundPendingApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElTag } from 'element-plus';

import { getOutboundDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  outBoundDocId: {
    type: String,
    default: '',
  },
  outBoundDocNumber: {
    type: String,
    default: '',
  },
});

const outboundData = ref<OutboundPendingApi.OutboundDocDetail>();

/** 获取出库数据 */
const getOutboundDocDetailHandle = async () => {
  try {
    const outboundRes = await getOutboundDocDetail({
      outBoundDocId: props.outBoundDocId,
      outBoundDocNumber: props.outBoundDocNumber,
      isQueryItem: false,
    });
    outboundData.value = outboundRes;
    return outboundRes;
  } catch {
    ElMessage.error('获取出库单据失败');
    return {} as OutboundPendingApi.OutboundDocDetail;
  }
};

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

onMounted(async () => {
  if (props.outBoundDocId || props.outBoundDocNumber) {
    const data = await getOutboundDocDetailHandle();
    if (data) {
      formApi.setValues(data);
    }
  } else {
    ElMessage.error('出库单据ID或出库单号不能为空');
  }
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>出库信息</span>
    </template>
    <template #default>
      <Form>
        <template #outBoundDocNumber="{ modelValue }">
          <div>
            {{ modelValue }}
            <ElTag v-if="outboundData?.isRectify" type="primary">补录</ElTag>
          </div>
        </template>
        <template #materialUserName="{ modelValue }">
          <div>
            {{ modelValue }}
            <span v-if="outboundData?.materialUserDeptName">
              ({{ outboundData?.materialUserDeptName }})
            </span>
          </div>
        </template>
        <template #applyUserName="{ modelValue }">
          <div>
            {{ modelValue }}
            <span v-if="outboundData?.applyUserDeptName">
              ({{ outboundData?.applyUserDeptName }})
            </span>
          </div>
        </template>

        <template #docProcess>
          <StepProgress
            v-if="outboundData?.outBoundDocNumber"
            :doc-number="outboundData?.outBoundDocNumber"
            class="min-h-[65px] overflow-x-auto"
          />
          <div v-else>/</div>
        </template>
      </Form>
    </template>
  </FormCard>
</template>
