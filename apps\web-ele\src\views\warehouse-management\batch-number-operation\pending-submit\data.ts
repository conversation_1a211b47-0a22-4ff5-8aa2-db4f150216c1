import type { VbenFormSchema, VxeTableGridOptions } from '@girant/adapter';

import type { EnumType } from '#/api/common';
import type { WarehouseInfoApi } from '#/api/warehouse-management';

import { h, markRaw } from 'vue';

import { ElInputTag, ElTag } from 'element-plus';

import { getEnumByName } from '#/api';
import { getWarehouseList } from '#/api/warehouse-management';

// 查询参数类型
export interface SearchParams {
  batchnumDocNumberList: string;
  warehouseIdList: string;
  docCode: string;
  modifyStartTime: string;
  modifyEndTime: string;
}

// 表格数据类型
export interface RowType {
  batchnumDocId: string;
  batchnumDocNumber: string;
  warehouseName: string;
  warehouseCode: string;
  warehouseId: string;
  docStatusLabel: string;
  docStatus: string;
  docCode: string;
  docCodeLabel: string;
  materialId: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'batchnumDocNumberList',
      label: '单据编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        clearable: true,
        placeholder: '请选择处理类型',
        afterFetch: (data: EnumType[]) => {
          const handleTypeList = data.map((item) => ({
            label: item.enumLabel,
            value: item.enumValue,
          }));
          return handleTypeList;
        },
        api: () => {
          return getEnumByName('WmBatchNumHandleTypeEnums');
        },
      },
      fieldName: 'docCode',
      formItemClass: 'col-span-1',
      label: '处理类型',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        multiple: true,
        maxCollapseTags: 1,
        collapseTags: true,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        placeholder: '请选择仓库',
        afterFetch: (data: WarehouseInfoApi.WarehouseList[]) => {
          const warehouseList = data.map((item) => ({
            label: item.warehouseName,
            value: item.warehouseId,
          }));
          return warehouseList;
        },
        api: () => {
          return getWarehouseList();
        },
      },
      fieldName: 'warehouseIdList',
      formItemClass: 'col-span-1',
      label: '仓库',
    },

    {
      component: 'Input',
      fieldName: 'modifyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },
  ];
}

/** 判断tag类型 */
const getTagType = (type: string) => {
  switch (type) {
    // 待审核
    case 'checking': {
      return 'warning';
    }
    // 已关闭
    case 'closed': {
      return 'info';
    }

    // 已完成
    case 'finish': {
      return 'success';
    }
    // 待执行
    case 'passed': {
      return 'primary';
    }
    // 待提交
    case 'pending': {
      return 'primary';
    }
    // 审核驳回
    case 'reject': {
      return 'danger';
    }

    default: {
      return 'info';
    }
  }
};

/** 表格 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      field: 'batchnumDocNumber',
      title: '单据编号',
      minWidth: 200,
    },
    {
      field: 'batchnumDocId',
      title: '批次号处理单据ID',
      visible: false,
    },
    {
      field: 'docCodeLabel',
      width: 150,
      title: '处理类型',
    },
    {
      field: 'docCode',
      title: '处理类型值',
      visible: false,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElTag,
            {
              type: getTagType(row.docStatus),
            },
            { default: () => row.docStatusLabel },
          );
        },
      },
      field: 'docStatusLabel',
      width: 120,
      title: '单据状态',
    },
    {
      field: 'docStatus',
      title: '单据状态值',
      visible: false,
    },
    {
      field: 'warehouseName',
      width: 200,
      title: '仓库',
    },
    {
      field: 'warehouseCode',
      title: '仓库编号',
      visible: false,
    },
    {
      field: 'warehouseId',
      title: '仓库ID',
      visible: false,
    },

    {
      slots: {
        default: 'operation',
      },
      align: 'center',
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 'auto',
      minWidth: 150,
    },
  ];
}
