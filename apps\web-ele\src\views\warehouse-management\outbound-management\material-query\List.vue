<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElButton, ElMessage, ElMessageBox, ElTooltip } from 'element-plus';

import {
  closePrepDoc,
  exportPrepDoc,
  getPrepDocPage,
  rollbackPrepDoc,
} from '#/api/warehouse-management/index';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      docStatusList?: string;
      executorEndTime?: string;
      executorStartTime?: string;
      materialUserList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      outBoundDocNumberList?: string;
      prepDocNumberList?: string;
      submitEndTime?: string;
      submitStartTime?: string;
      submitUserList?: string;
    }>,
    default: () => ({}),
  },
});

const prepDocId = ref<string>('');
const prepDocNumber = ref<string>('');
const outBoundDocId = ref<string>('');
const outBoundDocNumber = ref<string>('');

/** 提交时间 */
const submitTime = ref({
  // 开始时间
  startTime: props.params?.submitStartTime || '',
  // 结束时间
  endTime: props.params?.submitEndTime || '',
});

/** 备料时间 */
const executorTime = ref({
  // 开始时间
  startTime: props.params?.executorStartTime || '',
  // 结束时间
  endTime: props.params?.executorEndTime || '',
});

/** 出库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  // confirmText: '',
  destroyOnClose: true,
  onBeforeClose: () => {
    prepDocId.value = '';
    prepDocNumber.value = '';
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      submitTime.value = {
        startTime: '',
        endTime: '',
      };
      executorTime.value = {
        startTime: '',
        endTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.submitStartTime = submitTime.value.startTime;
          params.submitEndTime = submitTime.value.endTime;
          params.executorStartTime = executorTime.value.startTime;
          params.executorEndTime = executorTime.value.endTime;

          return await getPrepDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'supplierId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    docStatusList: props.params?.docStatusList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
    prepDocNumberList: props.params?.prepDocNumberList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
  });
});

function closeInboundModal() {
  formModalApi.close();
}

/** 数据导出 */
async function exportPrepDocHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.submitStartTime = submitTime.value.startTime;
    formValues.submitEndTime = submitTime.value.endTime;
    formValues.executorStartTime = executorTime.value.startTime;
    formValues.executorEndTime = executorTime.value.endTime;
    const response = await exportPrepDoc(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

function onBoundSuccess() {
  closeInboundModal();
  gridApi.query();
}

function onBoundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onActionClick(row: RowType, code: string) {
  switch (code) {
    case 'cancel': {
      closePrepDocHandle(row);
      break;
    }

    case 'return': {
      rollbackPrepDocHandle(row);
      break;
    }

    case 'view': {
      openPrepDocModal(row);
      break;
    }
  }
}

function openPrepDocModal(row: RowType) {
  prepDocId.value = row.prepDocId;
  prepDocNumber.value = row.prepDocNumber;
  outBoundDocId.value = row.outBoundDocId;
  outBoundDocNumber.value = row.outBoundDocNumber;
  formModalApi
    .setState({
      title: `备料单详情`,
    })
    .open();
}

/** 关闭备料单据 */
const closePrepDocHandle = async (row: RowType) => {
  if (
    await ElMessageBox.confirm('确定关闭备料单据吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    await closePrepDoc(row.prepDocId);
    ElMessage.success('取消备料成功');
    gridApi.query();
  }
};

/** 备料单据返仓 */
const rollbackPrepDocHandle = async (row: RowType) => {
  if (
    await ElMessageBox.confirm('确定返仓备料单据吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    await rollbackPrepDoc(row.prepDocId);
    ElMessage.success('返仓成功');
    gridApi.query();
  }
};
</script>

<template>
  <Page auto-content-height>
    <FormModal class="h-full w-10/12">
      <Form
        :prep-doc-id="prepDocId"
        :prep-doc-number="prepDocNumber"
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outBoundDocNumber"
        @bound-success="onBoundSuccess"
        @bound-loading="onBoundLoading"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>
    <Grid>
      <template #materialQueryOperation="{ row }">
        <ElButton type="info" @click="onActionClick(row, 'view')" link>
          查看
        </ElButton>
        <ElButton
          type="danger"
          @click="onActionClick(row, 'return')"
          link
          v-if="row.docStatus === 'prepared'"
          v-access:code="'wm:outbound:prep:rollback'"
        >
          返仓
        </ElButton>
        <ElButton
          type="danger"
          @click="onActionClick(row, 'cancel')"
          link
          v-if="row.docStatus === 'checking'"
          v-access:code="'wm:outbound:prep:close'"
        >
          取消备料
        </ElButton>
      </template>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, submitTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-executorTime>
        <ElDatePicker
          v-model="executorTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, executorTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="executorTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, executorTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportPrepDocHandle"
            v-access:code="'wm:outbound:prep:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
