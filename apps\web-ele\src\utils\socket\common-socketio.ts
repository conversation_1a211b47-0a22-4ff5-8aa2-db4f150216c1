import { ref } from 'vue';

import { useAppConfig } from '@vben/hooks';
import { useAccessStore } from '@vben/stores';

import { ElMessage } from 'element-plus';

import eventBus from './event-bus';

// 类型定义
type MessageType = 'PING' | 'SUBSCRIBE' | 'UNSUBSCRIBE' | string;
interface SocketMessage {
  type: MessageType;
  topic?: string;
  timestamp?: number;
  [key: string]: any;
}

interface ReceivedMessage extends SocketMessage {
  topic: string;
}

// 配置与状态
const { wsURL } = useAppConfig(import.meta.env, import.meta.env.PROD);
const accessToken = useAccessStore().accessToken;
const socket = ref<null | WebSocket>(null);
const subscribedTopics = new Set<string>();
const MAX_RECONNECT_ATTEMPTS = 5;
let reconnectAttempts = 0;
let isManualDisconnect = false;
let heartbeatInterval: NodeJS.Timeout | null = null;
let socketReadyPromise: null | Promise<void> = null;
let resolveSocketReady: (() => void) | null = null;

// 工具函数：处理事件数组
const processEvents = <T>(
  events: string | string[],
  handler: (event: string, arg: T) => void,
  arg: T,
) => {
  (Array.isArray(events) ? events : [events]).forEach((event) =>
    handler(event, arg),
  );
};

// WebSocket连接管理
const createSocket = () => {
  if (!wsURL) throw new Error('WebSocket URL 未配置');
  const wsUrl = new URL(`${wsURL}/ws`);
  accessToken && wsUrl.searchParams.append('token', accessToken);
  return new WebSocket(wsUrl.href);
};

const setupSocketEvents = () => {
  if (!socket.value) return;

  socketReadyPromise = new Promise((resolve) => {
    resolveSocketReady = resolve;
  });

  // 连接成功
  socket.value.addEventListener('open', () => {
    console.error('WebSocket 连接成功');
    resolveSocketReady?.();
    reconnectAttempts = 0;

    // 启动心跳
    heartbeatInterval = setInterval(
      () => sendMessage({ type: 'PING', timestamp: Date.now() }),
      30_000,
    );

    // 重连后恢复订阅
    [...subscribedTopics].forEach((topic) =>
      sendMessage({ type: 'SUBSCRIBE', topic }),
    );
  });

  // 消息处理
  socket.value.addEventListener('message', ({ data }) => {
    try {
      const parsed = JSON.parse(data) as ReceivedMessage;
      eventBus.emit(parsed.topic, parsed);
    } catch (error) {
      console.error('解析消息出错:', error);
    }
  });

  // 连接关闭
  socket.value.addEventListener('close', () => {
    console.error('WebSocket 连接已关闭');
    heartbeatInterval && clearInterval(heartbeatInterval);

    // 重连逻辑
    if (!isManualDisconnect && reconnectAttempts < MAX_RECONNECT_ATTEMPTS) {
      reconnectAttempts++;
      console.error(`尝试第 ${reconnectAttempts} 次重连...`);
      setTimeout(connectSocket, 5000);
    } else {
      isManualDisconnect = false;
      if (reconnectAttempts >= MAX_RECONNECT_ATTEMPTS) {
        ElMessage.warning('WebSocket 连接已断开，无法恢复');
      }
    }
  });

  // 错误处理
  socket.value.addEventListener('error', () => {
    ElMessage.error('WebSocket连接错误');
  });
};

// 核心功能实现
export const connectSocket = () => {
  if (socket.value?.readyState === WebSocket.OPEN) return socketReadyPromise;
  if (!accessToken) {
    ElMessage.error('请登录系统');
    return Promise.reject(new Error('未登录'));
  }

  try {
    socket.value = createSocket();
    setupSocketEvents();
    return socketReadyPromise;
  } catch (error: any) {
    ElMessage.error(`创建连接失败: ${error.message}`);
    return Promise.reject(error);
  }
};

const sendMessage = async (message: SocketMessage) => {
  if (!socketReadyPromise) throw new Error('WebSocket未初始化');
  await socketReadyPromise;

  if (socket.value?.readyState === WebSocket.OPEN) {
    socket.value.send(JSON.stringify(message));
  } else {
    throw new Error('WebSocket连接未就绪');
  }
};

export const subscribeTopic = async (topic: string) => {
  if (!topic) return ElMessage.error('请输入消息主题');
  if (subscribedTopics.has(topic)) return;

  await sendMessage({ type: 'SUBSCRIBE', topic });
  subscribedTopics.add(topic);
};

export const unsubscribeTopic = async (topic: string) => {
  if (!topic || !subscribedTopics.has(topic)) return;

  await sendMessage({ type: 'UNSUBSCRIBE', topic });
  subscribedTopics.delete(topic);
};

export const disconnectSocket = () => {
  if (socket.value) {
    isManualDisconnect = true;
    socket.value.close();
    socket.value = null;
    subscribedTopics.clear();
    ElMessage.info('WebSocket 连接已关闭');
  }
};

// 自动连接
connectSocket();

// 对外API
export const WS = {
  connectSocket,
  disconnectSocket,
  on: (events: string | string[], callback: (data: any) => void) =>
    processEvents(
      events,
      (event) => {
        eventBus.on(event, callback);
        subscribeTopic(event);
      },
      callback,
    ),

  off: (events: string | string[], callback: (data: any) => void) =>
    processEvents(
      events,
      (event) => {
        eventBus.off(event, callback);
        if (eventBus.getListenerCount(event) === 0) {
          unsubscribeTopic(event);
        }
      },
      callback,
    ),
  emit: eventBus.emit.bind(eventBus),
};
