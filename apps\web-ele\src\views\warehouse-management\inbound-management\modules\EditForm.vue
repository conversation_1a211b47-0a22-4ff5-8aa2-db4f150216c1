<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { execInBoundDoc, reconfirm } from '#/api/warehouse-management/index';

import ViewInboundInfo from '../components/view-inbound-info/index.vue';
import EditMaterialsList from './materials-list/EditForm/index.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  docStatus: {
    default: '',
    type: String,
  },
});

const emits = defineEmits(['inboundSuccess', 'inboundError']);

/** 入库信息 */
const materialsListRef = ref<InstanceType<typeof EditMaterialsList>>();
// 二次确认的密码
const confirmPassword = ref('');

/** 二次确认弹窗 */
const [ConfirmModal, confirmModalApi] = useVbenModal({
  confirmText: '确认',
  onBeforeClose: () => {
    confirmPassword.value = '';
    return true;
  },
  destroyOnClose: true,
  onConfirm: () => {
    confirmSubmit();
  },
  centered: true,
  closeOnClickModal: false,
  fullscreenButton: false,
  showCancelButton: true,
});

/** 执行入库 */
const submitAllForm = async () => {
  const submitFormDataList =
    await materialsListRef.value?.getSubmitFormDataList();

  if (!submitFormDataList) {
    return false;
  }

  const formData = {
    inBoundDocId: props.inBoundDocId,
    actualItemList: submitFormDataList,
  };

  try {
    await execInBoundDoc(formData as unknown as InBoundDocApi.execInbound);
    ElMessage.success('入库成功');
    emits('inboundSuccess');
    return true;
  } catch (error: any) {
    emits('inboundError');
    if (error.code === 10_004_032) {
      confirmModalApi.open();
    } else {
      ElMessage.error('执行入库失败');
    }
    return false;
  }
};

/** 二次确认 */
const confirmSubmit = async () => {
  // 判断密码不能为空，不能是空格
  if (!confirmPassword.value || confirmPassword.value.trim() === '') {
    ElMessage.error('密码不能为空');
    return;
  }

  try {
    confirmModalApi.setState({ loading: true });
    await reconfirm(confirmPassword.value);
    submitAllForm();
    confirmModalApi.close();
  } catch {
    confirmModalApi.setState({ loading: false });
  }
};

defineExpose({
  submitAllForm,
});
</script>
<template>
  <ConfirmModal title="入库前需要二次确认">
    请输入当前账号密码：
    <el-input
      v-model="confirmPassword"
      placeholder="请输入密码"
      class="mt-2 w-full"
      type="password"
      show-password
      @keyup.enter="confirmSubmit"
    />
  </ConfirmModal>

  <ViewInboundInfo
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    :doc-status="docStatus"
  />

  <EditMaterialsList
    ref="materialsListRef"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
  />
</template>
