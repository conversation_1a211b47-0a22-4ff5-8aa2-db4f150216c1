<script setup lang="ts">
import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { execInBoundDoc, reconfirm } from '#/api/warehouse-management/index';

import QuantityComparison from '../components/QuantityComparisonModal.vue';
import ViewInboundInfo from '../components/view-inbound-info/index.vue';
import EditMaterialsList from './materials-list/EditForm/index.vue';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  docStatus: {
    default: '',
    type: String,
  },
});

const emits = defineEmits(['inboundSuccess', 'inboundError', 'inboundCancel']);

/** 入库信息 */
const materialsListRef = ref<InstanceType<typeof EditMaterialsList>>();
// 二次确认的密码
const confirmPassword = ref('');

/** 二次确认弹窗 */
const [ConfirmModal, confirmModalApi] = useVbenModal({
  confirmText: '确认',
  onBeforeClose: () => {
    confirmPassword.value = '';
    return true;
  },
  destroyOnClose: true,
  onConfirm: () => {
    confirmSubmit();
  },
  centered: true,
  closeOnClickModal: false,
  fullscreenButton: false,
  showCancelButton: true,
});

// 提示弹窗
const [QuantityComparisonModal, QuantityComparisonModalApi] = useVbenModal({
  connectedComponent: QuantityComparison,
  destroyOnClose: true,
  centered: true,
});

/** 执行入库 */
const execInboundHandle = async () => {
  const submitFormDataList =
    await materialsListRef.value?.getSubmitFormDataList();

  if (!submitFormDataList) {
    return false;
  }

  const formData = {
    inBoundDocId: props.inBoundDocId,
    actualItemList: submitFormDataList,
  };

  try {
    await execInBoundDoc(formData as unknown as InBoundDocApi.execInbound);
    ElMessage.success('入库成功');
    emits('inboundSuccess');
    return true;
  } catch (error: any) {
    emits('inboundError');
    if (error.code === 10_004_032) {
      confirmModalApi.open();
    } else {
      ElMessage.error('执行入库失败');
    }
    return false;
  }
};

/** 二次确认 */
const confirmSubmit = async () => {
  // 判断密码不能为空，不能是空格
  if (!confirmPassword.value || confirmPassword.value.trim() === '') {
    ElMessage.error('密码不能为空');
    return;
  }

  try {
    confirmModalApi.setState({ loading: true });
    await reconfirm(confirmPassword.value);
    execInboundHandle();
    confirmModalApi.close();
  } catch {
    confirmModalApi.setState({ loading: false });
  }
};

// 点击出库按钮显示提示弹窗
const showQuantityComparisonModal = async () => {
  // 获取填入与申请数量不一致的个数
  const applyQuantityEqualCount =
    await materialsListRef.value?.getApplyQuantityEqualCount();

  // 获取填入总数
  const entryQuantitySum = await materialsListRef.value?.getEntryQuantitySum();

  QuantityComparisonModalApi.setState({
    title: '提示',
  })
    .setData({
      // 不一致的个数
      applyQuantityEqualCount,
      // 填入总数
      entryQuantitySum,
      // 物料总数
      materialCount: materialsListRef.value?.materialCount,
      // 提交表单
      submitAllForm: () => {
        // modalApi.setState({ loading: true });
        execInboundHandle();
      },
      // 点击取消/关闭提示弹窗
      inboundCancel: () => {
        emits('inboundCancel');
      },
    })
    .open();
};

const submitAllForm = async () => {
  // 检验表单
  const validateAllFormData =
    await materialsListRef.value?.validateAllFormData();

  if (!validateAllFormData) {
    emits('inboundError');
    return;
  }

  // 校验填入数量
  const validateEntryQuantitySum =
    await materialsListRef.value?.validateEntryQuantitySum();

  if (!validateEntryQuantitySum) {
    emits('inboundError');
    return;
  }

  // 获取填入与申请数量不一致的个数
  const applyQuantityEqualCount =
    (await materialsListRef.value?.getApplyQuantityEqualCount()) || 0;

  if (applyQuantityEqualCount > 0) {
    showQuantityComparisonModal();
    return;
  }

  ElMessageBox.confirm('确认执行入库吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(() => {
      execInboundHandle();
    })
    .catch(() => {
      emits('inboundCancel');
    });
};

defineExpose({
  execInboundHandle,
  submitAllForm,
});
</script>
<template>
  <ConfirmModal title="入库前需要二次确认">
    请输入当前账号密码：
    <el-input
      v-model="confirmPassword"
      placeholder="请输入密码"
      class="mt-2 w-full"
      type="password"
      show-password
      @keyup.enter="confirmSubmit"
    />
  </ConfirmModal>

  <QuantityComparisonModal />

  <ViewInboundInfo
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
    :doc-status="docStatus"
  />

  <EditMaterialsList
    ref="materialsListRef"
    :in-bound-doc-id="inBoundDocId"
    :in-bound-doc-number="inBoundDocNumber"
  />
</template>
