import type { Recordable } from '@vben/types';

import { baseDataPath } from '#/api/path';
import { requestClient } from '#/api/request';

/** 查询物料信息分页列表*/
export async function getMaterialPage(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/getMaterialPage`,
    params,
  );
}

/** 查询可用物料信息分页列表*/
export async function getEnableMaterialPage(params: Recordable<any>) {
  return requestClient.post(
    `${baseDataPath}/base/material/getEnableMaterialPage`,
    params,
  );
}
