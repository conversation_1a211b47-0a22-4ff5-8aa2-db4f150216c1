{"title": "System Management", "dept": {"name": "Department", "title": "Department Management", "deptName": "Department Name", "status": "Status", "code": "Code", "createTime": "Create Time", "remark": "Remark", "operation": "Operation", "parentDept": "Parent Department", "setPermissions": "SetPermissions", "RelatedPositions": "RelatedPositions"}, "menu": {"title": "Menu Management", "parent": "<PERSON><PERSON>", "menuTitle": "Title", "menuName": "<PERSON>u Name", "name": "<PERSON><PERSON>", "type": "Type", "typeCatalog": "Catalog", "typeMenu": "<PERSON><PERSON>", "typeButton": "<PERSON><PERSON>", "typeLink": "Link", "typeEmbedded": "Embedded", "icon": "Icon", "activeIcon": "Active Icon", "activePath": "Active Path", "path": "Route Path", "component": "Component", "status": "Status", "authCode": "Auth Code", "badge": "Badge", "operation": "Operation", "linkSrc": "Link Address", "affixTab": "Affix In Tabs", "keepAlive": "Keep Alive", "hideInMenu": "Hide In Menu", "hideInTab": "Hide In Tabbar", "hideChildrenInMenu": "Hide Children In Menu", "hideInBreadcrumb": "Hide In Breadcrumb", "advancedSettings": "Other Settings", "activePathMustExist": "The path could not find a valid menu", "activePathHelp": "When jumping to the current route, \nthe menu path that needs to be activated must be specified when it does not display in the navigation menu.", "badgeType": {"title": "Badge Type", "dot": "Dot", "normal": "Text", "none": "None"}, "badgeVariants": "Badge Style"}, "role": {"list": "Role List", "name": "Role Management", "role": "Role", "id": "Role ID", "roleName": "Role", "code": "Role Code", "status": "Role Status", "remark": "Role Remark", "createTime": "Creation Time", "operation": "Operation", "permissions": "Permissions", "setPermissions": "Permissions", "RelatedPositions": "RelatedPositions"}, "user": {"user": "User", "relatedRoles": "Related Roles", "role": "Role", "remark": "Remark", "unlock": "Unlock", "avatar": "Avatar", "lock": "Lock", "deactivate": "Deactivate", "normal": "Normal", "password": "Password", "confirmPassword": "Confirm Password", "operation": "Operation", "account": "Account", "userState": "UserState", "name": "User", "userName": "UserName", "nickName": "<PERSON><PERSON><PERSON>", "contactNumber": "ContactNumber", "userType": "UserType", "lockingState": "LockingState", "isDeactivate": "Deactivation status", "creationTime": "CreationTime", "StartAndEndTime": "timeFrame", "startTime": "StartTime", "endTime": "EndTime", "editPwd": "EditPwd", "thisPwd": "ThisPwd", "expirationTime": "ExpirationTime", "loginTimeRange": "LoginTimeRange", "presence": "Presence", "userScope": "UserScope", "online": "Online", "offline": "Offline", "limitedUsers": "LimitedUsers", "ordinaryUsers": "OrdinaryUsers", "loginTerminal": "LoginTerminal", "loginTime": "LoginTime", "PC": "PC", "applet": "applet", "AndroidAPP": "AndroidAPP", "iOSAPP": "iOSAPP", "effectiveTime": "EffectiveTime", "cancelOnline": "CancelOnline"}, "perms": {"name": "Permission Management", "parent": "<PERSON><PERSON>", "menuName": "<PERSON>u Name", "clientType": "Client Type", "menuType": "Menu Type", "targetUrl": "Target URL", "menuIcon": "Menu Icon", "openType": "Open Type", "menuOrder": "Menu Order", "parentMenu": "<PERSON><PERSON>", "permissionCode": "Permission Code", "permissionName": "Permission Name", "deactivate": "Deactivate", "enable": "Enable", "isEnable": "Is Enable", "permissionDesc": "Permission Description", "PC": "PC", "APP": "APP", "all": "All", "menu": "<PERSON><PERSON>", "button": "<PERSON><PERSON>", "dataControl": "Data Control", "dataFiltering": "Data Filtering", "permsType": "Permission Type"}}