<script setup lang="ts">
import type { VbenFormSchema } from '@girant/adapter';

import type { BatchNumberOperationQueryApi } from '#/api/warehouse-management';

import { h, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getBatchnumDocDetail } from '#/api/warehouse-management';
import StepProgress from '#/components/step-progress/Index.vue';

const props = defineProps({
  batchnumDocId: {
    type: String,
    required: true,
  },
  batchnumDocNumber: {
    type: String,
    required: true,
  },
});

const loading = ref(false);

const batchnumDocDetail =
  ref<BatchNumberOperationQueryApi.GetBatchnumDocDetailResponse>(
    {} as BatchNumberOperationQueryApi.GetBatchnumDocDetailResponse,
  );

const schema: VbenFormSchema[] = [
  {
    component: 'Input',
    fieldName: 'docCode',
    label: '操作类型值',
    formItemClass: 'hidden',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue);
    },
    fieldName: 'docCodeLabel',
    formItemClass: 'col-span-2',
    label: '操作类型',
  },

  {
    component: 'Input',
    fieldName: 'materialId',
    label: '物料Id',
    formItemClass: 'hidden',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue);
    },
    fieldName: 'materialName',
    label: '物料',
    formItemClass: 'col-span-2',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue || '/');
    },
    fieldName: 'baseUnitLabel',
    label: '基本单位',
    formItemClass: 'col-span-1',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue || '/');
    },
    fieldName: 'materialSpecs',
    label: '规格型号',
    formItemClass: 'col-span-full',
  },

  {
    component: 'Input',
    fieldName: 'warehouseId',
    label: '所属仓库Id',
    formItemClass: 'hidden',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue);
    },
    fieldName: 'warehouseName',
    label: '所属仓库',
  },

  {
    component: 'Input',
    fieldName: 'remarkOptionList',
    formItemClass: 'col-span-full [&>div]:!overflow-visible mb-2',
    label: '取消原因',
  },

  {
    component: h(UploadFiles, {
      mode: 'readMode',
      tableProps: {
        maxHeight: '300',
      },
    }),
    modelPropName: 'serialNumber', // 绑定serialNumber进行回显
    fieldName: 'serialNumber',
    formItemClass: 'col-span-full',
    label: '附件',
  },
  {
    component: 'Input',
    fieldName: 'docProcess',
    label: '单据流程',
    formItemClass: 'col-span-full',
  },
];

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 100 },
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

const getBatchnumDocDetailHandle = async () => {
  try {
    loading.value = true;
    const res = await getBatchnumDocDetail({
      batchnumDocId: props.batchnumDocId,
      batchnumDocNumber: props.batchnumDocNumber,
    });

    batchnumDocDetail.value = res;
    return res;
  } catch {
    ElMessage.error('获取批次号处理单据详情失败');
    return null;
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (isEmpty(props.batchnumDocId) && isEmpty(props.batchnumDocNumber)) {
    ElMessage.error('批次号处理单据ID或编号不能为空');
    return;
  }

  const res = await getBatchnumDocDetailHandle();
  if (!res) {
    return;
  }

  formApi.setValues(res);
});

/** 单据状态 dictKey*/
const docStatusDict: { [key: string]: any } = {
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: '!text-red-500',
  },
  /** 已完成 */
  finish: {
    name: 'yiwancheng',
    color: '!text-lime-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: '!text-gray-300',
  },
};
</script>

<template>
  <IconFont
    v-if="docStatusDict[batchnumDocDetail.docStatus]"
    :name="docStatusDict[batchnumDocDetail.docStatus].name"
    :size="150"
    class="absolute right-20 top-14 z-40"
    :class="docStatusDict[batchnumDocDetail.docStatus].color"
  />
  <Form v-loading="loading">
    <template #remarkOptionList>
      <div v-if="batchnumDocDetail?.remarkOptionList" class="flex flex-col">
        <template
          v-for="item in batchnumDocDetail?.remarkOptionList"
          :key="item.optionId"
        >
          <ElTag type="primary" effect="dark" class="mb-2">
            {{ item.optionName }}
          </ElTag>
        </template>

        <span class="mt-1 text-xs text-gray-500">
          {{ batchnumDocDetail?.remark }}
        </span>
      </div>
      <div v-else>
        <span>
          {{ batchnumDocDetail?.remark }}
        </span>
      </div>
    </template>

    <template #docProcess>
      <StepProgress
        v-if="batchnumDocDetail?.batchnumDocNumber"
        :doc-number="batchnumDocDetail?.batchnumDocNumber"
        class="pt-[10px]"
      />
      <div v-else>/</div>
    </template>
  </Form>
</template>
