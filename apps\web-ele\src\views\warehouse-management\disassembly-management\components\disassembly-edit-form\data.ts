import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

import { getEnableProdBomPage, getEnableWarehouseListByMaterial } from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

/** 拆卸单信息 */
export function useFormSchema(
  onQuantityChange: (currentValue: number, oldValue: number) => Promise<void>,
  onChange: (materialId: any, bomMessage: any) => void,
  docTypeOptions: any[],
): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'assemblyDocNumber',
      label: '单据编号',
      componentProps: {
        disabled: true,
        placeholder: '系统默认自动生成',
      },
    },
    {
      component: 'RadioGroup',
      componentProps: {
        options: docTypeOptions,
      },
      // 隐藏选项
      dependencies: {
        show: false,
        triggerFields: ['docCode'],
      },
      fieldName: 'docCode',
      label: '单据类型',
      rules: 'selectRequired',
    },
    {
      component: h(RemoteSearchSelect, {
        fetchMethod: async ({
          keyword,
          pageNum,
          pageSize,
        }: {
          keyword: string;
          pageNum: number;
          pageSize: number;
        }) => {
          return await getEnableProdBomPage({
            materialName: keyword,
            pageNum,
            pageSize,
          });
        },
        labelKey: 'materialName',
        valueKey: 'materialId',
        subLabelKey: 'materialCode',
        inputPlaceholder: '请选择拆卸成品',
        placeholder: '请输入关键字搜索',
        onChange,
      }),
      fieldName: 'productMaterialId',
      label: '拆卸成品',
      rules: 'selectRequired',
    },
    {
      component: (props: any) => {
        const value = props.modelValue;
        return value
          ? h('span', null, value)
          : h('span', { class: 'text-gray-400' }, '请选择拆卸成品');
      },
      fieldName: 'baseUnitLabel',
      label: '基本单位',
      componentProps: {
        disabled: true,
        placeholder: '请选择拆卸成品',
      },
    },
    {
      component: (props: any) => {
        const value = props.modelValue;
        return value
          ? h('span', null, value)
          : h('span', { class: 'text-gray-400' }, '请选择拆卸成品');
      },
      fieldName: 'materialSpecs',
      label: '规格类型',
      componentProps: {
        disabled: true,
        placeholder: '请选择拆卸成品',
      },
    },
    {
      component: 'Select',
      componentProps: {
        maxCollapseTags: 1,
        filterable: true,
        collapseTagsTooltip: true,
        clearable: true,
        options: [],
        placeholder: '请选择仓库',
      },
      dependencies: {
        required(values) {
          return !!values.productMaterialId;
        },
        async componentProps(values) {
          if (values.productMaterialId?.materialId) {
            // 获取母件物料可使用仓库
            const res = await getEnableWarehouseListByMaterial({
              materialId: values.productMaterialId.materialId,
              docTypeCode: values.docCode,
            });
            // 提取出仓库id和仓库名称
            const warehouseList = res?.warehouseList.map((item) => ({
              label: `${item.warehouseName}-可用量${item.availableInventory}`,
              value: item.warehouseId,
              disabled: !(item.availableInventory > 0),
            }));

            // 如果当前选择的仓库不在可用仓库列表中，或者可用量为0，则清空选择
            const validWarehouseIds = new Set(
              warehouseList.map((item) => item.value),
            );
            if (
              values.warehouseId &&
              (!validWarehouseIds.has(values.warehouseId) ||
                warehouseList.find((item) => item.value === values.warehouseId)
                  ?.disabled)
            ) {
              values.warehouseId = '';
            }

            return {
              options: warehouseList,
            };
          } else {
            return {
              disabled: true,
            };
          }
        },

        triggerFields: ['productMaterialId'],
      },
      defaultValue: '',
      fieldName: 'warehouseId',
      formItemClass: 'col-span-1',
      label: '所属仓库',
      rules: 'selectRequired',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        showAllLevels: false,
      }),
      modelPropName: 'value',
      fieldName: 'executorUser',
      formItemClass: 'col-span-1',
      label: '执行人',
    },
    {
      component: 'InputNumber',
      fieldName: 'quantity',
      label: '拆卸数量',
      componentProps: {
        min: 1,
        precision: 0,
        onChange: onQuantityChange,
      },
      defaultValue: 1,
      rules: 'required',
    },
    {
      component: 'Textarea',
      fieldName: 'remark',
      label: '备注说明',
      formItemClass: 'col-span-full items-start',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
    },
    {
      component: h(UploadFiles, {
        mode: 'editMode',
        showOperateRegion: false,
        tableProps: {
          maxHeight: '300',
        },
      }),
      modelPropName: 'serialNumber',
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full items-start',
    },
  ];
}
