import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, warehousePath } from '../../path';

/** 仓库管理接口返回 */
export namespace WarehouseInfoApi {
  /** 仓库*/
  export interface WareRest {
    /** 仓库ID*/
    warehouseId: string;
    /** 仓库编号*/
    warehouseCode: string;
  }
  /** 仓库列表中的库位列表*/
  export interface locationListType {
    /** 是否启用 */
    isEnable: boolean;
    /** 是否锁库位 */
    isLock: boolean;
    /**	是否可备料 */
    isPrepMaterial: boolean;
    /** 是否临时仓 */
    isTemp: boolean;
    /** 库位编号*/
    locationCode: string;
    /** 所在位置描述 */
    locationDesc: string;
    /** 库位ID */
    locationId: string;
    /** 库位名称 */
    locationName: string;
  }

  /** 仓库分页 */
  export interface WarehouseInfo {
    [key: string]: any;
    records: locationListType[];
    total: string;
  }

  /** 仓库列表(含库位)*/
  export interface WarehouseList {
    /** 仓库ID*/
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 仓库位置描述 */
    locationDesc: string;
    /** 是否锁仓 */
    isLock: boolean;
    /**	是否启用 */
    isEnable: boolean;
    /**	库位列表 */
    locationList: locationListType[];
  }
  /** 仓库策略信息 */
  export interface Restriction {
    /** 仓库策略ID */
    restrictionId: string;
    /** 仓库ID */
    warehouseId: string;
    /** 操作限制单据类型。枚举wmLimitDocType */
    limitDocType: string;
    /**	操作限制单据类型名称 */
    limitDocTypeLabel: string;
    /** 操作限制单据*/
    limitDocs: Array<string>;
    /**	是否参与呆滞分析 */
    isObsoleteAnalysis: boolean;
    /** 物料类型限制列表，字典baseMaterialType */
    limitMaterialTypeList: Array<{
      /** 物料类型限制 */
      limitMaterialTypes: string;
      /** 物料类型限制名称*/
      limitMaterialTypesLabel: string;
    }>;
    /**	是否参与安全库存预警 */
    isSafetyStockWarn: boolean;
    /** 物料库存策略，枚举WmInOutStrategyEnums */
    inOutStrategy: string;
    /**	物料库存策略名称 */
    inOutStrategyLabel: string;
    /**	附件流水号 */
    serialNumber: string;
  }
  /** 获取仓库详细信息 */
  export interface WarehouseDetail {
    /** 仓库ID */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 仓库位置描述 */
    remark: string;
    /** 所在位置描述 */
    locationDesc: string;
    /** 负责人id */
    managerUserId: string;
    /** 负责人姓名 */
    managerUserName: string;
    /** 是否锁仓（true-锁仓，false-未锁仓） */
    isLock: boolean;
    /** 是否启用（true-启用，false-未启用） */
    isEnable: boolean;
    /** 附件流水号 */
    serialNumber: string;
  }
  /** 来源单据配置列表 */
  export interface OriginalDocConfigList {
    /**	单据类型编号 */
    docCode: string;
    /**	单据类型名称 */
    docName: string;
    /**	来源单据类型，枚举WmDocTypeEnums */
    originalDocType: string;
    /** 来源单据类型标签，枚举WmDocTypeEnums */
    /** 来源单据类型标签，枚举WmDocTypeEnums */
    originalDocTypeLabel: string;
  }

  /** 查询可用仓库列表(含库位) */
  export interface EnableWarehouse {
    /** 仓库ID */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 所在位置描述 */
    locationDesc: string;
    /** 是否锁仓 */
    isLock: boolean;
    /** 仓库ID */
    locationList: locationListType[];
  }

  /** 查询可出入库的仓库列表(含库位) */
  export interface ActiveWarehouse {
    /** 仓库ID */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 所在位置描述 */
    locationDesc: string;
    /** 仓库ID */
    locationList: locationListType[];
  }
}
/** 库位管理接口返回*/
export namespace LocationInfoApi {
  /** 库位 */
  export interface Location {
    /** 库位ID */
    locationId: string;
    /** 库位编号*/
    locationCode: string;
  }
  /** 库位详细信息 */
  export interface LocationDetail {
    /** 是否启用 */
    isEnable: boolean;
    /** 是否锁库位 */
    isLock: boolean;
    /**	是否可备料 */
    isPrepMaterial: boolean;
    /** 是否临时仓 */
    isTemp: boolean;
    /** 库位编号*/
    locationCode: string;
    /** 所在位置描述 */
    locationDesc: string;
    /** 库位ID */
    locationId: string;
    /** 库位名称 */
    locationName: string;
    /**	库位描述 */
    remark: string;
    /** 仓库ID */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
  }

  /** 库位分页 */
  export interface LocationInfo {
    [key: string]: any;
    records: LocationDetail[];
    total: string;
  }

  /** 库位策略信息 */
  export interface RestrictionDetail {
    /** 库位策略ID */
    restrictionId: string;
    /** 库位ID */
    locationId: string;
    /** 操作限制类型，枚举wmLimitDocType */
    limitDocType: string;
    /** 操作限制类型标签，枚举wmLimitDocType */
    limitDocTypeLabel: string;
    /** 操作限制的单据列表 */
    limitDocs: Array<string>;
    /** 物料类型限制列表，字典baseMaterialType */
    limitMaterialTypeList: Array<{
      /** 物料类型限制 */
      limitMaterialTypes: string;
      /** 物料类型限制名称*/
      limitMaterialTypesLabel: string;
    }>;
  }
}
/** 获取员工分页列表*/
export interface SystemUser {
  [key: string]: any;
  records: string;
  total: string;
}

// #region 仓库管理接口
/** 编辑仓库策略信息 */
export async function restrictionModWareRest(params: Recordable<any>) {
  return requestClient.post<WarehouseInfoApi.WareRest>(
    `${warehousePath}/wm/warehouse/restriction/modWareRest`,
    params,
  );
}
/** 修改仓库信息 */
export async function modWarehouse(params: Recordable<any>) {
  return requestClient.post<WarehouseInfoApi.WareRest>(
    `${warehousePath}/wm/warehouse/modWarehouse`,
    params,
  );
}
/** 导入仓库列表(含库位) */
export async function importWarehouse(data: { file: Blob | File }) {
  return requestClient.upload(
    `${warehousePath}/wm/warehouse/importWarehouse`,
    data,
  );
}
/** 获取仓库列表(含库位) */
export async function getWarehouseList(params?: Recordable<any>) {
  return requestClient.post<WarehouseInfoApi.WarehouseList[]>(
    `${warehousePath}/wm/warehouse/getWarehouseList`,
    params,
  );
}
/** 导出仓库列表(含库位)*/
export async function exportWarehouse(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/warehouse/exportWarehouse`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 导入仓库列表模板(含库位) 下载*/
export async function download() {
  return requestClient.post(
    `${warehousePath}/wm/warehouse/download`,
    {},
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 新增仓库信息 */
export async function saveWarehouse(params: Recordable<any>) {
  return requestClient.post<WarehouseInfoApi.WareRest>(
    `${warehousePath}/wm/warehouse/saveWarehouse`,
    params,
  );
}
/** 解锁仓库 */
export async function unlockWarehouse(warehouseId: string) {
  return requestClient.get<WarehouseInfoApi.WareRest>(
    `${warehousePath}/wm/warehouse/unlockWarehouse/${warehouseId}`,
  );
}
/** 获取仓库策略信息 */
export async function getWarehouseRestriction(
  warehouseId: string,
  warehouseCode?: string,
) {
  return requestClient.get<WarehouseInfoApi.Restriction>(
    `${warehousePath}/wm/warehouse/restriction/getRestrictionDetail`,
    {
      params: {
        warehouseId,
        warehouseCode,
      },
    },
  );
}
/** 锁定仓库 */
export async function lockWarehouse(warehouseId: string) {
  return requestClient.get<WarehouseInfoApi.WareRest>(
    `${warehousePath}/wm/warehouse/lockWarehouse/${warehouseId}`,
  );
}
/** 获取仓库详细信息*/
export async function getWarehouseDetail(
  warehouseId: string,
  warehouseCode?: string,
) {
  return requestClient.get<WarehouseInfoApi.WarehouseDetail>(
    `${warehousePath}/wm/warehouse/getWarehouseDetail`,
    {
      params: {
        warehouseId,
        warehouseCode,
      },
    },
  );
}

/** 查询可用仓库列表(含库位) */
export async function getEnableWarehouseList(params: Recordable<any> = {}) {
  return requestClient.post<WarehouseInfoApi.EnableWarehouse[]>(
    `${warehousePath}/wm/warehouse/getEnableWarehouseList`,
    params,
  );
}

/** 查询可出入库的仓库列表(含库位) */
export async function getActiveWarehouseList(params: Recordable<any> = {}) {
  return requestClient.post<WarehouseInfoApi.ActiveWarehouse[]>(
    `${warehousePath}/wm/warehouse/getActiveWarehouseList`,
    params,
  );
}

// #endregion

// #region 库位管理接口
/** 获取库位信息分页列表 */
export async function getLocationPage(params: Recordable<any>) {
  return requestClient.post<LocationInfoApi.LocationInfo>(
    `${warehousePath}/wm/location/getLocationPage`,
    params,
  );
}
/** 新增库位 */
export async function saveLocation(params: Recordable<any>) {
  return requestClient.post<LocationInfoApi.Location>(
    `${warehousePath}/wm/location/saveLocation`,
    params,
  );
}
/** 修改库位 */
export async function modLocation(params: Recordable<any>) {
  return requestClient.post<LocationInfoApi.Location>(
    `${warehousePath}/wm/location/modLocation`,
    params,
  );
}
/** 查询库位列表 */
export async function getLocationList(params?: Recordable<any>) {
  return requestClient.post<LocationInfoApi.LocationDetail[]>(
    `${warehousePath}/wm/location/getLocationList`,
    params,
  );
}
/** 解锁库位*/
export async function unlockLocation(locationId: string) {
  return requestClient.get<LocationInfoApi.Location>(
    `${warehousePath}/wm/location/unlockLocation/${locationId}`,
  );
}
/** 修改库位策略信息*/
export async function modLocaRest(params: Recordable<any>) {
  return requestClient.post<LocationInfoApi.Location>(
    `${warehousePath}/wm/location/restriction/modLocaRest`,
    params,
  );
}
/** 获取库位策略信息 */
export async function getRestrictionDetail(
  locationId: string,
  locationCode?: string,
) {
  return requestClient.get<LocationInfoApi.RestrictionDetail>(
    `${warehousePath}/wm/location/restriction/getRestrictionDetail`,
    {
      params: {
        locationId,
        locationCode,
      },
    },
  );
}
/** 锁定库位 */
export async function lockLocation(locationId: string) {
  return requestClient.get<LocationInfoApi.Location>(
    `${warehousePath}/wm/location/lockLocation/${locationId}`,
  );
}
/** 获取库位详细信息 */
export async function getLocationDetail(
  locationId: string,
  locationCode?: string,
) {
  return requestClient.get<LocationInfoApi.LocationDetail>(
    `${warehousePath}/wm/location/getLocationDetail`,
    {
      params: {
        locationId,
        locationCode,
      },
    },
  );
}
// #endregion

/** 获取员工分页列表*/
export async function getStaffPage(params: Recordable<any>) {
  return requestClient.post<SystemUser>(
    `${baseDataPath}/base/staff/getStaffPage`,
    params,
  );
}
