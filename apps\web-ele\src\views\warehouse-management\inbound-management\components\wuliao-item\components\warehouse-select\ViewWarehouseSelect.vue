<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../../types/index.ts';

defineProps({
  warehouseItemData: {
    type: Object as PropType<MaterialItem.WarehouseItemDataType>,
    default: () => ({}),
  },
});
</script>

<template>
  <span class="text-left text-gray-500">
    仓库：<span class="font-medium text-gray-700">{{
      warehouseItemData.warehouseName || '/'
    }}</span>
  </span>
</template>
