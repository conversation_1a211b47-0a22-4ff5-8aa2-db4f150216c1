<script setup lang="ts">
import type { mapType } from '#/components/perms-tree/type';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@vben/common-ui';

import { ElMessage, ElRadio, ElRadioGroup } from 'element-plus';

import { getEnumByName, getWareTransferDocDetail } from '#/api';
import FormCard from '#/components/form-card/Index.vue';
import RemarkOptionSelect from '#/components/remark-option-select/index.vue';

import { getWarehouseList, useFormSchema } from './data';

const props = defineProps({
  /** 库存调拨单据ID */
  transferDocId: {
    type: String,
    default: '',
  },
  /** 库存调拨单据编号 */
  transferDocNumber: {
    type: String,
    default: '',
  },
});

const emit = defineEmits(['changeOldWarehouse', 'changeTargetWarehouse']);

const loading = ref(false);
/** 拆卸单据类型枚举内容 */
const wmTransferTypeEnums = ref<mapType[]>([]);
/** 可用仓库列表 */
const warehousesList = ref<any[]>([]);
/** 单据类型 */
const docCode = ref('');

/** 监听调出仓库变化 */
const oldWarehouseChange = async (warehouseId: string) => {
  emit('changeOldWarehouse', warehouseId);
};

/** 监听调入仓库变化 */
const targetWarehouseChange = async (warehouseId: string) => {
  emit('changeTargetWarehouse', warehouseId);
};

/** 调拨信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: [],
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});

onMounted(async () => {
  getData();
});

const getData = async () => {
  try {
    loading.value = true;
    // 获取前置准备数据
    const resWmTransferTypeEnums = await getEnumByName('WmTransferTypeEnums');
    wmTransferTypeEnums.value = resWmTransferTypeEnums?.map((item) => {
      return {
        label: item.enumLabel,
        value: item.enumValue,
      };
    });
    /** 获取调拨单据信息 */
    if (props.transferDocId || props.transferDocNumber) {
      const data = await getWareTransferDocDetail(
        props.transferDocId,
        props.transferDocNumber,
      );
      // 数据处理
      const processedRemarkList = data.remarkOptionList?.map(
        (item) => item.optionId,
      );
      const formData = {
        ...data,
        remarkOptionList: processedRemarkList,
      };
      // 赋值
      docCode.value = data.docCode;
      warehousesList.value = await getWarehouseList(data.docCode);
      formApi.setState({
        schema: useFormSchema(
          warehousesList,
          oldWarehouseChange,
          targetWarehouseChange,
        ),
      });
      formApi.setValues(formData);
    } else {
      formApi.setState({
        schema: useFormSchema(
          warehousesList,
          oldWarehouseChange,
          targetWarehouseChange,
        ),
      });
    }
  } catch {
    ElMessage.error('数据获取失败');
  } finally {
    loading.value = false;
  }
};

/** 单据类型改变 */
const changeDocCode = async (newDocCode: any) => {
  formApi.setValues({
    oldWarehouseId: '',
    targetWarehouseId: '',
  });
  emit('changeOldWarehouse', '');
  emit('changeTargetWarehouse', '');
  docCode.value = newDocCode;
  warehousesList.value = await getWarehouseList(newDocCode);
  formApi.updateSchema(
    useFormSchema(warehousesList, oldWarehouseChange, targetWarehouseChange),
  );
};

const handleRemarkOptionListChange = (_: any, isDescRequired: boolean) => {
  formApi.updateSchema([
    {
      rules: isDescRequired ? 'required' : '',
      fieldName: 'remark',
    },
  ]);
};

/** 校验 */
const validateForm = async () => {
  // 校验表单
  const verification = await formApi.validate();
  if (!verification.valid) {
    return false;
  }
  return true;
};

/** 提交 */
const getFormData = async () => {
  const data = await formApi.getValues();
  return data;
};

/** 对外开放方法 */
defineExpose({
  getFormData,
  validateForm,
});
</script>

<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>调拨信息</span>
    </template>
    <Form>
      <template #docCode="row">
        <b
          v-if="wmTransferTypeEnums.length === 0"
          class="w-[100px] text-[14px]"
        >
          暂无数据
        </b>
        <ElRadioGroup v-bind="row" @change="changeDocCode">
          <ElRadio
            v-for="item in wmTransferTypeEnums"
            :key="item.value"
            :value="item.value"
          >
            {{ item.label }}
          </ElRadio>
        </ElRadioGroup>
      </template>
      <template #remarkOptionList="row">
        <RemarkOptionSelect
          :model-value="row.value"
          :doc-code="docCode"
          doc-field-code="remark"
          @update:model-value="
            (optionIds) => {
              formApi.setFieldValue('remarkOptionList', optionIds);
            }
          "
          @change="handleRemarkOptionListChange"
        />
      </template>
    </Form>
  </FormCard>
</template>
