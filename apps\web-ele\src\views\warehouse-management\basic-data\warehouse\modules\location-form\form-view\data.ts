import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

/** 基础资料 查看 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'warehouseName',
      label: '所属仓库',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'locationCode',
      label: '库位编号',
    },
    {
      component: 'div',
      fieldName: 'locationName',
      formItemClass: 'col-span-2',
      label: '库位名称',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'locationDesc',
      label: '所在位置',
      formItemClass: 'col-span-full',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '库位描述',
      formItemClass: 'col-span-full',
    },
  ];
}
