<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from '../../types';

defineProps({
  warehouseItemData: {
    type: Object as PropType<MaterialItem.WarehouseItemDataType>,
    default: () => ({}),
  },
});
</script>

<template>
  <div class="flex flex-col gap-2.5">
    <template
      v-for="item in warehouseItemData.locationList"
      :key="item.locationId"
    >
      <div class="flex items-center">
        <div class="flex-1 text-gray-500">
          库位批次：<span class="text-gray-700">{{
            item.locationName || '/'
          }}</span>
          <span class="text-gray-700"> ({{ item.batchNumber || '/' }}) </span>
        </div>
        <div class="w-[90px] text-gray-500">
          数量：<span class="font-medium text-gray-700">{{
            item.quantity
          }}</span>
        </div>
      </div>
    </template>
  </div>
</template>
