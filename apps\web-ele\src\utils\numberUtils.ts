import Decimal from 'decimal.js';

// 获取两个数中最大的小数位数
export function getMaxDecimalPlaces(
  num1: number | string,
  num2: number | string,
): number {
  const getDecimalPlaces = (num: number | string) => {
    const numStr = num.toString();
    const decimalIndex = numStr.indexOf('.');
    return decimalIndex === -1 ? 0 : numStr.length - decimalIndex - 1;
  };
  return Math.max(getDecimalPlaces(num1), getDecimalPlaces(num2));
}

// 加法运算
export function add(
  num1: number | string,
  num2: number | string,
  decimalPlaces?: number,
): number {
  const decimalNum1 = new Decimal(num1);
  const decimalNum2 = new Decimal(num2);
  const places =
    decimalPlaces === undefined
      ? getMaxDecimalPlaces(num1, num2)
      : decimalPlaces;
  return Number.parseFloat(decimalNum1.plus(decimalNum2).toFixed(places));
}

// 减法运算
export function subtract(
  num1: number | string,
  num2: number | string,
  decimalPlaces?: number,
): number {
  const decimalNum1 = new Decimal(num1);
  const decimalNum2 = new Decimal(num2);
  const places =
    decimalPlaces === undefined
      ? getMaxDecimalPlaces(num1, num2)
      : decimalPlaces;
  return Number.parseFloat(decimalNum1.minus(decimalNum2).toFixed(places));
}

// 乘法运算
export function multiply(
  num1: number | string,
  num2: number | string,
  decimalPlaces?: number,
): number {
  const decimalNum1 = new Decimal(num1);
  const decimalNum2 = new Decimal(num2);
  const places =
    decimalPlaces === undefined
      ? getMaxDecimalPlaces(num1, num2)
      : decimalPlaces;
  return Number.parseFloat(decimalNum1.mul(decimalNum2).toFixed(places));
}

// 除法运算
export function divide(
  num1: number | string,
  num2: number | string,
  decimalPlaces?: number,
): number {
  const decimalNum1 = new Decimal(num1);
  const decimalNum2 = new Decimal(num2);
  const places =
    decimalPlaces === undefined
      ? getMaxDecimalPlaces(num1, num2)
      : decimalPlaces;
  return Number.parseFloat(decimalNum1.div(decimalNum2).toFixed(places));
}

// 金额格式化，保留指定小数位数，添加千位分隔符
export function formatAmount(
  amount: number | string,
  decimalPlaces: number = 2, // 默认保留两位小数
  options?: {
    decimalSeparator?: string; // 小数点分隔符
    groupSeparator?: string; // 千位分隔符
    useGrouping?: boolean; // 是否使用千位分隔符
  },
): string {
  // 基础校验
  if (amount === undefined || amount === null) return '0';

  const {
    useGrouping = true,
    decimalSeparator = '.',
    groupSeparator = ',',
  } = options || {};

  try {
    // 转换为 Decimal 对象
    const decimalAmount = new Decimal(amount);

    // 处理特殊值
    if (decimalAmount.isNaN()) return '0';

    // 格式化并分割
    const formatted = decimalAmount.toFixed(decimalPlaces);
    const parts = formatted.split('.');

    // 确保整数部分存在
    const integerPart = parts[0] || '0';
    const fractionalPart = parts.length > 1 ? parts[1] : '';

    // 添加千位分隔符
    const groupedInteger = useGrouping
      ? integerPart.replaceAll(/\B(?=(\d{3})+(?!\d))/g, groupSeparator)
      : integerPart;

    // 组合结果
    return fractionalPart
      ? `${groupedInteger}${decimalSeparator}${fractionalPart}`
      : groupedInteger;
  } catch (error) {
    console.error('金额格式化失败:', error);
    return '0';
  }
}

// 取绝对值
export function abs(num: number | string): number {
  const decimalNum = new Decimal(num);
  return decimalNum.abs().toNumber();
}

// 取最大值
export function max(...nums: (number | string)[]): number {
  const decimalNums = nums.map((num) => new Decimal(num));
  const maxDecimal = Decimal.max(...decimalNums);
  return maxDecimal.toNumber();
}

// 取最小值
export function min(...nums: (number | string)[]): number {
  const decimalNums = nums.map((num) => new Decimal(num));
  const minDecimal = Decimal.min(...decimalNums);
  return minDecimal.toNumber();
}

// 取幂运算
export function pow(num: number | string, exponent: number | string): number {
  const decimalNum = new Decimal(num);
  const decimalExponent = new Decimal(exponent);
  return decimalNum.pow(decimalExponent).toNumber();
}

// 四舍五入到指定小数位数
export function round(num: number | string, decimalPlaces: number): number {
  const decimalNum = new Decimal(num);
  return Number(decimalNum.toFixed(decimalPlaces, Decimal.ROUND_HALF_UP));
}

// 向上取整到指定精度
export function ceilToPrecision(
  num: number | string,
  precision: number,
): number {
  const decimalNum = new Decimal(num);
  return Number(decimalNum.toFixed(precision, Decimal.ROUND_CEIL));
}

// 向下取整到指定精度
export function floorToPrecision(
  num: number | string,
  precision: number,
): number {
  const decimalNum = new Decimal(num);
  return Number(decimalNum.toFixed(precision, Decimal.ROUND_FLOOR));
}

// 转换为科学计数法字符串，保留指定精度
export function toExponential(num: number | string, precision: number): string {
  const decimalNum = new Decimal(num);
  return decimalNum.toExponential(precision);
}

// 从科学计数法字符串转换为普通数字
export function fromExponential(numStr: string): number {
  const decimalNum = new Decimal(numStr);
  return decimalNum.toNumber();
}
