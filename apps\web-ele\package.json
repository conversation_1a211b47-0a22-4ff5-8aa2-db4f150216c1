{"name": "girant-system-web", "version": "1.0.0", "homepage": "", "license": "MIT", "author": {"name": "girant", "url": "https://www.girant.com/"}, "type": "module", "scripts": {"build": "pnpm vite build --mode production", "build:dev": "pnpm vite build --mode analyze", "build:test": "pnpm vite build --mode test", "build:trial": "pnpm vite build --mode trial", "dev": "pnpm vite --mode development"}, "imports": {"#/*": "./src/*"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@girant-web/dynamic-table-component": "catalog:", "@girant-web/file-view-component": "1.0.3", "@girant-web/img-view-component": "1.0.9", "@girant-web/tree-component": "0.0.9", "@girant-web/upload-files-component": "1.2.0", "@girant-web/upload-pic-component": "1.1.7", "@girant/adapter": "workspace:*", "@girant/locales": "workspace:*", "@vben-core/menu-ui": "workspace:*", "@vben/access": "workspace:*", "@vben/common-ui": "workspace:*", "@vben/constants": "workspace:*", "@vben/hooks": "workspace:*", "@vben/icons": "workspace:*", "@vben/layouts": "workspace:*", "@vben/locales": "workspace:*", "@vben/plugins": "workspace:*", "@vben/preferences": "workspace:*", "@vben/request": "workspace:*", "@vben/stores": "workspace:*", "@vben/styles": "workspace:*", "@vben/types": "workspace:*", "@vben/utils": "workspace:*", "@vueuse/core": "catalog:", "compressorjs": "^1.2.1", "dayjs": "catalog:", "decimal.js": "^10.5.0", "element-plus": "catalog:", "pinia": "catalog:", "vue": "catalog:", "vue-router": "catalog:"}, "devDependencies": {"unplugin-element-plus": "catalog:"}}