import { requestClient } from '#/api/request';

import { systemPath } from '../path';

// 定义数据结构
interface PermissionItem {
  permissionCode: string;
}
/**
 * 获取当前用户所有菜单
 */
export async function getMenusApi() {
  return requestClient.get<string[]>(`${systemPath}/perms/mi/getMenus`);
}

/**
 * 获取当前用户所有权限
 */
export async function getPermsApi() {
  return requestClient.get<PermissionItem[]>(`${systemPath}/perms/mi/getPerms`);
}
