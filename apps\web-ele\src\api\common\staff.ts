import { requestClient } from '#/api/request';

import { baseDataPath } from '../path';
/** 我的员工信息 */
export interface StaffInfoType {
  // 员工ID
  staffId: string;
  // 员工编号
  staffCode: string;
  // 员工姓名
  staffName: string;
  // 头像附件id
  avatarId: string;
  // 邮箱
  email: string;
  // 性别值
  genderType: string;
  // 性别标签
  genderTypeLabel: string;
  // 任职状态值
  workStatus: string;
  // 任职状态标签
  workStatusLabel: string;
  // 所属部门ID
  deptId: string;
  // 所属部门编号
  deptCode: string;
  // 所属部门名称
  deptName: string;
  // 入职日期
  hireTime: string;
  // 入职到现在总月份-未满一个月则为0
  hireMonth: number;
  // 离职日期
  terminationTime: string;
  // 主任岗位ID
  directorPositionId: string;
  // 主任岗位编号
  directorPositionCode: string;
  // 主任岗位名称
  directorPositionName: string;
  // 主任岗位类型 字典值：basePositionType
  directorPositionType: string;
  // 主任岗位类型 标签标签
  directorPositionTypeLabel: string;
  // 兼任岗位列表
  concurrentPositionList: {
    // 兼任岗位编号
    concurrentPositionCode: string;
    // 兼任岗位ID
    concurrentPositionId: string;
    // 兼任岗位名称
    concurrentPositionName: string;
    // 兼任岗位类型 字典值：basePositionType
    concurrentPositionType: string;
    // 兼任岗位类型 标签标签
    concurrentPositionTypeLabel: string;
  }[];
}
/** 获取我的员工信息 */
export async function getStaffInfo() {
  return requestClient.get<StaffInfoType>(
    `${baseDataPath}/base/staff/mi/getStaffInfo`,
  );
}
