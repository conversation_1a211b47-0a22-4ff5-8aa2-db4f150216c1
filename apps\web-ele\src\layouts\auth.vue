<script lang="ts" setup>
import { computed } from 'vue';

import { AuthPageLayout } from '@vben/layouts';
import { preferences } from '@vben/preferences';

const appName = computed(() => preferences.login.appName);
const logo = computed(() => preferences.login.watermark);
const sloganImage = computed(() => preferences.login.sloganImage);
const toolbar = computed(() => preferences.login.toolbar);
const bottomTips = computed(() => preferences.login.bottomTips);
const watermark = computed(() => preferences.login.watermark);
const clickLogo = () => {};
</script>

<template>
  <AuthPageLayout
    :app-name="appName"
    :logo="logo"
    :toolbar="toolbar"
    :slogan-image="sloganImage"
    :bottom-tips="bottomTips"
    :watermark="watermark"
    :click-logo="clickLogo"
  >
    <!-- 自定义工具栏 -->
    <!-- <template #toolbar></template> -->
  </AuthPageLayout>
</template>
