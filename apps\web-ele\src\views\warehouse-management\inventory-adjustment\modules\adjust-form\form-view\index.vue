<script setup lang="ts">
import type { InventoryAdjustment } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElScrollbar, ElTag } from 'element-plus';

import { getInvcAdjustDoc } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 库存调整单据ID */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 调整单数据 */
const data = ref<InventoryAdjustment.InvcAdjustDoc>();
/** 调整原因选项列表*/
const remarkOptionList =
  ref<InventoryAdjustment.InvcAdjustDoc['remarkOptionList']>();
/** 调整信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});
/** 单据状态 dictKey*/
const docStatusDict: { [key: string]: any } = {
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: '!text-red-500',
  },
  /** 已完成 */
  finish: {
    name: 'yiwancheng',
    color: '!text-lime-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: '!text-gray-300',
  },
};
/** 当前单据状态 */
const docStatus = ref('');
/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    const res = await getInvcAdjustDoc(
      props.invcAdjustDocId,
      props.invcAdjustDocNumber,
    );
    data.value = res;
    docStatus.value = res?.docStatus;
    remarkOptionList.value = res.remarkOptionList;
    // 赋值
    formApi.setValues(res);
  } catch (error) {
    console.error(error);
    // ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  if (props.invcAdjustDocId || props.invcAdjustDocNumber) {
    await getData();
  }
});
defineExpose({
  formApi,
  Form,
});
</script>
<template>
  <div class="relative" v-loading="loading">
    <IconFont
      v-if="docStatus && docStatus in docStatusDict"
      :name="docStatusDict[docStatus].name"
      :size="150"
      class="absolute right-20 top-14 z-40"
      :class="docStatusDict[docStatus].color"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>调整单信息</span>
      </template>
      <Form>
        <template #documentProcess>
          <ElScrollbar>
            <StepProgress
              :doc-number="
                invcAdjustDocNumber || data?.invcAdjustDocNumber || ''
              "
              class="min-w-[750px] pt-[10px]"
            />
          </ElScrollbar>
        </template>
        <template #remark="row">
          <div class="mb-2 flex w-full flex-wrap gap-2">
            <ElTag
              type="primary"
              size="small"
              v-for="item in remarkOptionList"
              :key="item.optionId"
            >
              {{ item.optionName }}
            </ElTag>
          </div>
          <span class="text-sm">{{ row.value }}</span>
        </template>
      </Form>
    </FormCard>
  </div>
</template>
