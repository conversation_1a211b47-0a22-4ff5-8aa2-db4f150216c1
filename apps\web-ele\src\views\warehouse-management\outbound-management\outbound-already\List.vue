<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElTooltip } from 'element-plus';

import {
  exportStockedOutboundDoc,
  getAlreadyOutBoundDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';
import { WS } from '#/utils/socket/common-socketio';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      applyEndTime?: string;
      applyStartTime?: string;
      applyUserList?: string;
      collectorUserList?: string;
      docStatusList?: string;
      executorEndTime?: string;
      executorStartTime?: string;
      isProxyExec?: unknown;
      materialUserList?: string;
      origDocNumberList?: string;
      origDocTypeCodeList?: string;
      outBoundDocNumberList?: string;
      preparationStateList?: string;
    }>,
    default: () => ({}),
  },
});

const wsType = [
  'wm.inbound.docstatus.pending.add',
  'wm.inbound.docstatus.finished',
  'wm.inbound.docstatus.cancelAudit',
  'wm.inbound.docstatus.pending.cancelReject',
  'wm.inbound.docstatus.close',
  'wm.inbound.export.pending',
  'wm.inbound.export.finished',
];
const modalFormRef = ref<InstanceType<typeof Form>>();
const outBoundDocId = ref<string>('');
const outBoundDocNumber = ref<string>('');

/** 提交时间 */
const applyTime = ref({
  // 开始时间
  startTime: props.params?.applyStartTime || '',
  // 结束时间
  endTime: props.params?.applyEndTime || '',
});

/** 出库时间 */
const executorTime = ref({
  // 开始时间
  startTime: props.params?.executorStartTime || '',
  // 结束时间
  endTime: props.params?.executorEndTime || '',
});

/** 出库弹窗 */
const [FormModal, formModalApi] = useVbenModal({
  confirmText: '已出库详情',
  destroyOnClose: true,
  onBeforeClose: () => {
    outBoundDocId.value = '';
    outBoundDocNumber.value = '';
    return true;
  },
  closeOnClickModal: false,
  footer: false,
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      applyTime.value = {
        startTime: '',
        endTime: '',
      };
      executorTime.value = {
        startTime: '',
        endTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    schema: useGridFormSchema(),
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    cellConfig: {
      height: 55,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.applyStartTime = applyTime.value.startTime;
          params.applyEndTime = applyTime.value.endTime;
          params.executorStartTime = executorTime.value.startTime;
          params.executorEndTime = executorTime.value.endTime;

          return await getAlreadyOutBoundDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    applyUserList: props.params?.applyUserList?.split(',') || [],
    materialUserList: props.params?.materialUserList?.split(',') || [],
    origDocNumberList: props.params?.origDocNumberList?.split(',') || [],
    origDocTypeCodeList: props.params?.origDocTypeCodeList?.split(',') || [],
    outBoundDocNumberList:
      props.params?.outBoundDocNumberList?.split(',') || [],
    preparationStateList: props.params?.preparationStateList?.split(',') || [],
    isProxyExec: isEmpty(props.params?.isProxyExec)
      ? ''
      : props.params?.isProxyExec,
    docStatusList: props.params?.docStatusList?.split(',') || [],
    collectorUserList: props.params?.collectorUserList?.split(',') || [],
  });
  WS.on(wsType, gridApi.query);
});
function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'view': {
      openOutboundModal(e.row);
      break;
    }
  }
}

function openOutboundModal(row: RowType) {
  outBoundDocId.value = row.outBoundDocId;
  outBoundDocNumber.value = row.outBoundDocNumber;
  formModalApi.open();
}

/** 数据导出 */
async function exportStockedOutboundDocHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.applyStartTime = applyTime.value.startTime;
    formValues.applyEndTime = applyTime.value.endTime;
    formValues.executorStartTime = executorTime.value.startTime;
    formValues.executorEndTime = executorTime.value.endTime;
    const response = await exportStockedOutboundDoc(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

function onOutboundLoading(loading: boolean) {
  formModalApi.setState({ loading });
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="h-full w-10/12">
      <Form
        ref="modalFormRef"
        :out-bound-doc-id="outBoundDocId"
        :out-bound-doc-number="outBoundDocNumber"
        @bound-loading="onOutboundLoading"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>
    <Grid>
      <template #form-applyTime>
        <ElDatePicker
          v-model="applyTime.startTime"
          :disabled-date="
            (time: Date) =>
              isAfter(time, applyTime.endTime || new Date('2099-12-31'))
          "
          type="datetime"
          placeholder="开始日期"
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="applyTime.endTime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, applyTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-executorTime>
        <ElDatePicker
          v-model="executorTime.startTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, executorTime.endTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="executorTime.endTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(time, executorTime.startTime || new Date('1900-01-01'))
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportStockedOutboundDocHandle"
            v-access:code="'wm:outbound:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>
    </Grid>
  </Page>
</template>
