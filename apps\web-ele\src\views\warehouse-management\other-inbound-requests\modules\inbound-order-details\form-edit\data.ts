import type { OnActionClickParams, VxeGridProps } from '@girant/adapter';

import { h } from 'vue';

import { ElButton, ElPopconfirm } from 'element-plus';

import MaterialSelect from '#/components/material-select/Index.vue';

/** 物料信息 */
export const useGridOptions = (
  onActionClick: (e: OnActionClickParams) => void,
): VxeGridProps => {
  return {
    columns: [
      { title: '序号', type: 'seq', width: 50 },
      {
        editRender: {},
        field: 'materialId',
        slots: {
          default: ({ row }) => {
            return row.materialName;
          },
          edit: ({ $table, row }) => {
            return h(MaterialSelect, {
              modelValue: row.materialId,
              onChange: async (materialId, selectedItems) => {
                if (!materialId) {
                  $table.setRow(row, {
                    materialId: undefined,
                    materialSpecs: undefined,
                    baseUnitLabel: undefined,
                    materialCode: undefined,
                  });
                  return;
                }
                const materialDetailRes = selectedItems;
                const rowData = {
                  ...materialDetailRes,
                  materialId: {
                    materialId,
                    materialName: materialDetailRes?.materialName,
                  },
                };
                $table.setRow(row, rowData);
              },
              valueKey: 'materialId',
            });
          },
        },
        title: '物料选择',
        width: 200,
      },
      {
        field: 'materialCode',
        title: '物料编号',
        minWidth: 140,
      },
      {
        field: 'materialSpecs',
        title: '规格型号',
        minWidth: 180,
        showOverflow: false,
      },
      {
        field: 'baseUnitLabel',
        title: '基本单位',
        width: 110,
      },
      {
        slots: {
          default: 'applyQuantity',
        },
        field: 'applyQuantity',
        title: '申请入库数量',
        width: 180,
      },
      {
        align: 'center',
        slots: {
          default: (scope) => {
            if (scope.$table.getTableData().fullData.length === 1) return [];
            return h(
              ElPopconfirm,
              {
                placement: 'top',
                title: '确定删除该行？',
                onConfirm: () =>
                  onActionClick({
                    code: 'delete',
                    row: scope.row,
                  }),
              },
              {
                reference: () => {
                  return h(
                    ElButton,
                    {
                      link: true,
                      size: 'small',
                      type: 'danger',
                    },
                    {
                      default: () => '删除',
                    },
                  );
                },
              },
            );
          },
        },
        title: '操作',
        width: 60,
        fixed: 'right',
      },
    ],
    editRules: {
      materialId: [
        { message: '物料不能为空', required: true, trigger: 'blur' },
      ],
      applyQuantity: [
        { message: '数量不能为空', required: true, trigger: 'blur' },
        {
          validator: ({ cellValue }) => {
            if (cellValue === 0) {
              return new Error('数量不能为0');
            }
          },
          trigger: 'blur',
        },
      ],
    },
    minHeight: 170,
    maxHeight: 600,
    cellConfig: {
      height: 40,
    },
    showOverflow: false,
  };
};
