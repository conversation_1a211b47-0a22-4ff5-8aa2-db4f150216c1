<script setup lang="ts">
import type { PropType } from 'vue';

import type { InventoryQueryApi } from '#/api/warehouse-management';

import { computed, nextTick, onMounted, ref } from 'vue';

import { ElAlert, ElButton } from 'element-plus';

const props = defineProps({
  errorData: {
    type: Object as PropType<InventoryQueryApi.ImportInventoryRes['data']>,
    default: () => ({}),
  },
});

/** 展开状态控制 */
const expandedStates = ref<Record<number, boolean>>({});

/** 内容溢出状态控制 */
const overflowStates = ref<Record<number, boolean>>({});

/** 切换展开状态 */
const toggleExpanded = (index: number) => {
  expandedStates.value[index] = !expandedStates.value[index];
};

/** 检查内容是否超过容器高度 */
const checkContentOverflow = (index: number) => {
  nextTick(() => {
    const contentElement = document.querySelector(
      `[data-content-index="${index}"]`,
    ) as HTMLElement;
    if (!contentElement) return;

    // 获取当前受限制状态下的高度
    const restrictedHeight = contentElement.clientHeight;

    // 临时移除 line-clamp-2 限制来获取完整内容高度
    const originalClass = contentElement.className;
    contentElement.className = contentElement.className.replace(
      'line-clamp-2',
      '',
    );

    // 强制重新计算布局
    void contentElement.offsetHeight;

    const fullHeight = contentElement.scrollHeight;

    // 恢复原始类名
    contentElement.className = originalClass;

    // 如果完整高度明显大于受限制高度，则认为有溢出
    // 使用相对差值判断，更加可靠
    const heightDifference = fullHeight - restrictedHeight;
    overflowStates.value[index] = heightDifference > 5; // 超过5px就认为有溢出
  });
};

/** 组件挂载后检查所有内容的溢出状态 */
onMounted(() => {
  nextTick(() => {
    importExceptionContent.value.forEach((_, index) => {
      checkContentOverflow(index);
    });
  });
});

/** 处理错误数据，转换为统一的展示格式 */
const importExceptionContent = computed(() => {
  const exceptions: {
    materialList: string[];
    title: string;
  }[] = [];

  if (!props.errorData) return exceptions;

  // 不可用或不存在的库位编号列表
  if (props.errorData.disableLocationCodeList?.length > 0) {
    exceptions.push({
      materialList: props.errorData.disableLocationCodeList,
      title: `有${props.errorData.disableLocationCodeList.length}个不可用或不存在的库位编号`,
    });
  }

  // 不可用或不存在的物料编号列表
  if (props.errorData.disableMaterialCodeList?.length > 0) {
    exceptions.push({
      materialList: props.errorData.disableMaterialCodeList,
      title: `有${props.errorData.disableMaterialCodeList.length}个不可用或不存在的物料编号`,
    });
  }

  // 库存错误对应行列表
  if (props.errorData.invcErrorRowList?.length > 0) {
    const errorRowsWithPrefix = props.errorData.invcErrorRowList.map(
      (row) => `第${row}行`,
    );
    exceptions.push({
      materialList: errorRowsWithPrefix,
      title: `有${props.errorData.invcErrorRowList.length}行库存数据存在错误`,
    });
  }

  // 库存明细重复行列表
  if (props.errorData.repeatInvcRowList?.length > 0) {
    const repeatRowsWithPrefix = props.errorData.repeatInvcRowList.map(
      (row) => `第${row}行`,
    );
    exceptions.push({
      materialList: repeatRowsWithPrefix,
      title: `有${props.errorData.repeatInvcRowList.length}条库存明细与现有数据重复`,
    });
  }

  // 重复行列表
  if (props.errorData.repeatRowList?.length > 0) {
    const duplicateRowsWithPrefix = props.errorData.repeatRowList.map(
      (row) => `第${row}行`,
    );
    exceptions.push({
      materialList: duplicateRowsWithPrefix,
      title: `导入文件中有${props.errorData.repeatRowList.length}行数据重复`,
    });
  }

  // 单价错误对应行列表
  if (props.errorData.unitPriceErrorRowList?.length > 0) {
    const priceErrorRowsWithPrefix = props.errorData.unitPriceErrorRowList.map(
      (row) => `第${row}行`,
    );
    exceptions.push({
      materialList: priceErrorRowsWithPrefix,
      title: `有${props.errorData.unitPriceErrorRowList.length}行单价数据格式错误`,
    });
  }

  return exceptions;
});
</script>

<template>
  <ElAlert
    type="error"
    show-icon
    :closable="false"
    title="导入数据存在以下问题："
  >
    <div>
      <div
        v-for="(item, index) in importExceptionContent"
        :key="item.title"
        class="mb-2"
      >
        <!-- 问题标题 -->
        <div class="mb-2 font-medium text-red-600">
          <span class="mr-2">{{ index + 1 }}.{{ item.title }}</span>
        </div>
        <!-- 问题内容 -->
        <div class="rounded bg-gray-50 p-2 text-stone-600">
          <div
            :class="[expandedStates[index] ? '' : 'line-clamp-2']"
            class="overflow-hidden"
            :data-content-index="index"
          >
            <span
              v-for="(material, materialIndex) in item.materialList"
              :key="material"
            >
              {{ material }}
              {{ materialIndex !== item.materialList.length - 1 ? '、' : '' }}
            </span>
          </div>
          <!-- 展开/收起按钮 -->
          <div v-if="overflowStates[index]" class="mt-1">
            <ElButton
              type="primary"
              link
              class="text-sm text-blue-500 transition-colors duration-200 hover:text-blue-700"
              @click="toggleExpanded(index)"
            >
              {{ expandedStates[index] ? '收起' : '展开' }}
            </ElButton>
          </div>
        </div>
      </div>
    </div>
  </ElAlert>
</template>

<style scoped>
@supports not (-webkit-line-clamp: 2) {
  .line-clamp-2 {
    position: relative;
    max-height: 3em;
    overflow: hidden;
  }

  .line-clamp-2::after {
    position: absolute;
    right: 0;
    bottom: 0;
    padding-left: 4px;
    content: '...';
    background: #f9fafb;
  }
}

.line-clamp-2 {
  display: -webkit-box;
  max-height: 3em;
  overflow: hidden;
  -webkit-line-clamp: 2;

  /* line-height: 1.5; */
  word-break: break-all;
  -webkit-box-orient: vertical;
}
</style>
