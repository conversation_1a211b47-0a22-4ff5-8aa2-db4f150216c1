import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElBadge, ElInputTag } from 'element-plus';

import {
  getDictItemList,
  getDocStatusInfo,
  getEnumByName,
  getMaterialCategoryTree,
  getOriginalDocConfigList,
} from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN', 'IN_AND_OUT']);

// 查询参数类型
export interface SearchParams {
  applyTime: string[];
  executorTime: string[];
  inBoundDocNumberList: string[];
  origDocNumberList: string[];
  materialCodeList: string[];
  materialName: string;
  applyUserList: string[];
  executorUserList: string[];
  materialAttributeList: string[];
  materialTypeList: string[];
  materialCategoryList: string[];
  warehouseName: string;
  locationName: string;
  batchNumber: string;
  isStandard: boolean;
  origDocTypeCodeList: string[];
  docStatusList: string[];
}

// 表格数据类型
export interface RowType {
  applyTime: string;
  applyUser: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  applyUserName: string;
  inBoundDocId: string;
  inBoundDocNumber: string;
  isRectify: boolean;
  origDocId: string;
  origDocNumber: string;
  origDocTypeCode: string;
  origDocTypeName: string;
  pictureFileId: string;
  materialCode: string;
  materialName: string;
  materialAttributeLabel: string;
  applyQuantitySum: number;
  baseUnitLabel: string;
  actualQuantitySum: number;
  executorUserName: string;
  docStatusLabel: string;
  materialSpecs: string;
  materialCategoryName: string;
  docStatus: string;
  materialTypeLabel: string;
  materialCategoryLabel: string;
  executorUser: string;
  executorTime: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'inBoundDocNumberList',
      label: '入库单号',
    },
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },

    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入物料名称',
      },
      fieldName: 'materialName',
      label: '物料名称',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'materialCodeList',
      label: '物料编号',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialAttribute');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialAttributeList',
      label: '物料属性',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const res = data.map((item: any) => ({
            label: item.dictLabel,
            value: item.dictValue,
          }));

          return res;
        },
        api: () => {
          return getDictItemList('baseMaterialType');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'materialTypeList',
      label: '物料大类',
    },

    {
      component: 'ApiTreeSelect',
      componentProps: {
        afterFetch: (data: any) => {
          // 转换函数
          function convertDeptData(data: any) {
            return {
              label: data.categoryName,
              value: data.categoryCode,
              children: data.children
                ? data.children.map((child: any) => convertDeptData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((data: any) => convertDeptData(data));
          return convertedData;
        },
        api: () => {
          return getMaterialCategoryTree();
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        filterable: true,
        maxCollapseTags: 1,
        multiple: true,
        showCheckbox: true,
      },
      defaultValue: [],
      fieldName: 'materialCategoryList',
      label: '物料细类',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入仓库名称',
      },
      fieldName: 'warehouseName',
      label: '仓库名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入库位名称',
      },
      fieldName: 'locationName',
      label: '库位名称',
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
        placeholder: '请输入批次号',
      },
      fieldName: 'batchNumber',
      label: '批次号',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isStandard',
      label: '是否标准物料',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      label: '申请人',
      modelPropName: 'value',
      formItemClass: 'col-span-1',
    },

    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'executorUserList',
      label: '入库人',
      modelPropName: 'value',
      formItemClass: 'col-span-1',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '入库类型',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const workStatusTypeList = data.map((item: any) => ({
            label: item.docStatusLabel,
            value: item.docStatusKey,
          }));
          workStatusTypeList.push({ label: '全部', value: '' });
          return workStatusTypeList;
        },
        api: () => {
          return getDocStatusInfo('WM0010');
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'docStatusList',
      formItemClass: 'hidden',
      label: '入库单状态',
    },
    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'executorTime',
      formItemClass: 'col-span-2',
      label: '入库时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },

    {
      field: 'materialCode',
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '非标',
              type: 'danger',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: row.isStandard,
              class: 'item',
            },
            {
              default: () => row.materialCode,
            },
          );
        },
      },
      title: '物料编号',
      minWidth: 220,
    },

    {
      field: 'executorTime',
      title: '入库时间',
      width: 150,
    },

    {
      field: 'docStatusLabel',
      title: '入库单状态',
      visible: false,
    },

    {
      field: 'docStatus',
      title: '入库单状态值',
      visible: false,
    },

    {
      field: 'materialName',
      title: '物料名称',
      width: 150,
    },

    {
      slots: {
        default: ({ row }) =>
          h(ImageViewer, {
            imgId: row.pictureFileId,
            imgCss: 'h-[45px]',
          }),
      },
      field: 'pictureFileId',
      title: '物料图片',
      width: 150,
    },

    {
      field: 'materialSpecs',
      title: '具体规格',
      minWidth: 230,
    },

    {
      field: 'applyQuantitySum',
      title: '入库数量',
      width: 100,
    },

    {
      field: 'actualItemList',
      title: '入库明细',
      align: 'left',
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {},
            row?.actualItemList?.length > 0
              ? row.actualItemList.map((item: any) => {
                  return h(
                    'div',
                    {
                      class:
                        'flex items-center text-black font-bold mb-1 rounded-md overflow-hidden',
                    },
                    [
                      h(
                        'span',
                        { class: 'bg-[#E3ECFD] px-2' },
                        item.warehouseName,
                      ),
                      h(
                        'span',
                        { class: 'bg-[#DBFFCA] px-2' },
                        item.locationName,
                      ),
                      h(
                        'span',
                        { class: 'bg-[#FFF9C8] px-2' },
                        item.batchNumber,
                      ),
                      h(
                        'span',
                        { class: 'bg-[#FCDABD] px-2' },
                        `×${item.actualQuantity}`,
                      ),
                    ],
                  );
                })
              : '',
          );
        },
      },
      width: 'auto',
      headerAlign: 'center',
    },

    {
      field: 'baseUnitLabel',
      title: '单位',
      width: 100,
    },

    {
      field: 'materialAttributeLabel',
      title: '物料属性',
      width: 100,
      visible: false,
    },

    {
      field: 'materialTypeLabel',
      title: '物料大类',
      width: 100,
      visible: false,
    },
    {
      field: 'materialCategoryName',
      title: '物料细类',
      width: 100,
      visible: false,
    },

    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '补录',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: !row.isRectify,
              class: 'item',
            },
            {
              default: () => row.inBoundDocNumber,
            },
          );
        },
      },
      field: 'inBoundDocNumber',
      title: '入库单号',
      minWidth: 250,
    },

    {
      field: 'origDocTypeName',
      minWidth: 150,
      title: '入库类型',
    },
    {
      field: 'origDocNumber',
      minWidth: 180,
      title: '申请单号',
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 150,
    },

    {
      field: 'executorUserName',
      title: '入库人',
      width: 'auto',
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'inBoundDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ];
}
