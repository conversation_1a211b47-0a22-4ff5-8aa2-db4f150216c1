import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { h, markRaw } from 'vue';

import { useAccess } from '@vben/access';

import { ElBadge, ElButton, ElInputTag, ElTag } from 'element-plus';

import { getEnumByName, getOriginalDocConfigList } from '#/api';
import DeptStaffTree from '#/components/dept-staff-tree/Index.vue';

const WMORIGINAL_DOCTYPE_ENUMKEY_LIST = new Set(['IN_AND_OUT', 'OUT']);

const { hasAccessByCodes } = useAccess();

// 查询参数类型
export interface SearchParams {
  outBoundDocNumberList: string[];
  applyUserList: string[];
  origDocTypeCodeList: string[];
  origDocNumberList: string[];
  isRectify: boolean;
  applyTime: string[];
  materialUserList: string[];
  materialUserDeptList: string[];
  preparationStateList: string[];
}

// 表格数据类型
export interface RowType {
  outBoundDocNumber: string;
  outBoundDocId: string;
  origDocTypeName: string;
  origDocNumber: string;
  origDocId: string;
  applyUserName: string;
  applyUser: string;
  applyUserDeptId: string;
  applyUserDeptName: string;
  applyTime: string;
  isRectify: boolean;
  materialUserName: string;
  preparationStateLabel: string;
  preparationState: string;
  origDocTypeCode: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'outBoundDocNumberList',
      label: '出库单号',
    },

    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'origDocNumberList',
      label: '申请单号',
    },
    {
      component: 'ApiSelect',
      componentProps: {
        api: async () => {
          const EnumRes = await getEnumByName('WmDocTypeEnums');

          const list = EnumRes.filter((item: any) => {
            return WMORIGINAL_DOCTYPE_ENUMKEY_LIST.has(item.enumKey);
          }).map((item: any) => item.enumValue);

          const OriginalDocConfigRes = await getOriginalDocConfigList({
            originalDocTypeList: list.join(','),
          });

          return OriginalDocConfigRes.map((item: any) => ({
            label: item.docName,
            value: item.docCode,
          }));
        },
        clearable: true,
        collapseTags: true,
        collapseTagsTooltip: true,
        multiple: true,
      },
      defaultValue: [],
      fieldName: 'origDocTypeCodeList',
      label: '出库类型',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'applyUserList',
      modelPropName: 'value',
      label: '申请人',
      formItemClass: 'col-span-1',
    },
    {
      component: h(DeptStaffTree, {
        clearable: true,
        multiple: true,
        maxCollapseTags: 1,
        showAllLevels: false,
      }),
      fieldName: 'materialUserList',
      modelPropName: 'value',
      label: '使用人',
      formItemClass: 'col-span-1',
    },

    {
      component: 'ApiSelect',
      componentProps: {
        afterFetch: (data: any) => {
          const preparationStateList = data.map((item: any) => ({
            label: item.enumLabel,
            value: item.enumValue,
          }));
          return preparationStateList;
        },
        api: () => {
          return getEnumByName('WmPreparationStateEnums');
        },
        clearable: true,
        multiple: true,
      },
      defaultValue: '',
      fieldName: 'preparationStateList',
      label: '备料状态',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        options: [
          { label: '全部', value: '' },
          { label: '是', value: true },
          { label: '否', value: false },
        ],
      },
      defaultValue: '',
      fieldName: 'isRectify',
      label: '是否补录',
      formItemClass: 'hidden',
    },

    {
      component: 'Input',
      fieldName: 'applyTime',
      formItemClass: 'col-span-2',
      label: '提交时间',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  const operationColumn = {
    align: 'center',
    cellRender: {
      attrs: {
        nameField: 'outBoundDocNumber',
        nameTitle: '操作',
        onClick: onActionClick,
      },
      name: 'CellOperation',

      options: [
        ...(hasAccessByCodes(['wm:outbound:exec'])
          ? [
              {
                code: 'outbound',
                label: '确认出库',
                type: 'primary',
              },
            ]
          : []),
        ...(hasAccessByCodes(['wm:inoutbound:cancel:submit'])
          ? [
              {
                code: 'close',
                label: '取消出库',
                type: 'danger',
              },
            ]
          : []),
      ],
    },
    field: 'operation',
    fixed: 'right',
    title: '操作',
    width: 'auto',
  } as NonNullable<VxeTableGridOptions['columns']>[0];

  return [
    {
      title: '序号',
      cellRender: {
        name: 'CellSequence',
      },
      width: 40,
    },
    {
      slots: {
        default: ({ row }) => {
          return h(
            ElBadge,
            {
              value: '补录',
              type: 'primary',
              offset: [-15, -5],
              badgeStyle: 'border-radius:0px',
              hidden: !row.isRectify,
              class: 'item',
            },
            {
              default: () => row.outBoundDocNumber,
            },
          );
        },
      },
      field: 'outBoundDocNumber',
      title: '出库单号',
      minWidth: 250,
    },
    {
      field: 'outBoundDocId',
      title: '出库单ID',
      visible: false,
    },
    {
      field: 'origDocTypeName',
      width: 180,
      title: '出库类型',
    },
    {
      field: 'origDocNumber',
      title: '申请单号',
      minWidth: 180,
    },

    {
      field: 'origDocId',
      title: '申请单ID',
      visible: false,
    },

    {
      field: 'applyUserName',
      title: '申请人',
      width: 'auto',
    },

    {
      field: 'applyUser',
      title: '申请人ID',
      visible: false,
    },
    {
      field: 'applyUserDeptId',
      title: '申请人部门ID',
      visible: false,
    },

    {
      field: 'applyUserDeptName',
      title: '申请人部门',
      visible: false,
    },

    {
      field: 'applyTime',
      title: '提交时间',
      width: 200,
    },

    {
      field: 'materialUserName',
      title: '使用人',
      width: 'auto',
    },

    {
      field: 'isRectify',
      title: '是否补录',
      visible: false,
    },

    {
      field: 'preparationStateLabel',
      title: '备料状态',
      slots: {
        default: ({ row }) => {
          return h(
            'div',
            {
              class: 'relative',
            },
            [
              h(
                ElTag,
                {
                  type: row.preparationState === '00' ? 'info' : 'success',
                },
                { default: () => row.preparationStateLabel },
              ),
              ...(row.preparationState === '00' &&
              hasAccessByCodes(['wm:outbound:prep:submit'])
                ? [
                    h(
                      ElButton,
                      {
                        type: 'primary',
                        size: 'small',
                        link: true,
                        onClick: () => {
                          onActionClick({
                            code: 'prep',
                            row,
                          });
                        },
                      },
                      {
                        default: () => '备料',
                      },
                    ),
                  ]
                : []),
              ...(row.preparationState === '10' &&
              hasAccessByCodes(['wm:outbound:prep:rollback'])
                ? [
                    h(
                      ElButton,
                      {
                        type: 'primary',
                        size: 'small',
                        link: true,
                        onClick: () => {
                          onActionClick({
                            code: 'return',
                            row,
                          });
                        },
                      },
                      {
                        default: () => '返仓',
                      },
                    ),
                  ]
                : []),
            ],
          );
        },
      },
      width: 150,
    },

    {
      field: 'preparationState',
      title: '备料状态',
      visible: false,
    },

    ...(hasAccessByCodes(['wm:outbound:exec', 'wm:inoutbound:cancel:submit'])
      ? [operationColumn]
      : []),
  ];
}
