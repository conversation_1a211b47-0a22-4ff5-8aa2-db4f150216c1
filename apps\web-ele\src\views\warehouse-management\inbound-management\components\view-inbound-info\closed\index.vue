<script setup lang="ts">
import type {
  InBoundDocApi,
  InOutCancelDocApi,
} from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElTag } from 'element-plus';

import {
  getInBoundDocDetail,
  getInBoundDocDetailByInCancelDoc,
  getInOutCancelDocListByInOutDoc,
} from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  inBoundDocId: {
    default: '',
    type: String,
  },
  inBoundDocNumber: {
    default: '',
    type: String,
  },
  cancelDocId: {
    default: '',
    type: String,
  },
  isShowInOutCancelDocNumber: {
    default: true,
    type: Boolean,
  },
});

const emits = defineEmits(['viewInOutCancelDoc', 'viewOrigDoc']);

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

const inOutCancelDocDetail = ref<InOutCancelDocApi.InOutCancelDocDetail>(
  {} as InOutCancelDocApi.InOutCancelDocDetail,
);

const loading = ref(false);

const [Form, formApi] = useVbenForm({
  schema: useFormSchema(props.isShowInOutCancelDocNumber),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-9',
});

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    loading.value = true;
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: false,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  } finally {
    loading.value = false;
  }
};

const getInBoundDocDetailByInCancelDocHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetailByInCancelDoc({
      inCancelDocId: props.cancelDocId,
      isQueryItem: false,
    });
    inBoundData.value = inBoundRes;
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
    return {} as InBoundDocApi.InBoundDocDetail;
  }
};

onMounted(async () => {
  const isCancelDoc = !isEmpty(props.cancelDocId); // 是否为取消单据id

  switch (isCancelDoc) {
    case false: {
      if (props.inBoundDocId || props.inBoundDocNumber) {
        await getInBoundDocDetailHandle();
        formApi.setValues(inBoundData.value);
      } else {
        ElMessage.error('缺少入库单据id');
      }
      break;
    }
    case true: {
      if (props.cancelDocId) {
        await getInBoundDocDetailByInCancelDocHandle();
        formApi.setValues(inBoundData.value);
      } else {
        ElMessage.error('缺少取消单据id');
      }
      break;
    }
    default: {
      ElMessage.error('缺少入库单据id或取消单据id');
    }
  }

  if (
    props.isShowInOutCancelDocNumber &&
    (inBoundData.value.docStatus === 'closed' ||
      inBoundData.value.docStatus === 'cancelAudit')
  ) {
    const inOutCancelDocDetailRes = await getInOutCancelDocListByInOutDoc({
      inOutBoundDocId: inBoundData.value.inBoundDocId,
      inOutBoundDocNumber: inBoundData.value.inBoundDocNumber,
    });

    inOutCancelDocDetail.value =
      inOutCancelDocDetailRes[0] ||
      ({} as InOutCancelDocApi.InOutCancelDocDetail);
  }
});

const handleViewInOutCancelDoc = () => {
  emits('viewInOutCancelDoc', {
    inOutCancelDocId: inOutCancelDocDetail.value.inOutCancelDocId,
    inOutCancelDocNumber: inOutCancelDocDetail.value.inOutCancelDocNumber,
  });
};

const handleViewOrigDoc = () => {
  emits('viewOrigDoc', {
    origDocId: inBoundData.value.origDocId,
    origDocNumber: inBoundData.value.origDocNumber,
  });
};
</script>

<template>
  <IconFont
    name="yiguanbi"
    :size="150"
    class="absolute right-20 top-14 !text-gray-300"
    v-if="inBoundData.docStatus === 'closed'"
  />
  <FormCard :is-footer="false" title="入库信息">
    <template #default>
      <Form v-loading="loading">
        <template #inBoundDocNumber="{ modelValue }">
          <div>
            {{ modelValue }}
            <ElTag
              :type="inBoundData.docStatus === 'closed' ? 'info' : 'warning'"
            >
              {{ inBoundData.docStatusLabel }}
            </ElTag>
            <ElTag v-if="inBoundData.isRectify" type="primary" class="ml-2">
              补录
            </ElTag>
          </div>
        </template>

        <template #origDocNumber>
          <div>
            <ElButton
              type="primary"
              link
              @click="handleViewOrigDoc"
              class="underline"
            >
              {{
                `${inBoundData.origDocTypeName || ''} ${inBoundData.origDocNumber || ''}`
              }}
            </ElButton>
          </div>
        </template>

        <template #docStatusLabel="{ modelValue }">
          <div>
            <ElTag
              :type="inBoundData.docStatus === 'closed' ? 'info' : 'warning'"
            >
              {{ modelValue }}
            </ElTag>
          </div>
        </template>

        <template #applyUserName="{ modelValue }">
          <div>
            {{ modelValue }}
            <span v-if="inBoundData.applyUserDeptName">
              ({{ inBoundData.applyUserDeptName }})
            </span>
          </div>
        </template>
        <template #closeUserName="{ modelValue }">
          <div>
            {{ modelValue }}
            <span v-if="inBoundData.closeUserDeptName">
              ({{ inBoundData.closeUserDeptName }})
            </span>
          </div>
        </template>
        <template #inOutCancelDocNumber>
          <div v-loading="!inOutCancelDocDetail.inOutCancelDocNumber">
            <ElButton
              type="primary"
              link
              @click="handleViewInOutCancelDoc"
              class="underline"
            >
              {{ inOutCancelDocDetail.inOutCancelDocNumber }}
            </ElButton>
          </div>
        </template>

        <template #docProcess>
          <StepProgress
            v-if="inBoundData?.inBoundDocNumber"
            :doc-number="inBoundData?.inBoundDocNumber"
            class="min-h-[65px] overflow-x-auto"
          />
          <div v-else>/</div>
        </template>
      </Form>
    </template>
  </FormCard>
</template>
