<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItemData } from '../../types';

defineProps({
  warehouseItemData: {
    type: Object as PropType<MaterialItemData.WarehouseGroup>,
    default: () => ({}),
  },
});
</script>

<template>
  <div class="bg-primary-50/80 mb-3 flex rounded-lg p-3 shadow-sm">
    <div
      class="cangku border-primary-200 relative flex w-1/5 flex-col justify-center border-r-2 border-dashed pl-4"
    >
      <span class="text-gray-500">
        仓库：<span class="font-medium text-gray-700">{{
          warehouseItemData.warehouseName
        }}</span>
      </span>
    </div>
    <div class="flex-1 pl-10">
      <div class="flex flex-col gap-2.5">
        <template
          v-for="item in warehouseItemData.locationList"
          :key="item.locationId"
        >
          <div class="flex items-center">
            <div class="flex-1 text-gray-500">
              库位批次：
              <span class="font-medium text-gray-700">
                {{ item.locationName }}
              </span>
              <span class="font-medium text-gray-700">
                ({{ item.batchNumber || '/' }})
              </span>
            </div>
            <div class="w-1/5 text-gray-500">
              数量：<span class="font-medium text-gray-700">{{
                item.quantity || 0
              }}</span>
            </div>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
