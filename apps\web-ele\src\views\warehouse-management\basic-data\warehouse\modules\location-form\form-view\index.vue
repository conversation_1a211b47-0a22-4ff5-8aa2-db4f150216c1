<script setup lang="ts">
import type { LocationInfoApi } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElMessageBox, ElTag } from 'element-plus';

import {
  getLocationDetail,
  lockLocation,
  unlockLocation,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  locationId: {
    type: String,
    default: '',
  },
  locationCode: {
    type: String,
    default: '',
  },
  viewBtn: {
    type: Boolean,
    default: true,
  },
});
const emits = defineEmits(['edit', 'formSubmitSuccess']);
const loading = ref(false);
/** 库位详细信息 */
const locationDetail = ref<LocationInfoApi.LocationDetail>();
const isLock = ref(false);
/** 基础资料表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: {
    componentProps: { class: 'w-full' },
    formItemClass: 'items-baseline',
  },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    // 获取库位详细信息
    const resWarehouse = await getLocationDetail(
      props.locationId,
      props.locationCode,
    );
    isLock.value = resWarehouse?.isLock;
    locationDetail.value = resWarehouse;
    // 填充表单数据
    formApi.setValues(resWarehouse);
  } catch {
    ElMessage.error('数据获取失败');
  } finally {
    loading.value = false;
  }
};

/** 锁定 */
const lock = async () => {
  try {
    await ElMessageBox.confirm('确定锁定吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await lockLocation(props.locationId);
    ElMessage.success('锁定成功');
    getData();
    emits('formSubmitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('锁定失败');
  } finally {
    loading.value = false;
  }
};
/** 解锁 */
const unlock = async () => {
  try {
    await ElMessageBox.confirm('确定解锁吗？', '提示', {
      type: 'warning',
    });
    loading.value = true;
    await unlockLocation(props.locationId);
    ElMessage.success('解锁成功');
    getData();
    emits('formSubmitSuccess');
  } catch (error) {
    console.error(error);
    // ElMessage.error('解锁失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.locationId || props.locationCode) {
    getData();
  }
});
defineExpose({
  Form,
  formApi,
});
</script>

<template>
  <div class="h-full w-full">
    <ElScrollbar noresize>
      <div v-loading="loading">
        <IconFont
          v-if="isLock"
          name="yisuoding"
          :size="150"
          color="dark:bg-gray-800"
          class="text-primary-500 absolute right-20 top-24 z-40"
        />
        <FormCard :is-footer="false">
          <template #title>
            <span>基础资料</span>
          </template>
          <template #default>
            <Form>
              <template #locationName="row">
                {{ row.value }}
                <div>
                  <ElTag
                    class="ml-[10px]"
                    :type="locationDetail?.isEnable ? 'success' : 'danger'"
                  >
                    {{ locationDetail?.isEnable ? '启用' : '停用' }}
                  </ElTag>
                  <ElTag class="ml-[10px]">
                    {{ locationDetail?.isTemp ? '临时仓' : '非临时仓' }}
                  </ElTag>
                  <ElTag class="ml-[10px]">
                    {{ locationDetail?.isPrepMaterial ? '可备料' : '不可备料' }}
                  </ElTag>
                </div>
              </template>
            </Form>
          </template>
        </FormCard>
      </div>
    </ElScrollbar>
    <div class="flex min-h-[40px] justify-end" v-if="props.viewBtn">
      <ElButton
        type="primary"
        @click="emits('edit', locationDetail?.locationId)"
        v-access:code="'wm:location:edit:mod'"
      >
        编辑
      </ElButton>
      <ElButton
        type="primary"
        @click="lock"
        v-if="!isLock"
        v-access:code="'wm:location:lock:lock'"
      >
        锁库位
      </ElButton>
      <ElButton
        type="primary"
        @click="unlock"
        v-if="isLock"
        v-access:code="'wm:location:lock:unlock'"
      >
        解锁
      </ElButton>
    </div>
  </div>
</template>
