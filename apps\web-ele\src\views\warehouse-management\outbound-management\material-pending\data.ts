import type {
  OnActionClickFn,
  VbenFormSchema,
  VxeTableGridOptions,
} from '@girant/adapter';

import { markRaw } from 'vue';

import { ElInputTag } from 'element-plus';

// 查询参数类型
export interface SearchParams {
  outBoundDocNumberList: string[];
  modifyTime: string[];
}

// 表格数据类型
export interface RowType {
  prepDocId: string;
  outBoundDocId: string;
  outBoundDocNumber: string;
  origDocNumber: string;
  modifyTime: string;
}

/** 查询 */
export function useGridFormSchema(): VbenFormSchema[] {
  return [
    {
      component: markRaw(ElInputTag),
      componentProps: {
        clearable: true,
        placeholder: '请输入,多个编号用回车分隔',
        max: 4,
      },
      fieldName: 'outBoundDocNumberList',
      formItemClass: 'col-span-2',
      label: '出库单号',
    },

    {
      component: 'Input',
      fieldName: 'modifyTime',
      formItemClass: 'col-span-2',
      label: '最近修改时间',
      labelClass: 'ml-3',
    },
  ];
}

/** 表格 */
export function useColumns<T = RowType>(
  onActionClick: OnActionClickFn<T>,
): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      field: 'outBoundDocNumber',
      title: '出库单号',
      minWidth: 200,
    },
    {
      field: 'outBoundDocId',
      title: '出库单ID',
      visible: false,
    },
    {
      field: 'prepDocId',
      title: '备料单ID',
      visible: false,
    },

    {
      field: 'origDocNumber',
      title: '转入编号',
      minWidth: 200,
    },
    {
      field: 'modifyTime',
      width: 180,
      title: '最近修改时间',
    },

    {
      align: 'center',
      cellRender: {
        attrs: {
          nameField: 'outBoundDocNumber',
          nameTitle: '操作',
          onClick: onActionClick,
        },
        name: 'CellOperation',
        options: [
          {
            code: 'view',
            label: '查看',
            type: 'info',
          },
        ],
      },
      field: 'operation',
      fixed: 'right',
      title: '操作',
      width: 100,
    },
  ];
}
