import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      label: '默认仓库',
      fieldName: 'warehouseName',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      label: '默认库位',
      fieldName: 'locationName',
    },
    {
      component: 'Input',
      fieldName: 'safetyInventoryWarnList',
      label: '安全库存预警设置',
      formItemClass: 'col-span-full items-start',
      labelWidth: 120,
    },
    {
      component: 'Input',
      fieldName: 'slowMovingAnalysisList',
      label: '呆滞期设置',
      formItemClass: 'col-span-full items-start',
      labelWidth: 120,
    },
  ];
}
