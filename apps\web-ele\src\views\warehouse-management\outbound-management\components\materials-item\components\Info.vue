<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItemData } from '../types';

import { ImageViewer } from '@girant-web/img-view-component';
import { ElButton, ElTag } from 'element-plus';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<MaterialItemData.DocItem>,
    default: () => ({}),
  },
});

const emits = defineEmits(['handleMaterialCode']);

const handleMaterialCode = () => {
  emits('handleMaterialCode', props.materialItemData.materialId);
};
</script>
<template>
  <div class="flex pt-2">
    <div class="ml-4 flex flex-1 justify-between">
      <div class="flex items-center space-x-2">
        <div>
          <span class="text-primary-500 text-lg font-medium">
            {{ materialItemData.materialName }}
          </span>

          <span
            class="text-primary-500 ml-2 mr-2 text-sm font-medium"
            v-if="materialItemData.materialAlias"
          >
            {{ `(${materialItemData.materialAlias})` }}
          </span>
        </div>

        <ElButton
          type="info"
          link
          class="underline"
          @click="handleMaterialCode"
        >
          {{ materialItemData.materialCode }}
        </ElButton>

        <ImageViewer
          v-if="materialItemData.pictureFileId"
          :img-id="materialItemData.pictureFileId"
          img-css="size-7 flex-shrink-0"
          :show-thumbnail="false"
        />

        <ElTag
          class="!border-blue-500 !bg-blue-500 text-white"
          size="small"
          effect="dark"
          v-if="materialItemData.materialType === '20'"
        >
          原料
        </ElTag>

        <ElTag
          type="danger"
          size="small"
          effect="dark"
          v-if="!materialItemData.isStandard"
        >
          非标
        </ElTag>

        <span class="text-sm text-gray-500">
          规格型号： {{ materialItemData.materialSpecs || '/' }}
        </span>
      </div>
      <div class="text-sm text-gray-600">
        <slot name="rightExtra" :material-item-data="materialItemData"></slot>
        <template v-if="!$slots.rightExtra">
          <span>出库合计：</span>
          <span class="font-medium">{{ materialItemData.quantitySum }}</span>
          <span class="text-gray-500">
            ({{ materialItemData.baseUnitLabel }})
          </span>
        </template>
      </div>
    </div>
  </div>
</template>
