<script setup lang="ts">
import type { InOutCancelDocApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElTag } from 'element-plus';

import { getInOutCancelDocDetail } from '#/api/warehouse-management/index';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './closeBoundInfoData';

const props = defineProps({
  inOutCancelDocId: {
    type: String,
    default: '',
  },
  inOutCancelDocNumber: {
    type: String,
    default: '',
  },
});

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 100 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-7',
});

const inOutCancelDocDetail = ref<InOutCancelDocApi.InOutCancelDocDetail>(
  {} as InOutCancelDocApi.InOutCancelDocDetail,
);

onMounted(async () => {
  const inOutCancelDocDetailRes = await getInOutCancelDocDetail({
    inOutCancelDocId: props.inOutCancelDocId,
    inOutCancelDocNumber: props.inOutCancelDocNumber,
  });
  inOutCancelDocDetail.value = inOutCancelDocDetailRes;
  formApi.setValues(inOutCancelDocDetailRes);
});
</script>

<template>
  <IconFont
    v-if="inOutCancelDocDetail.docStatus === 'passed'"
    name="shenhetongguo1"
    :size="150"
    color="dark:bg-gray-800"
    class="absolute right-20 top-14 text-green-500"
  />

  <Form>
    <template #inOutCancelDocNumber="{ modelValue }">
      <div>
        {{ modelValue }}
        <ElTag
          :type="
            inOutCancelDocDetail.docStatus === 'passed' ? 'success' : 'warning'
          "
        >
          {{ inOutCancelDocDetail?.docStatusLabel }}
        </ElTag>
      </div>
    </template>

    <template #remarkOptionList>
      <template
        v-for="item in inOutCancelDocDetail.remarkOptionList"
        :key="item.optionId"
      >
        <ElTag type="primary">
          {{ item.optionName }}
        </ElTag>
      </template>

      <span class="absolute bottom-[-20px] text-sm text-gray-500">{{
        inOutCancelDocDetail.remark
      }}</span>
    </template>

    <template #docProcess>
      <StepProgress
        v-if="inOutCancelDocDetail.inOutCancelDocNumber"
        :doc-number="inOutCancelDocDetail.inOutCancelDocNumber"
        class="pt-[10px]"
      />
      <div v-else>/</div>
    </template>
  </Form>
</template>
