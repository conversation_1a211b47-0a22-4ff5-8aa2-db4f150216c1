import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

// 统计出库单据数量
export async function getOutBoundDocNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getOutBoundDocNum`,
    params,
  );
}

// 统计出库实际明细子项数量
export async function getOutBoundActualItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getActualItemNum`,
    params,
  );
}

// 统计已出库单据实际明细物料总数
export async function getOutBoundActualMaterialNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getActualMaterialNum`,
    params,
  );
}

// 统计已出库明细物料项数
export async function getOutActualMaterialItemNum(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getActualMaterialItemNum`,
    params,
  );
}

// 统计已出库明细物料总数
export async function getOutActualMaterialItem(params?: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/outBound/getActualMaterialNum`,
    params,
  );
}
