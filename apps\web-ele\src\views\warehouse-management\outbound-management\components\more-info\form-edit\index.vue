<script setup lang="ts">
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { useFormSchema } from './data';

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 60 },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1',
});

const loadMoreInfoData = (data: any) => {
  formApi.setValues(data);
};

// 获取表单数据
const getFormData = async () => {
  const serialNumber: any = await formApi?.getFieldComponentRef('serialNumber');
  const isCompleted = await serialNumber?.getCompleteStatus();
  if (!isCompleted) {
    ElMessage.warning('请等待附件上传完成');
    return;
  }
  const formValues = await formApi.getValues();
  return formValues;
};

defineExpose({
  formApi,
  getFormData,
  loadMoreInfoData,
});
</script>

<template>
  <Form />
</template>
