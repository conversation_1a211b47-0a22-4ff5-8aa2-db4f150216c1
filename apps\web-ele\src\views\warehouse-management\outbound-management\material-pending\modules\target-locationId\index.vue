<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialPendingApi } from '#/api/warehouse-management/index';

import { onMounted, ref, watch } from 'vue';

import { ElMessage } from 'element-plus';

import { getPrepDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';

import locationItemEdit from '../../../components/location-item/form-edit/index.vue';

export interface TargetWarehouse {
  warehouseCode: string;
  warehouseId: string;
  warehouseName: string;
}

const props = defineProps({
  targetWarehouseListData: {
    type: Array as PropType<TargetWarehouse[]>,
    required: true,
  },
  prepDocId: {
    type: String,
    default: '',
  },
});

const locationItemEditRef = ref<InstanceType<typeof locationItemEdit>[]>([]);

const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

const prepItemListMap = ref<MaterialPendingApi.ItemList[]>([]);

const targetWarehouseList = ref<TargetWarehouse[]>([]);

/** 获取备料数据 */
const getPrepDocDetailHandle = async () => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId: props.prepDocId,
      isQueryItem: true,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
  }
};

onMounted(async () => {
  if (props.prepDocId) {
    await getPrepDocDetailHandle();
    prepItemListMap.value =
      prepDocDetail.value?.prepItemList.flatMap(
        (item: MaterialPendingApi.PrepItemList) => {
          return item.itemList;
        },
      ) || [];
  }
});

watch(
  () => props.targetWarehouseListData,
  (newVal: TargetWarehouse[]) => {
    targetWarehouseList.value = props.prepDocId
      ? newVal.map((item) => {
          return {
            ...item,
            // 获取targetLocationId
            locationId:
              prepItemListMap.value.find(
                (prepItem) => prepItem.warehouseId === item.warehouseId,
              )?.targetLocationId || '',
          };
        })
      : newVal;
  },
);

const getTargetLocationList = async () => {
  try {
    const targetLocationList = await Promise.all(
      locationItemEditRef.value.map((item) => item.getFormData()),
    );
    return targetLocationList;
  } catch (error: any) {
    throw new Error(error.message);
  }
};

defineExpose({
  getTargetLocationList,
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>备料目标库位</span>
    </template>

    <template #default>
      <div v-if="targetWarehouseList.length > 0">
        <locationItemEdit
          v-for="item in targetWarehouseList"
          :key="item.warehouseId"
          :target-warehouse="item"
          ref="locationItemEditRef"
        />
      </div>
    </template>
  </FormCard>
</template>
