<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { closeTransferDoc, execTransferDoc } from '#/api/warehouse-management';

import AdjustmentDetails from '../components/adjustment-details/View.vue';
import AdjustmentInformation from '../components/adjustment-info/View.vue';

/** 共享数据 */
const data = ref();

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});

/** 刷新列表 */
const refreshList = () => {
  data.value?.refreshList();
  modalApi.setState({ loading: false }).close();
};

const execHandle = async () => {
  await ElMessageBox.confirm('确定提交调拨吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        modalApi.setState({ loading: true });
        await execTransferDoc(data.value.transferDocId);
        ElMessage.success('执行成功');
        refreshList();
      } catch {
        modalApi.setState({ loading: false });
        ElMessage.error('执行失败');
      }
    })
    .catch(() => {});
};

const closeHandle = async () => {
  await ElMessageBox.confirm('确定提交取消单据吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        modalApi.setState({ loading: true });
        await closeTransferDoc(data.value.transferDocId);
        ElMessage.success('提交成功');
        refreshList();
      } catch {
        ElMessage.error('提交失败');
        modalApi.setState({ loading: false });
      }
    })
    .catch(() => {});
};
</script>

<template>
  <Modal>
    <AdjustmentInformation
      :transfer-doc-id="data.transferDocId"
      :transfer-doc-number="data.transferDocNumber"
    />

    <AdjustmentDetails
      :transfer-doc-id="data.transferDocId"
      :transfer-doc-number="data.transferDocNumber"
    />

    <template #footer>
      <ElButton type="info" @click="modalApi.close()"> 取消 </ElButton>

      <div v-if="data.docStatus === 'passed'">
        <ElButton
          type="primary"
          v-access:code="'wm:stock:transfer:exec'"
          @click="execHandle"
        >
          确认调拨
        </ElButton>
        <ElButton
          type="danger"
          v-access:code="'wm:stock:transfer:close'"
          @click="closeHandle"
        >
          取消单据
        </ElButton>
      </div>
    </template>
  </Modal>
</template>
