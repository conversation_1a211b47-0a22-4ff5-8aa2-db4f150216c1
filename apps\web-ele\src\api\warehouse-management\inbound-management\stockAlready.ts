import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

// -------------------------------------------------已入库查询-------------------------------------------------
/** 已入库单据接口 */
export namespace AlreadyInBoundDocApi {
  /** 已入库单据查询参数 */
  export interface AlreadyInBoundDocPageParams {
    /** 入库单据号列表，多个用英文逗号分隔 */
    inBoundDocNumberList?: string;
    /** 源单据类型代码列表，多个用英文逗号分隔 */
    origDocTypeCodeList?: string;
    /** 源单据号列表，多个用英文逗号分隔 */
    origDocNumberList?: string;
    /** 申请人ID列表，多个用英文逗号分隔 */
    applyUserList?: string;
    /** 执行仓管员ID列表，多个用英文逗号分隔 */
    executorUserList?: string;
    /** 是否补录，不填则展示全部，true-补录，false-非补录 */
    isRectify?: boolean;
    /** 申请起始时间，时间格式：yyyy-MM-dd HH:mm */
    applyStartTime?: string;
    /** 申请终止时间，时间格式：yyyy-MM-dd HH:mm */
    applyEndTime?: string;
    /** 执行入库起始时间，时间格式：yyyy-MM-dd HH:mm */
    executorStartTime?: string;
    /** 执行入库终止时间，时间格式：yyyy-MM-dd HH:mm */
    executorEndTime?: string;
    /** 单据状态值列表，字典wmInDocStatus */
    docStatusList?: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  /** 已入库单据记录 */
  export interface AlreadyInBoundDocRecord {
    /* 入库单据ID */
    inBoundDocId: number;

    /* 入库单据编号 */
    inBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据ID */
    origDocId: number;

    /* 源单据编号 */
    origDocNumber: string;

    /* 申请人ID */
    applyUser: number;

    /* 申请人姓名 */
    applyUserName: string;

    /* 申请人部门ID */
    applyUserDeptId: number;

    /* 申请人部门名称 */
    applyUserDeptName: string;

    /* 执行仓管员ID */
    executorUser: number;

    /* 执行仓管员姓名 */
    executorUserName: string;

    /* 执行仓管员部门ID */
    executorUserDeptId: number;

    /* 执行仓管员部门名称 */
    executorUserDeptName: string;

    /* 仓管员确认方式值，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethod: string;

    /* 仓管员确认方式标签，枚举WmInOutManagerConfirmMethodEnums */
    executorConfirmMethodLabel: string;

    /* 是否补录 */
    isRectify: boolean;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    applyTime: Record<string, unknown>;

    /* 执行入库时间，时间格式：yyyy-MM-dd HH:mm */
    executorTime: Record<string, unknown>;

    /* 是否自动完成入库 */
    isAutoIo: boolean;
  }

  /** 已入库单据分页响应 */
  export interface AlreadyInBoundDocPageRes {
    /** 是否成功 */
    success: boolean;
    /** 状态码 */
    code: number;
    /** 数据 */
    data: {
      /** 当前页 */
      pageNum: number;
      /** 分页条数 */
      pages: number;
      /** 分页数 */
      pageSize: number;
      /** 数据 */
      records: AlreadyInBoundDocRecord[];
      /** 总条数 */
      total: number;
    };
    /** 消息 */
    msg: string;
  }
}

/**
 * 查询已入库单据分页列表
 */
export async function getAlreadyInBoundDocPage(
  params: AlreadyInBoundDocApi.AlreadyInBoundDocPageParams,
) {
  return requestClient.post<AlreadyInBoundDocApi.AlreadyInBoundDocPageRes>(
    `${warehousePath}/wm/inBound/stocked/getInBoundDocPage`,
    { ...params },
  );
}

/** 导出已入库单据列表*/
export async function exportStockedInBoundDoc(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inBound/stocked/exportStockedInBoundDoc`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
