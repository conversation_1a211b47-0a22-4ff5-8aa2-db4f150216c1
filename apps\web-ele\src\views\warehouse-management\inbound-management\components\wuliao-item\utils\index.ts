import type { MaterialItem } from '../types';

// 数量关系
export const QUANTITY_RELATION = [
  {
    enumKey: 'UNLIMITED',
    enumLabel: '不限制',
    enumValue: 'unlimited',
  },
  {
    enumKey: 'GTE',
    enumLabel: '不小于申请数量',
    enumValue: 'gte',
  },
  {
    enumKey: 'LTE',
    enumLabel: '不大于申请数量',
    enumValue: 'lte',
  },
  {
    enumKey: 'EQ',
    enumLabel: '等于申请数量',
    enumValue: 'eq',
  },
];

// 处理子项数据，合并相同仓库的数据
export const initWarehouseData = (
  ItemList: MaterialItem.Item[],
): {
  ItemList: MaterialItem.WarehouseItemDataType[];
} => {
  // 使用 Map 来按 warehouseId 分组
  const warehouseMap = new Map<string, MaterialItem.WarehouseItemDataType>();

  ItemList.forEach((item) => {
    const {
      quantity,

      /* 批次号 */
      batchNumber,

      /* 库位编号 */
      locationCode,

      /* 库位id */
      locationId,

      /* 库位名称 */
      locationName,

      /* 均价（单价），默认不可见 */
      unitPrice,

      /* 仓库编号 */
      warehouseCode,

      /* 仓库id */
      warehouseId,

      /* 仓库名称 */
      warehouseName,
    } = item;

    // 如果这个仓库ID还没有记录，创建新记录
    if (!warehouseMap.has(warehouseId)) {
      warehouseMap.set(warehouseId, {
        warehouseId,
        warehouseCode,
        warehouseName,
        locationList: [],
        timestamp: Date.now(),
      });
    }

    // 获取当前仓库记录
    const warehouse = warehouseMap.get(warehouseId);

    // 添加库位信息
    warehouse?.locationList?.push({
      locationId,
      locationCode,
      locationName,
      unitPrice: unitPrice || 0,
      batchNumber: batchNumber || '',
      quantity,
    });
  });

  return {
    // 将 Map 转换为数组
    ItemList: [...warehouseMap.values()],
  };
};

// 判断申请数量与填入数量的关系
export const checkEntryQuantitySum = ({
  applyQuantity,
  entryQuantity,
  quantityRelation,
}: {
  applyQuantity: number;
  entryQuantity: number;
  quantityRelation: string;
}) => {
  if (quantityRelation === 'unlimited') {
    return false;
  }

  switch (quantityRelation) {
    case 'eq': {
      return applyQuantity === entryQuantity;
    }
    case 'gte': {
      return entryQuantity >= applyQuantity;
    }
    case 'lte': {
      return entryQuantity <= applyQuantity;
    }
  }
};
