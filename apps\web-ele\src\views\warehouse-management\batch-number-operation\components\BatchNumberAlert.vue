<script setup lang="ts">
import { ref, unref } from 'vue';

import { isEmpty } from '@vben/utils';

import { ElButton, ElPopover } from 'element-plus';

import { getInventoryList } from '#/api/warehouse-management';
import IconFont from '#/components/IconFont/IconFont.vue';

const props = defineProps({
  batchNumber: {
    type: String,
    default: '',
  },
  locationId: {
    type: String,
    default: '',
  },
  materialId: {
    type: String,
    default: '',
  },
});

const ALERT_MESSAGE = [
  {
    message: '所填批次号不存在，将新增该批次',
    type: 'noHas',
  },
  {
    message: '所填批次号已存在，将合并到该批次',
    type: 'has',
  },
  {
    message: '没有填写批次号，系统将自动生成',
    type: 'no',
  },
];

const loading = ref(false);

const currentType = ref<string>('no');

const getInventoryListHandle = async () => {
  try {
    loading.value = true;
    const inventoryList = await getInventoryList({
      batchNumber: props.batchNumber,
    });

    loading.value = false;
    return inventoryList;
  } catch {
    loading.value = false;
    return null;
  }
};

const buttonRef = ref();
const popoverRef = ref();
const onClickPopover = async () => {
  if (isEmpty(props.batchNumber)) {
    currentType.value = 'no';
  } else {
    const res = await getInventoryListHandle();
    currentType.value = res?.length ? 'has' : 'noHas';
  }
  unref(popoverRef).popperRef?.delayHide?.();
};
</script>

<template>
  <div>
    <ElButton
      size="small"
      circle
      ref="buttonRef"
      @click="onClickPopover"
      :disabled="isEmpty(locationId) || isEmpty(materialId)"
    >
      <IconFont name="tishi" :size="15" />
    </ElButton>

    <ElPopover
      ref="popoverRef"
      :virtual-ref="buttonRef"
      trigger="click"
      virtual-triggering
      placement="top"
      width="220"
    >
      <span class="text-xs text-red-500" v-loading="loading">
        {{
          ALERT_MESSAGE.find((item) => item.type === currentType)?.message ||
          '/'
        }}
      </span>
    </ElPopover>
  </div>
</template>
