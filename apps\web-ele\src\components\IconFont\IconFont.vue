<script lang="ts" setup>
import { computed, defineProps } from 'vue';

import './iconfont/iconfont.css';

defineOptions({
  name: 'IconFont',
});

const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  size: {
    type: [Number, String],
    default: 16,
  },
  color: {
    type: String,
    default: '#303133',
  },
});

const iconClass = `iconfont ${props.name ? `icon-girant-web${props.name}` : ''}`;
const computedSize = computed(() => {
  if (typeof props.size === 'number') {
    return `${props.size}px`;
  }
  return props.size;
});
</script>

<template>
  <i :class="iconClass" :style="{ fontSize: computedSize, color }"></i>
</template>
<style scoped>
.iconfont {
  font-family: iconfont !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
