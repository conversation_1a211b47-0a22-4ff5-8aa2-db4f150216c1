<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessageBox } from 'element-plus';

/** 共享数据 */
const data = ref();

const [Modal, modalApi] = useVbenModal({
  destroyOnClose: true,
  closeOnClickModal: false,
  centered: true,
  fullscreenButton: false,
  onClosed() {
    modalApi.close();
    data.value?.inboundCancel();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      data.value = modalApi.getData<Record<string, any>>();
    }
  },
});

const submitAllForm = () => {
  ElMessageBox.confirm('确认执行入库吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(() => {
      data.value?.submitAllForm();
      modalApi.close();
    })
    .catch(() => {
      data.value?.inboundCancel();
    });
};
</script>

<template>
  <Modal>
    <div class="bg-primary-50 rounded-lg p-4 font-bold">
      <div>
        共领料
        <span class="text-primary">
          {{ data.materialCount }}
        </span>
        种，数量合计
        <span class="text-primary">
          {{ data.entryQuantitySum }}
        </span>
      </div>

      <div class="mt-2 text-red-400">
        注意应发实发数量不一致的有
        <span class="text-red-500">
          {{ data.applyQuantityEqualCount }}
        </span>
        种
      </div>
    </div>
    <template #footer>
      <ElButton type="info" @click="modalApi.close()"> 取消 </ElButton>

      <div>
        <ElButton type="primary" @click="submitAllForm"> 确认入库 </ElButton>
      </div>
    </template>
  </Modal>
</template>
