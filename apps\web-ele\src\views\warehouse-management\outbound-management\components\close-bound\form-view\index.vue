<script setup lang="ts">
import type { InOutCancelDocApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { ElScrollbar } from 'element-plus';

import { getInOutCancelDocDetail } from '#/api/warehouse-management/index';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';

import CloseBoundInfo from './closeBoundInfo.vue';

const props = defineProps({
  inOutCancelDocId: {
    type: String,
    default: '',
  },
  inOutCancelDocNumber: {
    type: String,
    default: '',
  },
});

const inOutCancelDocDetail = ref<InOutCancelDocApi.InOutCancelDocDetail>(
  {} as InOutCancelDocApi.InOutCancelDocDetail,
);

onMounted(async () => {
  const inOutCancelDocDetailRes = await getInOutCancelDocDetail({
    inOutCancelDocId: props.inOutCancelDocId,
    inOutCancelDocNumber: props.inOutCancelDocNumber,
  });
  inOutCancelDocDetail.value = inOutCancelDocDetailRes;
});
</script>

<template>
  <FormCard :is-footer="false">
    <template #title>
      <span>取消单信息</span>
    </template>

    <template #default>
      <CloseBoundInfo
        :in-out-cancel-doc-id="inOutCancelDocId"
        :in-out-cancel-doc-number="inOutCancelDocNumber"
      />
    </template>
  </FormCard>

  <FormCard :is-footer="false">
    <template #title>
      <span>审核流程</span>
    </template>
    <template #default>
      <ElScrollbar>
        <ApprovalTimeline
          v-if="inOutCancelDocDetail.processInstanceId"
          :process-instance-id="inOutCancelDocDetail.processInstanceId"
        />
      </ElScrollbar>
    </template>
  </FormCard>
</template>
