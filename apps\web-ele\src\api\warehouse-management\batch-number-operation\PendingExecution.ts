import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace PendingExecutionApi {}

/** 删除待我提交的批次号处理单据*/
export async function delBatchnumDoc(batchnumDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/batchnum/delBatchnumDoc/${batchnumDocId}`,
  );
}

/** 执行批次号处理单据 */
export async function execBatchnumDoc(batchnumDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/batchnum/execBatchnumDoc/${batchnumDocId}`,
  );
}

/** 关闭批次号处理单据 */
export async function closeBatchnumDoc(batchnumDocId: string) {
  return requestClient.get(
    `${warehousePath}/wm/batchnum/closeBatchnumDoc/${batchnumDocId}`,
  );
}
