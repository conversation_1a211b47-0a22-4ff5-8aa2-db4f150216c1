<script setup lang="ts">
import { computed, ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import { closeTransferDoc, execTransferDoc } from '#/api/warehouse-management';

import FormToWarehouseTansfer from '../../components/FormToWarehouseTansfer.vue';

const props = defineProps({
  transferDocId: {
    type: String,
    default: '',
  },
  transferDocNumber: {
    type: String,
    default: '',
  },
  docStatus: {
    type: String,
    default: '',
  },
  isView: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits([
  'handleCancel',
  'submitSuccess',
  'execSuccess',
  'saveSuccess',
  'transferLoading',
  'closeSuccess',
]);

const handleCancel = () => {
  emits('handleCancel');
};

const currentIsView = computed(() => {
  return props.docStatus === 'reject' ? false : props.isView;
});

const FormToWarehouseTansferRef =
  ref<InstanceType<typeof FormToWarehouseTansfer>>();

const submitHandle = async () => {
  await ElMessageBox.confirm('确定提交吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        const result = await FormToWarehouseTansferRef.value?.submitHandle();
        if (result) {
          emits('submitSuccess');
        }
      } catch {
        ElMessage.error('提交失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};

const execHandle = async () => {
  await ElMessageBox.confirm('确定确认调拨吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        await execTransferDoc(props.transferDocId);
        emits('execSuccess');
      } catch {
        ElMessage.error('执行失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};

const saveHandle = async () => {
  await ElMessageBox.confirm('确定暂存吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        const result = await FormToWarehouseTansferRef.value?.saveHandle();
        if (result) {
          emits('saveSuccess');
        }
      } catch {
        ElMessage.error('暂存失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};

const closeHandle = async () => {
  await ElMessageBox.confirm('确定取消单据吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        emits('transferLoading', true);
        await closeTransferDoc(props.transferDocId);
        emits('closeSuccess');
      } catch {
        ElMessage.error('取消失败');
      } finally {
        emits('transferLoading', false);
      }
    })
    .catch(() => {});
};
</script>

<template>
  <div class="h-full">
    <FormToWarehouseTansfer
      ref="FormToWarehouseTansferRef"
      :transfer-doc-id="transferDocId"
      :transfer-doc-number="transferDocNumber"
      :is-view="currentIsView"
    >
      <template #btn-group>
        <div class="flex items-center justify-end gap-2">
          <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
          <div v-if="!isView" v-access:code="'wm:stock:transfer:submit'">
            <ElButton type="primary" @click="saveHandle">暂存</ElButton>
          </div>
          <div
            v-if="docStatus === 'checking' || docStatus === 'reject' || !isView"
            v-access:code="'wm:stock:transfer:submit'"
          >
            <ElButton type="primary" @click="submitHandle">
              {{ docStatus === 'reject' ? '重新提交' : '提交' }}
            </ElButton>
          </div>
          <div v-if="docStatus === 'passed'">
            <ElButton
              type="primary"
              @click="execHandle"
              v-access:code="'wm:stock:transfer:exec'"
            >
              确认调拨
            </ElButton>
            <ElButton
              type="danger"
              @click="closeHandle"
              v-access:code="'wm:stock:transfer:close'"
            >
              取消单据
            </ElButton>
          </div>
        </div>
      </template>
    </FormToWarehouseTansfer>
  </div>
</template>
