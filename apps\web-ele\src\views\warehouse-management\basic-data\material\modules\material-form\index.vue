<script setup lang="ts">
import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';

import { getMaterialDetail } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 物料编号 */
  materialCode: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 是否标准物料 */
const isStandard = ref(false);
/** 物料信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

/** 根据物料id或编号查询物料详细信息 */
const getMaterialInfo = async (materialId: string, materialCode?: string) => {
  try {
    loading.value = true;
    const res = await getMaterialDetail(materialId, materialCode);
    isStandard.value = res?.isStandard || false;
    formApi.setValues(res);
  } catch (error) {
    console.error(error);
    // ElMessage.error('获取物料信息失败');
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  if (props.materialId || props.materialCode) {
    getMaterialInfo(props.materialId, props.materialCode);
  }
});
defineExpose({
  Form,
  formApi,
  props,
});
</script>

<template>
  <FormCard title="物料信息" :is-footer="false">
    <Form v-loading="loading">
      <template #materialCode="row">
        {{ row.value || '/' }}
        <ElTag v-if="!isStandard" type="primary" size="small" class="ml-[10px]">
          非标
        </ElTag>
      </template>
    </Form>
  </FormCard>
</template>
