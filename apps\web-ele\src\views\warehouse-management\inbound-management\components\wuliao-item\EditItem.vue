<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItem } from './types/index.ts';

import type { WarehouseListForMaterialListApi } from '#/api/warehouse-management/index';

import { computed, nextTick, onMounted, ref, watch } from 'vue';

import { Plus } from '@element-plus/icons-vue';
import { ElMessage, ElMessageBox } from 'element-plus';

import { add } from '#/utils/numberUtils';

import EditMaterialItem from './components/EditMaterialItem.vue';
import InventoryPopover from './components/PendingInventoryPopover.vue';
import ShowQuantity from './components/ShowQuantity.vue';
import {
  checkEntryQuantitySum,
  initWarehouseData,
  QUANTITY_RELATION,
} from './utils';

const props = defineProps({
  materialItemData: {
    type: Object as PropType<MaterialItem.MaterialInfo>,
    default: () => ({}),
  },

  warehouseListForMaterial: {
    type: Object as PropType<WarehouseListForMaterialListApi.WarehouseListForMaterialList>,
    default: () => ({}),
  },

  // 默认可以改变仓库选择
  inChangeWarehouse: {
    type: Boolean,
    default: true,
  },

  // 入库数量限制
  inQuantityLimit: {
    type: String,
    default: '',
  },
});

const warehouseItemRef = ref<InstanceType<typeof EditMaterialItem>[]>([]);

//  仓库item数据列表 合并后每个仓库有效的数据（过滤出物料信息中，仓库在可入库仓库列表中的数据）
const warehouseItemList = ref<MaterialItem.WarehouseItemDataType[]>([]);

watch(
  () => props.materialItemData,
  async (newVal: MaterialItem.MaterialInfo) => {
    const { ItemList } = initWarehouseData(newVal.itemList);
    warehouseItemList.value = ItemList;
  },
  { immediate: true },
);

// 默认仓库ID,没有就拿仓库列表第一个
const defaultWarehouseId = ref<string>('');

// 已选中的仓库id列表
const currentSelectedWarehouseIdList = ref<Array<string>>([]);

// 仓库下拉框数据：处理可入库的仓库列表，不能选择已选中的仓库
const selectWarehouseList = computed<MaterialItem.SelectWarehouseListType[]>(
  () => {
    return props.warehouseListForMaterial.warehouseList.map((item) => {
      return {
        disabled: currentSelectedWarehouseIdList.value.includes(
          item.warehouseId.toString(),
        ),
        label: item.warehouseName,
        value: item.warehouseId,
        availableInventory: item.availableInventory,
        inventory: item.inventory,
      };
    });
  },
);

onMounted(() => {
  defaultWarehouseId.value =
    props.warehouseListForMaterial.warehouseList.find(
      (item) =>
        item.warehouseId === props.warehouseListForMaterial.mainWarehouseId,
    )?.warehouseId ||
    props.warehouseListForMaterial.warehouseList[0]?.warehouseId ||
    '';
  currentSelectedWarehouseIdList.value = [defaultWarehouseId.value];

  // 初始化一条仓库item数据
  const initWarehouseItemData = {
    warehouseId: defaultWarehouseId.value,
    // 时间戳
    timestamp: Date.now(),
    locationList: [
      {
        quantity: props.materialItemData.quantitySum,
        locationId: '',
        batchNumber: '',
        unitPrice: 0,
      },
    ],
  };
  warehouseItemList.value = [initWarehouseItemData];
});

// 获取全部已选中的仓库id
const getAllSelectedWarehouseId = () => {
  return warehouseItemRef.value.map((item) => item.currentWarehouseId);
};

// 获取填入数量
const getEntryQuantitySum = () => {
  const fillQuantityList = warehouseItemRef.value.map(
    (item) => item.currentWarehouseFillQuantity,
  );

  let fillQuantity = 0;
  for (const item of fillQuantityList) {
    fillQuantity = add(fillQuantity, item);
  }
  entryQuantitySum.value = fillQuantity;
};

// 仓库改变
const warehouseChangeHandle = () => {
  currentSelectedWarehouseIdList.value = getAllSelectedWarehouseId();
};

// 填入数量
const entryQuantitySum = ref(0);

// 增加仓库item
const addWarehouseItem = async () => {
  if (
    await ElMessageBox.confirm('确定添加仓库吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    warehouseItemList.value.push({
      warehouseId: '',
      timestamp: Date.now(),
      locationList: [
        {
          quantity: 0,
          locationId: '',
          batchNumber: '',
          unitPrice: 0,
        },
      ],
    });
  }
};

// 删除仓库item
const deleteWarehouseItem = async (timestamp: number) => {
  if (
    await ElMessageBox.confirm('确定删除吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    warehouseItemList.value = warehouseItemList.value.filter(
      (item) => item.timestamp !== timestamp,
    );

    nextTick(() => {
      warehouseChangeHandle();
      getEntryQuantitySum();
    });
  }
};

const getSubmitFormData = async () => {
  if (
    !checkEntryQuantitySum({
      applyQuantity: props.materialItemData.quantitySum,
      entryQuantity: entryQuantitySum.value,
      quantityRelation: props.inQuantityLimit,
    })
  ) {
    ElMessage.warning(
      `请填写${
        QUANTITY_RELATION.find(
          (item) => item.enumValue === props.inQuantityLimit,
        )?.enumLabel
      }`,
    );
    return false;
  }

  const validateFormDataList = await Promise.all(
    warehouseItemRef.value.map((item) => item.validateFormData()),
  );

  if (validateFormDataList.some((item) => !item)) {
    ElMessage.error('请填写正确的表单数据');
    return false;
  }

  const formDataList = await Promise.all(
    warehouseItemRef.value.map((item) => item.getFormData()),
  );

  const submitFormData = formDataList.flat();
  return submitFormData;
};

defineExpose({
  getSubmitFormData,
});
</script>

<template>
  <div class="relative mb-8">
    <div class="absolute right-0 top-[-30px]">
      <ShowQuantity
        :quantity-sum="materialItemData.quantitySum"
        :entry-quantity-sum="entryQuantitySum"
        :base-unit-label="materialItemData.baseUnitLabel"
      />
    </div>

    <div class="absolute bottom-[-25px] left-0">
      <ElButton
        type="primary"
        link
        @click="addWarehouseItem"
        v-if="
          inChangeWarehouse &&
          warehouseItemList.length <
            warehouseListForMaterial?.warehouseList?.length
        "
      >
        <el-icon class="!text-base">
          <Plus />
        </el-icon>
        添加仓库
      </ElButton>
    </div>
    <template
      v-for="(item, index) in warehouseItemList"
      :key="item.warehouseId"
    >
      <EditMaterialItem
        ref="warehouseItemRef"
        :material-item-data="materialItemData"
        :warehouse-item-data="item"
        :select-warehouse-list="selectWarehouseList"
        :in-change-warehouse="inChangeWarehouse"
        @warehouse-change="warehouseChangeHandle"
        @entry-quantity-change="getEntryQuantitySum"
        :index="index"
      >
        <template #inventory-wrapper="{ showData }">
          <InventoryPopover
            :warehouse-item-data="showData.warehouseItemData"
            :current-warehouse-fill-quantity="
              showData.currentWarehouseFillQuantity
            "
            class="mx-2"
          />
        </template>

        <template
          #delete-wrapper
          v-if="inChangeWarehouse && warehouseItemList.length > 1"
        >
          <ElButton
            type="danger"
            link
            size="small"
            @click="deleteWarehouseItem(item.timestamp)"
          >
            删除仓库
          </ElButton>
        </template>
      </EditMaterialItem>
    </template>
  </div>
</template>
