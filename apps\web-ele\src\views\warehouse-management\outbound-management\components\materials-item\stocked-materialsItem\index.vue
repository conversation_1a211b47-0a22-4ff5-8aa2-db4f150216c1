<script setup lang="ts">
import type { PropType } from 'vue';

import type { MaterialItemData } from '../types';

import type { WarehouseListForMaterialListApi } from '#/api/warehouse-management/index';

import { computed, onMounted, ref } from 'vue';

import { Right } from '@element-plus/icons-vue';
import { ElMessage } from 'element-plus';

import Info from '../components/Info.vue';
import WarehouseItem from './warehouse-item/index.vue';

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    type: Object as PropType<MaterialItemData.DocItem>,
    default: () => ({}),
  },

  warehouseListForMaterial: {
    type: Object as PropType<WarehouseListForMaterialListApi.WarehouseListForMaterialList>,
    default: () => ({}),
  },
});

const emit = defineEmits(['handleMaterialCode']);

// 当前物料可出入库的仓库列表
const warehouseListForMaterialList = ref<
  WarehouseListForMaterialListApi.warehouseItem[]
>([]);

// 计算总数
const getTotalQuantity = (quantityList: number[]) => {
  return quantityList.reduce((acc: number, item: number) => acc + item, 0);
};

// 当前仓库可用量
const currentWarehouseAvailableQuantity = computed(() => {
  return warehouseListForMaterialList.value.length > 0
    ? getTotalQuantity(
        warehouseListForMaterialList.value.map((item) => {
          return item.inventory;
        }),
      )
    : 0;
});

// 当前仓库库存量
const currentWarehouseInventoryQuantity = computed(() => {
  return warehouseListForMaterialList.value.length > 0
    ? getTotalQuantity(
        warehouseListForMaterialList.value.map((item) => {
          return item.inventory;
        }),
      )
    : 0;
});
// 填入数量
const entryQuantitySum = ref(0);
// 计算填入数量
const getEntryQuantitySum = async () => {
  const fillQuantityList = warehouseItemList.value.flatMap((item) => {
    return item.locationList.map((location) => location.quantity);
  });

  const sum = getTotalQuantity(fillQuantityList);

  entryQuantitySum.value = sum;

  return sum;
};

// 合并后每个仓库有效的数据（过滤出物料信息中，仓库在可入库仓库列表中的数据）
const warehouseItemList = ref<MaterialItemData.WarehouseGroup[]>([]);
// 处理子项数据，合并相同仓库的数据
function initWarehouseData(itemList: MaterialItemData.Item[]): {
  ItemList: MaterialItemData.WarehouseGroup[];
} {
  // 使用 Map 来按 warehouseId 分组
  const warehouseMap = new Map<string, MaterialItemData.WarehouseGroup>();

  itemList.forEach((item) => {
    const {
      quantity = 0,

      /* 批次号 */
      batchNumber,

      /* 库位编号 */
      locationCode,

      /* 库位id */
      locationId,

      /* 库位名称 */
      locationName,

      /* 均价（单价），默认不可见 */
      unitPrice,

      /* 仓库编号 */
      warehouseCode,

      /* 仓库id */
      warehouseId,

      /* 仓库名称 */
      warehouseName,
    } = item;

    // 如果这个仓库ID还没有记录，创建新记录
    if (!warehouseMap.has(warehouseId)) {
      warehouseMap.set(warehouseId, {
        warehouseId,
        warehouseCode,
        warehouseName,
        locationList: [],
      });
    }

    // 获取当前仓库记录
    const warehouse = warehouseMap.get(warehouseId)!;

    // 添加库位信息
    warehouse.locationList.push({
      locationId,
      locationCode,
      locationName,
      unitPrice,
      batchNumber,
      quantity,
    });
  });

  return {
    // 将 Map 转换为数组
    ItemList: [...warehouseMap.values()],
  };
}

onMounted(() => {
  warehouseListForMaterialList.value =
    props.warehouseListForMaterial?.warehouseList || [];

  const { ItemList } = initWarehouseData(props.materialItemData.itemList);
  warehouseItemList.value = ItemList;

  getEntryQuantitySum();
});

const getData = (): MaterialItemData.WarehouseGroup[] => {
  return warehouseItemList.value;
};

const handlePurchaseApply = () => {
  ElMessage.warning('采购申请功能暂未开放');
};

const handleUnlockInventory = () => {
  ElMessage.warning('解锁库存功能暂未开放');
};

const handleMaterialCode = () => {
  emit('handleMaterialCode', props.materialItemData.materialId);
};

defineExpose({
  getData,
  entryQuantitySum,
});
</script>
<template>
  <div class="rounded-lg bg-white">
    <Info
      :material-item-data="materialItemData"
      @handle-material-code="handleMaterialCode"
    >
      <template #rightExtra>
        <div>
          <div
            class="flex items-center text-sm font-medium text-red-500"
            v-if="
              warehouseListForMaterialList.length > 0 &&
              materialItemData.quantitySum > currentWarehouseAvailableQuantity
            "
          >
            <span class="mr-1">库存不足</span>

            <el-button
              type="primary"
              link
              class="underline"
              @click="handlePurchaseApply"
            >
              <el-icon><Right /></el-icon>
              采购申请
            </el-button>

            <el-button
              v-if="
                currentWarehouseInventoryQuantity > materialItemData.quantitySum
              "
              type="primary"
              link
              class="underline"
              @click="handleUnlockInventory"
            >
              <el-icon><Right /></el-icon>
              解锁库存
            </el-button>
          </div>
          <div class="text-sm text-gray-800">
            <span>应出 / 实出：</span>
            <span class="font-medium">{{ materialItemData.quantitySum }}</span>
            <span class="mx-1">/</span>
            <span
              class="font-medium"
              :class="[
                entryQuantitySum === materialItemData.quantitySum
                  ? 'text-green-500'
                  : 'text-red-500',
              ]"
            >
              {{ entryQuantitySum }}
            </span>
            <span class="ml-1 text-gray-500">
              ({{ materialItemData.baseUnitLabel || '-' }})
            </span>
          </div>
        </div>
      </template>
    </Info>

    <div class="mt-4">
      <template v-for="item in warehouseItemList" :key="item.warehouseId">
        <WarehouseItem :warehouse-item-data="item" />
      </template>
    </div>
  </div>
</template>
