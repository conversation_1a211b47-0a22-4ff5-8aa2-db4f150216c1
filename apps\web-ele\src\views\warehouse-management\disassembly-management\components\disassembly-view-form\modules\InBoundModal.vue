<script setup lang="ts">
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import InboundViewForm from '#/views/warehouse-management/inbound-management/components/view-inbound-info/index.vue';
import MaterialViewForm from '#/views/warehouse-management/inbound-management/modules/materials-list/ViewForm/index.vue';

/** 共享数据 */
const shareData = ref();

const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  closeOnClickModal: false,
  onClosed() {
    modalApi.close();
  },
  onOpenChange(isOpen) {
    if (isOpen) {
      shareData.value = modalApi.getData<Record<string, any>>();
    }
  },
});
</script>

<template>
  <Modal>
    <InboundViewForm
      :in-bound-doc-id="shareData.inBoundDocId"
      :in-bound-doc-number="shareData.inBoundDocNumber"
    />
    <MaterialViewForm
      :in-bound-doc-id="shareData.inBoundDocId"
      :in-bound-doc-number="shareData.inBoundDocNumber"
    />
  </Modal>
</template>
