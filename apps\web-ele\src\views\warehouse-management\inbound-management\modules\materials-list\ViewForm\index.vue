<script setup lang="ts">
import type { MaterialItem } from '../../../components/wuliao-item/types';

import type { InBoundDocApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage } from 'element-plus';

import { getInBoundDocDetail } from '#/api/warehouse-management/index';
import FormCard from '#/components/form-card/Index.vue';
import TriangleCard from '#/components/triangle-card/Index.vue';

import MaterialInfo from '../../../components/wuliao-item/components/MaterialInfo.vue';
import ViewItem from '../../../components/wuliao-item/ViewItem.vue';
import MaterialInfoDetailModal from '../components/MaterialInfoDetailModal.vue';
import { transformInBoundData } from '../utils';

const props = defineProps({
  inBoundDocId: {
    type: String,
    default: '',
  },
  inBoundDocNumber: {
    type: String,
    default: '',
  },
});

const inBoundData = ref<InBoundDocApi.InBoundDocDetail>(
  {} as InBoundDocApi.InBoundDocDetail,
);

const formatData = ref<MaterialItem.MaterialItemData>(
  {} as MaterialItem.MaterialItemData,
);

/** 获取数据 */
const getInBoundDocDetailHandle = async () => {
  try {
    const inBoundRes = await getInBoundDocDetail({
      inBoundDocId: props.inBoundDocId,
      inBoundDocNumber: props.inBoundDocNumber,
      isQueryItem: true,
    });
    inBoundData.value = inBoundRes || ({} as InBoundDocApi.InBoundDocDetail);
    return inBoundRes;
  } catch {
    ElMessage.error('获取入库单据失败');
  }
};

const [Modal, ModalApi] = useVbenModal({
  connectedComponent: MaterialInfoDetailModal,
  destroyOnClose: true,
});

const handleMaterialCode = (materialId: string) => {
  ModalApi?.setState({ title: '物料信息' })
    .setData({
      materialId,
    })
    .open();
};

onMounted(async () => {
  const inBoundRes = await getInBoundDocDetailHandle();
  if (inBoundRes) {
    formatData.value = transformInBoundData(inBoundRes);
  }
});
</script>

<template>
  <Modal class="h-full w-10/12" />

  <FormCard :is-footer="false" title="入库明细">
    <template #titleMore>
      <span class="text-sm text-gray-500">
        共
        <span class="text-primary-500">
          {{ inBoundData?.inBoundItemList?.length || 0 }}
        </span>
        种物料
      </span>
    </template>
    <template
      v-for="(itemData, index) in formatData.inBoundItemList"
      :key="itemData.materialId"
    >
      <TriangleCard :number="index + 1" title="" class="mb-2 pt-2">
        <template #content>
          <MaterialInfo
            :material-item-info="itemData"
            @handle-material-code="handleMaterialCode"
            class="mb-2"
          />

          <ViewItem
            :material-item-data="itemData"
            :in-bound-doc-id="inBoundDocId"
          />
        </template>
      </TriangleCard>
    </template>
  </FormCard>
</template>
