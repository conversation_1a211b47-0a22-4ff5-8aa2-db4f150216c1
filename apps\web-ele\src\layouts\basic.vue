<script lang="ts" setup>
import type { NotificationItem } from '@vben/layouts';

import { computed, onMounted, ref, watch } from 'vue';

import { AuthenticationLoginExpiredModal, useVbenModal } from '@vben/common-ui';
import { useWatermark } from '@vben/hooks';
import {
  BasicLayout,
  LockScreen,
  Notification,
  UserDropdown,
} from '@vben/layouts';
import { preferences } from '@vben/preferences';
import { useAccessStore, useUserStore } from '@vben/stores';

import { getReceiveMsgPage } from '#/api';
import { useAuthStore } from '#/store';
import { WS } from '#/utils/socket/common-socketio';
import LoginForm from '#/views/_core/authentication/login.vue';
// import FormView from '#/views/system-settings/user/modules/form-view/index.vue';
import FormView from '#/views/demos/element/index.vue';

import OrganizationalDropdown from './component/organizational-dropdown.vue';

const notifications = ref<NotificationItem[]>([]);

const userStore = useUserStore();
const authStore = useAuthStore();
const accessStore = useAccessStore();
const { destroyWatermark, updateWatermark } = useWatermark();
const showDot = computed(() =>
  notifications.value.some((item) => !item.isRead),
);

const menus = computed(() => []);

const avatar = computed(() => {
  return userStore.userInfo?.avatar ?? preferences.app.defaultAvatar;
});

const isShowOrgList = computed(() => {
  return userStore.userInfo?.orgList.length > 0;
});

const [FormModal, FormModalApi] = useVbenModal({
  footer: true,
  showConfirmButton: false,
  showCancelButton: true,
});

const handleMessage = (message: any) => {
  console.error('业务页面接收到订阅消息:', message);

  // 调接口
  // // 处理接收到的消息
  // ElNotification({
  //   title: '消息提示',
  //   message: message.content,
  //   type: 'success',
  // });
};

const getReceiveMsg = async () => {
  const response = await getReceiveMsgPage({
    pageSize: 5,
    pageNum: 1,
    isRead: false,
  });
  notifications.value = response.records;
};

const handleAvatarClick = () => {
  FormModalApi.setState({ title: '个人信息' }).open();
};

getReceiveMsg();

onMounted(async () => {
  // 监听消息事件
  await WS.on('wm.out:scanCode', handleMessage);
});

async function handleLogout() {
  await authStore.logout(false);
}

// function handleNoticeClear() {
//   // notifications.value = [];
// }

function handleMakeAll() {
  notifications.value.forEach((item) => (item.isRead = true));
}

function handleClickLogo() {}

watch(
  () => preferences.app.watermark,
  async (enable) => {
    if (enable) {
      await updateWatermark({
        content: `${userStore.userInfo?.username}`,
      });
    } else {
      destroyWatermark();
    }
  },
  {
    immediate: true,
  },
);
</script>

<template>
  <BasicLayout
    @clear-preferences-and-logout="handleLogout"
    @click-logo="handleClickLogo"
  >
    <template #header-right-2>
      <OrganizationalDropdown
        v-if="isShowOrgList"
        :org-list="userStore.userInfo?.orgList"
        :current-org-name="userStore.userInfo?.currentOrgName"
      />
    </template>
    <template #user-dropdown>
      <UserDropdown
        :avatar
        :menus
        :text="userStore.userInfo?.nickname"
        :description="userStore.userInfo?.currentOrgName"
        :tag-text="userStore.userInfo?.currentUserTypeLabel"
        @logout="handleLogout"
        @avatar-click="handleAvatarClick"
      />
    </template>
    <template #notification>
      <Notification
        :dot="showDot"
        :notifications="notifications"
        :show-clear="false"
        @make-all="handleMakeAll"
      />
    </template>
    <template #extra>
      <AuthenticationLoginExpiredModal
        v-model:open="accessStore.loginExpired"
        :avatar
      >
        <LoginForm />
      </AuthenticationLoginExpiredModal>
    </template>
    <template #lock-screen>
      <LockScreen :avatar @to-login="handleLogout" />
    </template>
  </BasicLayout>

  <FormModal class="w-8/12">
    <FormView :user-id="userStore.userInfo?.userId" />
  </FormModal>
</template>
