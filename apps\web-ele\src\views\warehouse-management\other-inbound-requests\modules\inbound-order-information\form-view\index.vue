<script setup lang="ts">
import type { InBound } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

import { useVbenForm } from '@girant/adapter';
import { ElMessage, ElScrollbar, ElTag } from 'element-plus';

import { getInOutReqDocDetailInBound } from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 其它出入库申请单id */
  inOutReqDocId: {
    type: String,
    default: '',
  },
  /** 其它出入库申请单编号*/
  inOutReqDocNumber: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 当前表单数据 */
const formData = ref<InBound.InOutBoundReqDocDetail>();
/** 调整信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: [],
  showDefaultActions: false,
  wrapperClass: 'grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 ',
});
/** 单据状态 dictKey*/
const docStatusDict: { [key: string]: any } = {
  /** 已完成 */
  finished: {
    name: 'yiwancheng',
    color: '!text-lime-500',
  },
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: '!text-red-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: '!text-gray-300',
  },
};

/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInOutReqDocDetailInBound(
      props.inOutReqDocId,
      props.inOutReqDocNumber,
    );
    formData.value = data;
    // 设置表单schema
    formApi.setState({
      schema: useFormSchema(formData.value),
    });
    // 赋值
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(async () => {
  if (props.inOutReqDocId) {
    await getData();
  }
});
defineExpose({
  formApi,
  Form,
});
</script>
<template>
  <div class="relative" v-loading="loading">
    <IconFont
      v-if="formData?.docStatus && formData.docStatus in docStatusDict"
      :name="docStatusDict[formData.docStatus].name"
      :size="150"
      class="absolute right-20 top-14 z-40"
      :class="docStatusDict[formData.docStatus].color"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>入库单信息</span>
      </template>
      <Form>
        <template #inOutReqDocNumber="row">
          <span>{{ row.value }}</span>
          <ElTag v-if="formData?.isRectify" type="warning" class="ml-1">
            补录
          </ElTag>
        </template>
        <template #materialUserName="row">
          <span>{{ row.value }}</span>
          <span v-if="formData?.materialUserDeptName">
            ({{ formData?.materialUserDeptName }})
          </span>
        </template>
        <template #collectorUserName="row">
          <span>{{ row.value }}</span>
          <span v-if="formData?.collectorUserDeptName">
            ({{ formData?.collectorUserDeptName }})
          </span>
        </template>
        <template #documentProcess>
          <ElScrollbar>
            <StepProgress
              :doc-number="
                inOutReqDocNumber || formData?.inOutReqDocNumber || ''
              "
              class="min-w-[750px] pt-[10px]"
            />
          </ElScrollbar>
        </template>
      </Form>
    </FormCard>
  </div>
</template>
