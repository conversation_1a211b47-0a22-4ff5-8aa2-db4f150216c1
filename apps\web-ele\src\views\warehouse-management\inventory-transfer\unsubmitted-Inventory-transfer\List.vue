<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import {
  ElButton,
  ElCard,
  ElMessage,
  ElMessageBox,
  ElTag,
  ElTooltip,
} from 'element-plus';

import {
  delWareTransferDoc,
  exportWareTransferMyDraftDoc,
  getWareTransferMyDraftDocPage,
} from '#/api';

import EditForm from '../modules/EditForm.vue';
import ViewForm from '../modules/ViewForm.vue';
import { docStatusDict } from '../utils/index';
import { useColumns, useGridFormSchema } from './data';

const props = defineProps({
  params: {
    type: Object,
    default: () => ({}),
  },
  attr: {
    type: Object,
    default: () => ({}),
  },
});

const exportLoading = ref(false);
/** 当行单据内容 */
const docRow = ref('');

onMounted(async () => {
  /** 初始化搜索条件 */
  await gridApi.formApi.setValues({
    transferDocNumberList:
      props.params?.transferDocNumberList?.split(',') || [],
    docCode: props.params?.docCode?.split(',') || '',
    oldWarehouseIdList: props.params?.oldWarehouseIdList?.split(',') || [],
    targetWarehouseIdList:
      props.params?.targetWarehouseIdList?.split(',') || [],
  });
});

/** 模态框组件*/
const [EditModal, editModalApi] = useVbenModal({
  connectedComponent: EditForm,
  destroyOnClose: true,
});
const [ViewModal, viewModalApi] = useVbenModal({
  connectedComponent: ViewForm,
  destroyOnClose: true,
  onConfirm() {
    viewModalApi.close();
    resubmit(docRow.value);
  },
});

/** 重置 */
const handleReset = () => {
  try {
    // 重置表单
    gridApi.formApi.resetForm();

    gridApi.query();
    return Promise.resolve();
  } catch {
    return Promise.reject(new Error('重置失败'));
  }
};

/** 表格*/
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelWidth: 80,
    },
    handleReset,
    schema: useGridFormSchema(),
    showCollapseButton: props.attr?.showCollapseButton || false,
    collapsed: props.attr?.collapsed || true,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    showOverflow: false,
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const res = await getWareTransferMyDraftDocPage({
            pageNum: page.currentPage,
            pageSize: page.pageSize,
            ...formValues,
          });
          return res;
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions,
});

/** 刷新列表 */
const refreshList = () => {
  gridApi.query();
};

/** 新增 */
const onAdd = () => {
  editModalApi
    .setState({ title: '新增' })
    .setData({
      transferDocId: '',
      transferDocNumber: '',
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 导出数据 */
const exportHandle = async () => {
  try {
    const formApi = gridApi.formApi;
    exportLoading.value = true;
    const formValues = await formApi.getValues();
    const response = await exportWareTransferMyDraftDoc({
      ...formValues,
    });
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  } finally {
    exportLoading.value = false;
  }
};

/** 查看 */
const onView = (row: any) => {
  docRow.value = row;
  const confirmText = row.docStatus === 'pending' ? '提交单据' : '再次提交';
  viewModalApi
    .setState({ title: '库存调拨详情', showConfirmButton: true, confirmText })
    .setData({
      transferDocId: row.transferDocId,
      transferDocNumber: row.transferDocNumber,
      processInstanceId: row.processInstanceId,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 再次提交 */
const resubmit = async (row: any) => {
  editModalApi
    .setState({ title: '提交单据' })
    .setData({
      transferDocId: row.transferDocId,
      transferDocNumber: row.transferDocNumber,
      refreshList: () => {
        refreshList();
      },
    })
    .open();
};

/** 删除待提交单据 */
const deleteDoc = async (transferDocId: string) => {
  try {
    await ElMessageBox.confirm('确定删除吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });

    await delWareTransferDoc(transferDocId);
    ElMessage.success('删除成功');
    refreshList();
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('删除失败');
    }
  }
};
</script>

<template>
  <Page auto-content-height>
    <!-- 模态框 -->
    <EditModal class="h-full w-10/12" />
    <ViewModal class="h-full w-10/12" />

    <ElCard class="h-full" body-class="h-full !p-[10px]">
      <Grid>
        <template #toolbar-actions>
          <ElButton
            v-access:code="['wm:ware:transfer:submit']"
            type="primary"
            @click="onAdd"
          >
            新增
          </ElButton>
        </template>
        <template #transferDocNumber="{ row }">
          <span v-if="row.transferDocNumber">{{ row.transferDocNumber }}</span>
          <span v-else>暂无单据编号</span>
        </template>
        <template #docStatusLabel="{ row }">
          <ElTag size="small" :type="docStatusDict[row.docStatus]">
            {{ row.docStatusLabel }}
          </ElTag>
        </template>
        <template #CellOperation="{ row }">
          <div>
            <ElButton link size="small" @click="onView(row)" type="info">
              查看
            </ElButton>
            <ElButton link size="small" @click="resubmit(row)" type="primary">
              {{ row.docStatus === 'pending' ? '提交单据' : '再次提交' }}
            </ElButton>
            <ElButton
              link
              size="small"
              @click="deleteDoc(row.transferDocId)"
              type="danger"
            >
              删除
            </ElButton>
          </div>
        </template>
        <template #toolbar-tools>
          <ElTooltip
            class="box-item"
            effect="light"
            content="导出数据"
            placement="top-start"
          >
            <ElButton :loading="exportLoading" circle @click="exportHandle">
              <template #icon><IconFont name="xiazai" /></template>
            </ElButton>
          </ElTooltip>
        </template>
      </Grid>
    </ElCard>
  </Page>
</template>
