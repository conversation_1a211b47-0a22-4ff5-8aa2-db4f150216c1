<script lang="ts" setup>
import { ref } from 'vue';

const passedParam = ref('');

// 定义组件内的数据
const inputValue = ref('');

// 定义一个方法用于获取组件内的值
const getValue = () => {
  return inputValue.value;
};

const setValue = (value: String) => {
  passedParam.value = value;
};
defineExpose({
  getValue,
  setValue,
});
</script>
<template>
  <div>
    <h2>模态框内容</h2>
    <p>接收到的参数: {{ passedParam }}</p>
    <input v-model="inputValue" placeholder="输入一些内容" />
  </div>
</template>
