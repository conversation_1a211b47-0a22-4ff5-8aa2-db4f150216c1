<script setup lang="ts">
import type { PropType } from 'vue';

import type { materialConfig } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

// @ts-ignore
import { DynamicForm } from '@girant-web/dynamic-table-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import {
  getEnableWarehouseListByMaterial,
  getMaterialInvcConfig,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';

import {
  safetyInventoryWarnOptions,
  slowMovingAnalysisOptions,
  useFormSchema,
} from './data';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 选择的物料配置 */
  materialConfigData: {
    type: Object as PropType<materialConfig.materialPageRecords>,
    default: () => ({}),
  },
});
const key = ref(0);
const loading = ref(false);
/** 安全预警设置ref */
const safetyInventoryWarnRef = ref();
/** 呆滞期设置ref */
const slowMovingAnalysisRef = ref();
/** 安全预警设置回填数据 */
const safetyInventoryWarnData = ref<materialConfig.safetyInventoryWarnType[]>(
  [],
);
/** 呆滞期设置回填数据 */
const slowMovingAnalysisData = ref<materialConfig.slowMovingAnalysisType[]>([]);
/** 库存配置表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(props.materialConfigData, []),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2',
});

/** 获取填写的表单数据 */
const getAllFormValues = async () => {
  try {
    // 获取安全预警设置和呆滞期设置表单数据
    let [safetyInventoryWarnData, slowMovingAnalysisData] = await Promise.all([
      safetyInventoryWarnRef.value?.getAllFormValues(),
      slowMovingAnalysisRef.value?.getAllFormValues(),
    ]);

    safetyInventoryWarnData = await Promise.all(safetyInventoryWarnData);
    slowMovingAnalysisData = await Promise.all(slowMovingAnalysisData);
    // 遍历数组，过滤warehouseId为空的数据
    safetyInventoryWarnData = safetyInventoryWarnData?.filter(
      (item: materialConfig.materialInvcConfig) => item.warehouseId,
    );

    slowMovingAnalysisData = slowMovingAnalysisData?.filter(
      (item: materialConfig.slowMovingAnalysisType) => item.warehouseId,
    );
    // 获取表单数据 默认仓库和默认库位
    const formData = await formApi.getValues();
    return {
      ...formData,
      safetyInventoryWarnList: safetyInventoryWarnData,
      slowMovingAnalysisList: slowMovingAnalysisData,
    };
  } catch {
    ElMessage.error('获取表单数据失败');
    return null;
  }
};

/** 获取数据回填表单 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getMaterialInvcConfig(props.materialId);
    // 获取当前物料的可用仓库列表
    const res = await getEnableWarehouseListByMaterial({
      materialId: props.materialId,
    });
    const warehouseList = res?.warehouseList?.map((item) => ({
      label: item.warehouseName,
      value: item.warehouseId,
    }));
    // 设置表单状态
    formApi.setState({
      schema: useFormSchema(props.materialConfigData, warehouseList),
    });
    safetyInventoryWarnData.value = data?.safetyInventoryWarnList;
    slowMovingAnalysisData.value = data?.slowMovingAnalysisList;
    // 填充表单数据
    formApi.setValues({
      warehouseId: data?.warehouseId,
      locationId: data?.locationId,
    });
    // 填充安全预警设置和呆滞期设置表单数据
    key.value++;
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.materialId) {
    getData();
  }
});
defineExpose({
  formApi,
  Form,
  props,
  getAllFormValues,
});
</script>

<template>
  <FormCard
    title="库存配置"
    :is-footer="false"
    v-loading="loading"
    body-class="!p-[10px]"
  >
    <Form>
      <template #safetyInventoryWarnList>
        <DynamicForm
          :key="key"
          ref="safetyInventoryWarnRef"
          :form-data="safetyInventoryWarnData"
          :form-options="safetyInventoryWarnOptions"
        />
      </template>
      <template #slowMovingAnalysisList>
        <DynamicForm
          :key="key + 10000"
          ref="slowMovingAnalysisRef"
          :form-data="slowMovingAnalysisData"
          :form-options="slowMovingAnalysisOptions"
        />
      </template>
    </Form>
  </FormCard>
</template>
