<script setup lang="ts">
import type { PropType } from 'vue';

import type { InventoryMaterialsByStrategyAPI } from '#/api/warehouse-management';

import { onMounted, ref, watch } from 'vue';

import { getInventoryMaterialsByStrategy } from '#/api/warehouse-management';

interface LocationABatchNumberItem {
  LocationABatchNumber: string;
  locationId: string;
  locationCode: string;
  batchNumber: string;
  inventory: number;
  locationName: string;
  label: string;
  value: string;
}

type Emits = {
  (e: 'update:modelValue', val: string): void;
  (e: 'change', payload: LocationABatchNumberItem): void;
};

defineOptions({
  name: 'LocationABatchNumber',
});

const props = defineProps({
  materialId: {
    type: String,
    default: '',
  },
  modelValue: {
    type: String,
    default: null,
  },
  warehouseId: {
    type: String,
    default: '',
  },
  currentSelLocationABatchNumberList: {
    type: Array as PropType<string[]>,
    default: () => [],
  },
});

const emits = defineEmits<Emits>();

const value = ref(props.modelValue);

watch(
  () => props.modelValue,
  (val) => {
    value.value = val;
  },
  {
    once: true,
  },
);

/** 建议可用量接口的数据 */
const inventoryMaterialsByStrategy =
  ref<InventoryMaterialsByStrategyAPI.InventoryMaterialsByStrategy>();

const getInventoryMaterialsByStrategyData = async (
  materialId: string,
  warehouseId: string,
) => {
  if (materialId && warehouseId) {
    const res = await getInventoryMaterialsByStrategy({
      materialId,
      warehouseId,
    });
    inventoryMaterialsByStrategy.value = res;
    return res;
  }
};

const locationListOptions = ref<LocationABatchNumberItem[]>([]);

// 处理建议可用量接口的数据
const handleInventoryMaterialsByStrategyData = (
  data: InventoryMaterialsByStrategyAPI.InventoryMaterialsItem[],
) => {
  const locationListOptions: LocationABatchNumberItem[] = [];

  data.forEach((item) => {
    const { batchNumber, inventory, locationId, locationName, locationCode } =
      item;

    const LocationABatchNumber = `${locationId}#${batchNumber}`;
    const label = `${locationName} (${batchNumber}) - 库存${inventory}`;
    locationListOptions.push({
      label,
      value: LocationABatchNumber,
      inventory,
      batchNumber,
      LocationABatchNumber,
      locationId,
      locationCode,
      locationName,
    });
  });

  return locationListOptions;
};

watch(
  [() => props.materialId, () => props.warehouseId],
  async ([materialId, warehouseId]) => {
    if (materialId && warehouseId) {
      const res = await getInventoryMaterialsByStrategyData(
        materialId,
        warehouseId,
      );
      if (res?.materialItemList) {
        locationListOptions.value = handleInventoryMaterialsByStrategyData(
          res.materialItemList,
        );
      }
    }
  },
);

onMounted(async () => {
  if (props.materialId && props.warehouseId) {
    const res = await getInventoryMaterialsByStrategyData(
      props.materialId,
      props.warehouseId,
    );
    if (res?.materialItemList) {
      locationListOptions.value = handleInventoryMaterialsByStrategyData(
        res.materialItemList,
      );
      // 只在 modelValue 存在时才触发 change 事件
      if (props.modelValue) {
        const matchedOption = locationListOptions.value.find(
          (item) => item.value === props.modelValue,
        );
        if (matchedOption) {
          emits('change', matchedOption);
        }
      }
    }
  }
});

const handleChange = (val: string) => {
  emits('update:modelValue', val);
  emits(
    'change',
    locationListOptions.value.find((item) => item.value === val) ||
      ({} as LocationABatchNumberItem),
  );
};

export type { LocationABatchNumberItem };
</script>

<template>
  <ElSelect
    v-model="value"
    placeholder="请选择库位-批次号"
    @change="handleChange"
  >
    <ElOption
      v-for="item in locationListOptions"
      :key="item.value"
      :label="item.label"
      :value="item.value"
      :disabled="
        item.inventory === 0 ||
        currentSelLocationABatchNumberList.includes(item.value)
      "
    />
  </ElSelect>
</template>
