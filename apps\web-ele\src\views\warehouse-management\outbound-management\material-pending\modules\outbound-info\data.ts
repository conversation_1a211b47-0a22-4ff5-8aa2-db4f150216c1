import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: 'Input',
      fieldName: 'outBoundDocNumber',
      label: '出库单号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'origDocNumber',
      label: '转入申请单',
    },
    {
      component: 'Input',
      fieldName: 'materialUserName',
      label: '使用人',
    },
    {
      component: 'Input',
      fieldName: 'applyUserName',
      label: '申请人',
    },

    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'applyTime',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'docProcess',
      label: '单据流程',
      formItemClass: 'col-span-full',
    },
  ];
}
