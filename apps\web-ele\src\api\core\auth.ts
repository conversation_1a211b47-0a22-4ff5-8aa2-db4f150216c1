import { baseRequestClient, requestClient } from '#/api/request';

import { authPath, systemPath } from '../path';

export namespace AuthApi {
  /** 登录接口参数 */
  export interface LoginParams {
    password?: string;
    username?: string;
  }

  /** 登录接口返回值 */
  export interface LoginResult {
    token: string;
  }

  export interface RefreshTokenResult {
    data: string;
    status: number;
  }

  /** 获取验证码接口返回值 */
  export interface CaptchaResult {
    captchaImg: string;
    captchaKey: string;
  }
}

/**
 * 登录
 */
export async function loginApi(data: AuthApi.LoginParams) {
  return requestClient.post<AuthApi.LoginResult>(
    `${authPath}/authorize/login`,
    data,
  );
}

/**
 * 刷新token
 */
export async function refreshTokenApi() {
  return baseRequestClient.post<AuthApi.RefreshTokenResult>('/auth/refresh', {
    withCredentials: true,
  });
}

/**
 * 退出登录
 */
export async function logoutApi() {
  return requestClient.get(`${authPath}/authorize/logout`);
}

/**
 * 获取验证码
 */
export async function getCaptchaApi() {
  return requestClient.get<AuthApi.CaptchaResult>(
    `${authPath}/authorize/getCaptcha`,
  );
}

/**
 * 用户修改自己的密码
 */
export async function resetPassword(data: any) {
  return requestClient.post(`${systemPath}/user/mod/mi/mpw`, data);
}
