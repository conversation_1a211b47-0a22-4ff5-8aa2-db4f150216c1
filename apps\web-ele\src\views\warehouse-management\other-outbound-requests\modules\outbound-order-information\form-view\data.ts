import type { VbenFormSchema } from '@girant/adapter';

import type { OutBound } from '#/api/warehouse-management';

import { h } from 'vue';

import { UploadFiles } from '@girant-web/upload-files-component';

/** 出库单信息 */
export function useFormSchema(
  isStaff: boolean,
  formData: OutBound.InOutBoundReqDocDetail,
): VbenFormSchema[] {
  const data = [
    {
      component: 'Input',
      fieldName: 'inOutReqDocNumber',
      label: '单据编号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'docCodeLabel',
      label: '出库类型',
      formItemClass: 'col-span-2',
    },
    {
      component: (props: any) => {
        return h(
          'div',
          null,
          props.modelValue
            ? `${props.modelValue}(${formData?.submitUserDeptName || '未知部门'})`
            : '/',
        );
      },
      fieldName: 'submitUserName',
      label: '申请人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'submitTime',
      label: '提交时间',
    },
    {
      component: (props: any) => {
        return h(
          'div',
          null,
          props.modelValue
            ? `${props.modelValue}(${formData?.materialUserDeptName || '未知部门'})`
            : '/',
        );
      },
      fieldName: 'materialUserName',
      label: '使用人',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'remark',
      label: '备注说明',
      formItemClass: 'col-span-full',
    },
    {
      component: (props: any) => {
        if (!props.modelValue) {
          return h('div', null, '暂无附件');
        }
        return h(UploadFiles, {
          mode: 'readMode',
          serialNumber: props.modelValue,
          tableProps: {
            maxHeight: '300',
          },
        });
      },
      fieldName: 'serialNumber',
      label: '附件',
      formItemClass: 'col-span-full',
    },
    {
      component: 'Input',
      fieldName: 'documentProcess',
      formItemClass: 'col-span-full',
      label: '单据流程',
    },
  ];
  if (formData.docStatus === 'finished') {
    // 找到"使用人"字段的索引
    const materialUserIndex = data.findIndex(
      (item) => item.fieldName === 'materialUserName',
    );

    if (materialUserIndex !== -1) {
      // 在"使用人"之后插入两个新字段
      data.splice(
        materialUserIndex + 1,
        0,
        {
          component: 'Input',
          fieldName: 'collectorUserName',
          label: '领料人',
        },
        {
          component: (props: any) => {
            return h('div', null, props.modelValue || '/');
          },
          fieldName: 'finishTime',
          label: '出库时间',
        },
      );
    }
  }
  if (formData.docStatus === 'awaitOut' && isStaff) {
    // 在 '备注说明'后面插入 '领料码'
    const remarkrIndex = data.findIndex((item) => item.fieldName === 'remark');
    if (remarkrIndex !== -1) {
      data.splice(remarkrIndex + 1, 0, {
        component: 'Input',
        fieldName: 'execCode',
        label: '领料码',
      });
    }
  }
  return data;
}
