# 部门岗位级联选择器

## 说明

本组件用于选择部门，选择部门和岗位。

使用ElCascader组件封装，更多属性请参考

[Cascader 级联选择器 | Element Plus](https://element-plus.org/zh-CN/component/cascader.html#cascaderpanel-api)

使用方法和ElCascader一致

### Props

| 属性 | 类型 | 默认值 | 说明 |
| :-: | :-: | :-: | :-: |
| value | Array<number\| string> \| number \| string | 绑定的值 | 绑定的值，可以是数组、数字或字符串 |
| props | 和ElCascader一致 | - | 级联选择器props属性 |
| isEnable | Boolean \| null | true | 部门是否启用 |
| loadPosition | Boolean | false | 是否加载岗位 |
| showNoPositionDept | Boolean | false | 是否显示无岗位的部门 |
| showDisableTag | Boolean | true | 是否显示部门禁用标签(isEnable的值为null时会搜索全部部门)(当部门被禁用时) |
| disabledWhenDeptDisabled | Boolean | false | 当部门禁用时,是否能选择 |

### 使用方法

```ts
import DeptPositionTree from '#/components/dept-position-tree/Index.vue';
//在表单中
//选择部门
    {
      component: h(DeptPositionTree,{class: 'w-full'}),
      fieldName: 'xxx',
      label: 'xxx',
    },
//只选择岗位 多选，如果不要多选去掉multiple
    {
      component: h(DeptPositionTree, {
        class: 'w-full',
        loadPosition: true,
        showNoPositionDept: false,
        props: {
          multiple: true,
          checkStrictly: false,
        },
      }),
      fieldName: 'parentId',
      label: '上级部门',
    },
        
//选择部门和岗位 多选
//注意选择的岗位的id后缀会带一个'p',在提交时需要对值进行处理。同样，回填数据时也需要处理。不推荐使用
    {
      component: h(DeptPositionTree, {
        class: 'w-full',
        loadPosition: true,
        showNoPositionDept: true,
        props: {
          multiple: true,
        },
      }),
      fieldName: 'xxx',
      label: 'xxx',
    },
        
/** 获取部门和岗位选择的数据 */
const getSelectedDeptAndPosition = async () => {
  // 获取部门/岗位选择的数据
  const formValues: any = await gridApi.formApi.getValues();
  const idList = formValues.deptIdList;
  // 定义部门和岗位选择的数据
  const deptAndPosition = {
    deptIdList: [] as string[],
    positionIdList: [] as string[],
  };
  // 遍历idList
  idList?.forEach((item: string) => {
    // 如果后缀带'p',说明是岗位,否则是部门
    item.endsWith('p')
      ? deptAndPosition.positionIdList.push(item.slice(0, -1))
      : deptAndPosition.deptIdList.push(item);
  });
  return deptAndPosition;
};

//搜索或者下载时
const formValues = await gridApi.formApi.getValues();
// 获取部门和岗位选择的数据
 const deptAndPosition = await getSelectedDeptAndPosition();
//然后赋值
```
