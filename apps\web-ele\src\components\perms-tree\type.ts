/** 字段权限 */
export type fieldListType = {
  [key: string]: any;
  /** 字段权限控制类型 */
  controlType: [];
  /** 字段权限id */
  fieldId: string;
  /** 字段权限标识符 */
  fieldMark: string;
  /** 字段权限名称 */
  fieldName: string;
  /** 字段权限排序号*/
  fieldOrder: number;
};

/** 数据筛选*/
export type conditionListType = {
  [key: string]: any;
  /** 数据权限id */
  conditionId: string;
  /** 数据权限标识符 */
  conditionMark: string;
  /** 数据权限名称 */
  conditionName: string;
  /** 数据权限排序号 */
  conditionOrder: number;
};
/** 按钮权限 */
export type permissionListType = {
  [key: string]: any;
  /**	数据筛选 */
  conditionList: conditionListType[];
  /** 权限字段 */
  fieldList: fieldListType[];
  /** 按钮权限标识符 */
  permissionCode: string;
  /** 按钮权限id */
  permissionId: string;
  /** 按钮权限名称 */
  permissionName: string;
};
/** 获取所有菜单树返回数据类型*/
export type allMenuTreeType = {
  [key: string]: any;
  children?: allMenuTreeType[];
  /** 客户端类型 */
  clientType?: string;
  /** 客户端类型名 */
  clientTypeLabel?: string;
  /** 转换后icon */
  icon?: string;
  /** 转换后id */
  id?: string;
  /** 转换后iconlabel */
  label?: string;
  /** */
  /** 菜单编码 */
  menuCode?: string;
  /** 菜单图标*/
  menuIcon?: string;
  /** 菜单id */
  menuId?: string;
  /** 菜单名称 */
  menuName?: string;
  /** 菜单排序 */
  menuOrder?: number;
  /** 菜单类型  字典：systemMenusType 01 目录 02 菜单 03 外链*/
  menuType?: string;
  /** 菜单类型名 字典：systemMenusType(01 目录 02 菜单 03 外链)*/
  menuTypeLabel?: string;
  /** 菜单打开方式:字典：systemMenuOpenType (01 框架内打开 02新窗口打开) */
  openType?: string;
  /** 菜单打开方式:字典：systemMenuOpenType (01 框架内打开 02新窗口打开) */
  openTypeLabel?: string;
  /** 父菜单id */
  parentId?: string;
  /** 按钮权限列表 */
  permissionList?: permissionListType[];
  /** 	菜单地址 */
  targetUrl?: string;
};
/** 字典返回值类型*/
export type dictType = {
  /** 字典标识 */
  dictCode: string;
  /** 字典子项id */
  dictItemId: string;
  /** 字典键名 */
  dictKey: string;
  /** 字典标签 */
  dictLabel: string;
  /** 排序号 */
  dictSort: number;
  /** 字典值 */
  dictValue: string;
};
/** map */
export type mapType = {
  label: string;
  value: string;
};

/** 客户端类型枚举 */
export type clientTypeEnum = {
  /**	枚举键 */
  enumKey: string;
  /** 枚举标签 */
  enumLabel: string;
  /** 枚举值 */
  enumValue: string;
};
