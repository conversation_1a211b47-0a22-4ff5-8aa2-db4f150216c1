<script lang="ts" setup>
import type { PropType } from 'vue';

import type { MaterialItemData } from './types';

import type { WarehouseListForMaterialListApi } from '#/api/warehouse-management';

import { computed, defineAsyncComponent, markRaw, ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import MaterialInfo from '#/views/warehouse-management/basic-data/material/modules/material-form/index.vue';

// 定义子组件可能暴露的方法的接口
export interface MaterialsItemChildRef {
  getData?: () => Promise<MaterialItemData.WarehouseGroup[]>;
  entryQuantitySum?: number;
  comparisonQuantitySum?: () => {
    isResult: boolean;
    materialCode: string;
    materialId: string;
    materialName: string;
    message: string;
  };
  currentSelectedWarehouseList?: {
    warehouseCode: string;
    warehouseId: string;
    warehouseName: string;
  }[];
}

const props = defineProps({
  // 物料信息（包含仓库、库位信息）
  materialItemData: {
    default: () => ({}),
    type: Object as PropType<MaterialItemData.DocItem>,
  },
  /**
   * 物料item需要显示的状态（编辑:showEdit、预览:showView、关闭:showClose）
   */
  itemStatus: {
    default: 'showClose',
    type: String as PropType<'showClose' | 'showEdit' | 'showView'>,
  },
  warehouseListForMaterial: {
    type: Object as PropType<WarehouseListForMaterialListApi.WarehouseListForMaterialList>,
    default: () => ({}),
  },
  // 源单据类型标识
  origDocTypeCode: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['currentSelectedWarehouseListDataChange']);

const MaterialsItemRef = ref<MaterialsItemChildRef>();

const currentComponent = computed(() => {
  switch (props.itemStatus) {
    // 关闭状态，只展示物料信息
    case 'showClose': {
      return markRaw(
        defineAsyncComponent(() => import('./closed-materialsItem/index.vue')),
      );
    }

    // 编辑状态
    case 'showEdit': {
      return markRaw(
        defineAsyncComponent(() => import('./pending-materialsItem/index.vue')),
      );
    }

    // 预览状态
    case 'showView': {
      return markRaw(
        defineAsyncComponent(() => import('./stocked-materialsItem/index.vue')),
      );
    }

    default: {
      return markRaw(
        defineAsyncComponent(() => import('./closed-materialsItem/index.vue')),
      );
    }
  }
});

/** 获取最后出库的信息，用于点击执行出库的提示弹窗使用 */
const getLastOutboundInfo = () => {
  const { materialName, quantitySum, baseUnitLabel } = props.materialItemData;
  const entryQuantitySum = MaterialsItemRef.value?.entryQuantitySum || 0;
  const lastOutboundInfo = {
    materialName,
    quantitySum,
    baseUnitLabel,
    entryQuantitySum,
    materialId: props.materialItemData.materialId,
  };
  return lastOutboundInfo;
};

const getItemData = async () => {
  if (props.itemStatus === 'showEdit') {
    const comparisonQuantitySum =
      MaterialsItemRef.value?.comparisonQuantitySum?.();
    if (!comparisonQuantitySum?.isResult) {
      throw new Error(comparisonQuantitySum?.message);
    }
  }

  try {
    if (!MaterialsItemRef.value?.getData) {
      return [];
    }

    const itemData = await MaterialsItemRef.value.getData();
    if (!itemData) {
      return [];
    }

    const mergeData = itemData.flatMap(
      (item: MaterialItemData.WarehouseGroup) => {
        return item.locationList.map((location) => {
          return {
            actualQuantity: location.quantity,
            locationId: location.locationId,
            batchNumber: location.batchNumber,
            materialId: props.materialItemData.materialId,
            warehouseId: item.warehouseId,
          };
        });
      },
    );
    return mergeData;
  } catch (error) {
    throw new Error((error as Error).message);
  }
};

const handleCurrentSelectedWarehouseListChange = () => {
  emits('currentSelectedWarehouseListDataChange');
};

const materialId = ref<string>('');
const handleMaterialCode = (id: string) => {
  materialId.value = id; // 物料id
  materialInfoModalApi.open();
};

/** 物料信息查看模式*/
const [MaterialInfoModal, materialInfoModalApi] = useVbenModal({
  showConfirmButton: false,
});

defineExpose({
  MaterialsItemRef,
  getItemData,
  getLastOutboundInfo,
});
</script>

<template>
  <div>
    <component
      :is="currentComponent"
      v-if="
        itemStatus === 'showClose' ||
        itemStatus === 'showView' ||
        !isEmpty(warehouseListForMaterial)
      "
      :material-item-data="materialItemData"
      :warehouse-list-for-material="warehouseListForMaterial"
      :orig-doc-type-code="origDocTypeCode"
      ref="MaterialsItemRef"
      @handle-material-code="handleMaterialCode"
      @current-selected-warehouse-list-change="
        handleCurrentSelectedWarehouseListChange
      "
    />

    <MaterialInfoModal class="w-10/12">
      <MaterialInfo :is-view="true" :material-id="materialId" />
    </MaterialInfoModal>
  </div>
</template>
