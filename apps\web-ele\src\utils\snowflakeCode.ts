/**
 * @class CustomSnowflake
 * @description 生成19位雪花码
 * <AUTHOR>
 * @param {number} workerId 机器ID
 * @param {number} epoch 初始时间戳，可自定义
 * @date [2025-03-12]
 */

export class CustomSnowflake {
  private epoch: number;
  private lastTimestamp: number; // 上次生成ID的时间戳
  private sequence: number; // 序列号
  private workerId: number;

  constructor(workerId: number = 1, epoch: number = 1_609_459_200_000) {
    this.workerId = workerId;
    this.sequence = 0;
    this.lastTimestamp = -1;
    this.epoch = epoch;
  }

  // 生成下一个ID的方法
  nextId(): string {
    let timestamp = Date.now();

    // 如果当前时间小于上次生成ID的时间，抛出异常
    if (timestamp < this.lastTimestamp) {
      throw new Error('时钟向后移动,拒绝生成id');
    }

    // 如果当前时间与上次生成ID的时间相同，则递增序列号
    if (timestamp === this.lastTimestamp) {
      this.sequence = (this.sequence + 1) & 4095; // 12位序列号
      // 如果序列号达到最大值，等待下一毫秒
      if (this.sequence === 0) {
        timestamp = this.waitNextMillis(timestamp);
      }
    } else {
      this.sequence = 0; // 重置序列号
    }

    this.lastTimestamp = timestamp; // 更新上次生成ID的时间戳

    // 生成ID，包括时间戳、机器ID和序列号
    const id = (
      (BigInt(timestamp - this.epoch) << 22n) |
      (BigInt(this.workerId) << 10n) |
      BigInt(this.sequence)
    ).toString();

    return id;
  }

  // 等待下一毫秒的方法
  private waitNextMillis(timestamp: number): number {
    while (timestamp <= this.lastTimestamp) {
      timestamp = Date.now();
    }
    return timestamp;
  }
}

// 创建单例实例
export const snowflakeIdGenerator = new CustomSnowflake();
