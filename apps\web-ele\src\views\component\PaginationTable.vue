<script lang="ts" setup>
import { defineEmits, defineProps, ref, watch } from 'vue';

// 定义组件的 props
const props = defineProps<{
  // 表格列配置
  columns: {
    align?: string;
    label: string;
    prop: string;
    render?: any;
    width?: string;
  }[];
  // 初始页码
  currentPage?: number;
  // 初始每页显示数量
  pageSize?: number;
  // 每页显示数量的选项
  pageSizes?: number[];
  // 查询条件
  queryParams: Record<string, any>;
  // 数据请求函数
  requestApi: (params: any) => Promise<{ data: any[]; total: number }>;
  // 是否显示边框，默认显示
  showBorder?: boolean;
  // 是否显示斑马纹，默认显示
  showStripe?: boolean;
}>();

// 定义组件的 emits
const emits = defineEmits<{
  // 页码改变时触发
  (event: 'pageChange', newPage: number): void;
  // 每页显示数量改变时触发
  (event: 'sizeChange', newSize: number): void;
}>();

// 当前页码
const currentPage = ref(props.currentPage || 1);
// 每页显示的记录数
const pageSize = ref(props.pageSize || 10);
// 每页显示数量的选项
const pageSizes = ref(props.pageSizes || [10, 20, 30]);
// 表格数据
const tableData = ref<any[]>([]);
// 总记录数
const total = ref(0);
// 是否显示边框，取 props 值或默认 true
const showBorder = ref(props.showBorder ?? true);
// 是否显示斑马纹，取 props 值或默认 true
const showStripe = ref(props.showStripe ?? true);

// 封装数据请求方法
const fetchData = async () => {
  const params = {
    ...props.queryParams,
    page: currentPage.value,
    pageSize: pageSize.value,
  };
  try {
    const result = await props.requestApi(params);
    tableData.value = result.data;
    total.value = result.total;
  } catch (error) {
    console.error('数据请求失败:', error);
  }
};

// 监听查询条件和页码、每页数量的变化，触发数据请求
watch([() => props.queryParams, currentPage, pageSize], () => {
  fetchData();
});

// 每页显示数量改变时的回调
const handleSizeChange = (newSize: number) => {
  pageSize.value = newSize;
  emits('sizeChange', newSize);
};

// 当前页码改变时的回调
const handleCurrentChange = (newPage: number) => {
  currentPage.value = newPage;
  emits('pageChange', newPage);
};

// 组件挂载时请求数据
fetchData();
</script>

<template>
  <div>
    <el-table
      :data="tableData"
      :border="showBorder"
      :stripe="showStripe"
      style="border-radius: 10px"
    >
      <el-table-column type="selection" width="55" />
      <template v-for="column in columns" :key="column.prop">
        <el-table-column
          :prop="column.prop"
          :label="column.label"
          :width="column.width"
          :align="column.align"
        >
          <template #default="scope" v-if="column.render">
            <component :is="column.render" :row="scope.row" />
          </template>
        </el-table-column>
      </template>
    </el-table>
    <div class="mt-4 w-full text-center">
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="currentPage"
        :page-sizes="pageSizes"
        :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="total"
        background
        class="w-full"
      />
    </div>
  </div>
</template>

<style scoped>
/* 设置表头背景色 */
.el-table__header th {
  background-color: #c1c1c1;
}
</style>
