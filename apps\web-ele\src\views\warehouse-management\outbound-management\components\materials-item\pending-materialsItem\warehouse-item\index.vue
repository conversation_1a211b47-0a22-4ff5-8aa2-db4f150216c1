<script setup lang="ts">
import type { VbenFormProps } from '@girant/adapter';

import type { PropType } from 'vue';

import type { MaterialItemData } from '../../types';
import type {
  SelectWarehouseListType,
  WarehouseItemDataType,
} from '../index.vue';

import type { InventoryMaterialsByStrategyAPI } from '#/api/warehouse-management/index';

import { computed, h, nextTick, ref, watch } from 'vue';

import { DynamicForm } from '@girant-web/dynamic-table-component';
import { z } from '@girant/adapter';
import { ElOption, ElSelect } from 'element-plus';

import { getInventoryMaterialsByStrategy } from '#/api/warehouse-management/index';
import { add } from '#/utils/numberUtils';

const props = defineProps({
  warehouseItemData: {
    type: Object as PropType<WarehouseItemDataType>,
    default: () => ({}),
  },
  selectWarehouseList: {
    type: Array as PropType<SelectWarehouseListType[]>,
    default: () => [],
  },
  materialId: {
    type: String,
    default: '',
  },
  entryQuantitySum: {
    type: Number,
    default: 0,
  },
  defaultLocationId: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['entryQuantityChange', 'warehouseChange']);

const dynamicFormRef = ref<InstanceType<typeof DynamicForm>>();

// 当前选择的仓库id
const currentWarehouseId = ref(props.warehouseItemData?.warehouseId);

// 当前仓库填入的数量
const currentWarehouseFillQuantity = ref(0);

// 获取表单数据
const getFormData = async (hasWarehouseId: boolean = false) => {
  if (!dynamicFormRef.value) {
    console.warn('dynamicFormRef is not ready');
    return [];
  }

  try {
    const formValuesPromises = await dynamicFormRef.value.getAllFormValues();

    const formData = await Promise.all(formValuesPromises);

    if (hasWarehouseId) {
      const warehouseItemData = props.selectWarehouseList.find(
        (item) => item.value === currentWarehouseId.value,
      );

      const data = {
        warehouseId: currentWarehouseId.value,
        locationList: formData,
        warehouseName: warehouseItemData?.warehouseName,
        warehouseCode: warehouseItemData?.value,
      };

      return data;
    }

    return formData;
  } catch (error) {
    console.error('Error getting form data:', error);
    return [];
  }
};

// 获取当前仓库填入的数量
const getCurrentWarehouseFillQuantity = async () => {
  const formData = await getFormData();
  let fillQuantity = 0;
  for (const item of formData) {
    fillQuantity = add(fillQuantity, item.quantity);
  }
  currentWarehouseFillQuantity.value = fillQuantity;
  emits('entryQuantityChange');
};

// 已选中的库位
const currentSelectedLocationId = ref<Array<string>>([]);
// 库位选择改变
const onLocationChange = (newVal: string, oldVal: string) => {
  // 删除当前库位
  const index = currentSelectedLocationId.value.indexOf(oldVal);
  if (index !== -1) {
    currentSelectedLocationId.value.splice(index, 1);
  }
  // 添加新库位
  currentSelectedLocationId.value.push(newVal);
  getCurrentWarehouseFillQuantity();
};

// 获取已选择的库位
const getSelectedLocationId = async () => {
  const formData = await getFormData();
  const locationIdList = formData.map((item: any) => item.locationAbatchNumber);
  currentSelectedLocationId.value = locationIdList;
};

// 库位选项列表
const locationListOptions = ref<
  {
    batchNumber?: string;
    disabled?: boolean;
    inventory?: number;
    label: string;
    /**  库位批次*/
    locationAbatchNumber: string;
    locationId: string;
    value: string;
  }[]
>([]);

// 初始化时的库位数据
const initLocationList = ref<MaterialItemData.LocationItemType[]>([]);

// 数据是否处理完成
const isLoaded = ref(false);

// 处理建议可用量接口的数据
const handleInvcMaterialsByStrategyData = (
  data: InventoryMaterialsByStrategyAPI.InventoryMaterialsItem[],
) => {
  const locationListOptions: any[] = [];
  const initFormData: any[] = [];

  data.forEach((item) => {
    const {
      batchNumber,
      inventory,
      locationId,
      suggestedQuantity,
      locationName,
      locationCode,
    } = item;

    const locationAbatchNumber = `${locationId}#${batchNumber}`;
    const label = `${locationName} (${batchNumber}) - 库存${inventory}`;
    locationListOptions.push({
      label,
      value: locationAbatchNumber,
      disabled:
        inventory === 0 ||
        computed(() =>
          currentSelectedLocationId.value.includes(locationAbatchNumber),
        ),
      inventory,
      batchNumber,
      locationAbatchNumber,
      locationId,
    });

    if (suggestedQuantity > 0) {
      initFormData.push({
        locationId,
        quantity: suggestedQuantity,
        batchNumber,
        inventory,
        locationAbatchNumber,
        locationName,
        locationCode,
      });
    }
  });

  return {
    locationListOptions,
    initFormData,
  };
};

// 处理有库存的库位数据
const handleLocationList = (data: MaterialItemData.Item[]) => {
  const initFormData: any[] = [];

  data.forEach((item) => {
    const {
      locationId,
      locationName,
      locationCode,
      batchNumber,
      inventory = 0,
      quantity,
    } = item;

    const locationAbatchNumber = `${locationId}#${batchNumber}`;

    initFormData.push({
      locationId,
      quantity,
      batchNumber,
      inventory,
      locationAbatchNumber,
      locationName,
      locationCode,
    });
  });

  return initFormData;
};

// 监听当前选择的仓库id
watch(
  () => currentWarehouseId.value,
  async (newVal) => {
    locationListOptions.value = [];
    initLocationList.value = [];
    currentSelectedLocationId.value = [];

    dynamicFormRef.value?.removeAllForms();

    isLoaded.value = false;

    if (!newVal) {
      isLoaded.value = true;
      return;
    }

    if (newVal) {
      // 库位数据，含可用量，建议用量
      const locationListRes = await getInventoryMaterialsByStrategy({
        materialId: props.materialId,
        warehouseId: newVal,
        inventory:
          props.warehouseItemData?.quantitySum - props.entryQuantitySum,
      });

      const { locationListOptions: locationListOptionsData, initFormData } =
        handleInvcMaterialsByStrategyData(locationListRes.materialItemList);

      initLocationList.value =
        props.warehouseItemData?.locationList.length > 0
          ? handleLocationList(props.warehouseItemData?.locationList)
          : initFormData;

      locationListOptions.value = locationListOptionsData;

      isLoaded.value = true;

      nextTick(() => {
        dynamicFormRef.value?.removeAllForms();
        if (initLocationList.value.length > 0) {
          initLocationList.value.forEach((item) => {
            dynamicFormRef.value?.initForm(item);
          });
        } else {
          dynamicFormRef.value?.initForm([]);
        }
      });

      const debounceTimer: ReturnType<typeof setTimeout> = setTimeout(() => {
        getCurrentWarehouseFillQuantity();
        getSelectedLocationId();
        clearTimeout(debounceTimer);
      }, 100);
    }
  },
  { immediate: true },
);

// 数量改变
const onQuantityChange = async () => {
  // 防抖
  const debounceTimer: ReturnType<typeof setTimeout> = setTimeout(() => {
    getCurrentWarehouseFillQuantity();
    clearTimeout(debounceTimer);
  }, 100);
};

// 动态表单配置
const formOptions = ref<VbenFormProps>({
  schema: [
    {
      component: (props: any) => {
        return h(
          ElSelect,
          {
            modelValue: props.modelValue,
            placeholder: '请选择库位批次',
            disabled: !currentWarehouseId.value,
            onChange: (val: string) => {
              onLocationChange(val, props.modelValue);
            },
          },
          {
            // 使用默认插槽渲染选项
            default: () =>
              locationListOptions.value.map((item) =>
                h(ElOption, {
                  key: item.locationAbatchNumber,
                  label: item.label,
                  value: item.locationAbatchNumber,
                  disabled: item.disabled,
                }),
              ),
          },
        );
      },
      componentProps: (values, form) => {
        return {
          onChange: () => {
            const [newLocationId, newBatchNumber] =
              values.locationAbatchNumber.split('#');
            form.setFieldValue('locationId', newLocationId);
            form.setFieldValue('batchNumber', newBatchNumber);
            form.setFieldValue('quantity', 0);
          },
        };
      },
      fieldName: 'locationAbatchNumber',
      label: '库位批次',
      rules: z.string().min(1, '请选择库位批次'),
      labelWidth: 100,
      formItemClass: 'col-span-2',
    },
    {
      component: 'Input',
      fieldName: 'locationId',
      label: '库位ID',
      formItemClass: 'hidden',
      labelWidth: 50,
    },
    {
      component: 'Input',
      fieldName: 'batchNumber',
      formItemClass: 'hidden',
      label: '批次号',
      labelWidth: 60,
    },
    {
      component: 'InputNumber',
      fieldName: 'quantity',
      label: '数量',
      componentProps: {
        min: 0,
        precision: 3,
        disabled: computed(() => !currentWarehouseId.value),
        onChange: onQuantityChange,
      },
      dependencies: {
        triggerFields: ['locationAbatchNumber'],
        componentProps: (values) => {
          const inventory =
            locationListOptions.value.find(
              (item) => item.value === values.locationAbatchNumber,
            )?.inventory || 0;
          return {
            max: inventory,
            disabled: !values.locationAbatchNumber,
          };
        },
      },
      defaultValue: 0,
      labelWidth: 60,
    },
    {
      component: 'Input',
      fieldName: 'locationName',
      label: '库位名称',
      formItemClass: 'hidden',
    },
    {
      component: 'Input',
      fieldName: 'locationCode',
      label: '库位编号',
      formItemClass: 'hidden',
    },
  ],
  wrapperClass: 'grid-cols-3 ',
});

// 删除一个表单
const removeForm = () => {
  nextTick(() => {
    getSelectedLocationId();
    getCurrentWarehouseFillQuantity();
  });
};

// 校验表单数据
const validateFormData = async () => {
  const validateFormData = await dynamicFormRef.value?.validateAllForms(false);
  return validateFormData;
};

const handleWarehouseChange = () => {
  currentWarehouseFillQuantity.value = 0;
  emits('warehouseChange');
  emits('entryQuantityChange');
};

defineExpose({
  getFormData,
  validateFormData,
  currentWarehouseFillQuantity,
  currentWarehouseId,
});
</script>

<template>
  <div class="bg-primary-50/80 mb-3 rounded-lg px-2 shadow-sm">
    <div class="flex">
      <div
        class="border-primary-200/80 relative flex w-1/4 flex-col justify-center border-r-2 border-dashed pr-2"
      >
        <div class="space-y-3">
          <div class="flex items-center">
            <div class="text-sm text-gray-700">仓库：</div>
            <ElSelect
              v-model="currentWarehouseId"
              class="!w-full flex-1"
              placeholder="请选择仓库"
              @change="handleWarehouseChange"
            >
              <template v-for="item in selectWarehouseList" :key="item.value">
                <ElOption
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                />
              </template>
            </ElSelect>

            <slot name="delete-wrapper"></slot>
          </div>
        </div>
      </div>
      <div class="flex flex-1 flex-col" v-loading="!isLoaded">
        <DynamicForm
          v-if="isLoaded"
          ref="dynamicFormRef"
          class="flex-1 pl-4"
          :form-options="formOptions"
          @remove-form="removeForm"
        />
      </div>
    </div>
  </div>
</template>
