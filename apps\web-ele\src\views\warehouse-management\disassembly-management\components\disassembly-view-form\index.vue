<script setup lang="ts">
import type { DisassemblyApi } from '#/api';
import type { StaffInfoType } from '#/api/common/staff';

import { defineAsyncComponent, onMounted, ref } from 'vue';

import { useVbenForm, useVbenModal } from '@vben/common-ui';

import { useClipboard } from '@vueuse/core';
import { ElButton, ElLink, ElMessage, ElScrollbar, ElTag } from 'element-plus';

import {
  getAssemblyDocDetail,
  getExecCode,
  getInBoundDocDetailByOrigDoc,
  getOutBoundDocDetailByOrigDoc,
} from '#/api';
import { getStaffInfo } from '#/api/common/staff';
import FormCard from '#/components/form-card/Index.vue';
import StepProgress from '#/components/step-progress/Index.vue';
import ProductViewPopover from '#/views/warehouse-management/disassembly-management/components/disassembly-view-form/components/ProductViewPopover.vue';
import { docStatusDict } from '#/views/warehouse-management/disassembly-management/utils/index';

import { useFormSchema } from './data';

const props = defineProps({
  /** 组装拆卸单据ID */
  assemblyDocId: {
    type: String,
    default: '',
  },
  /** 组装拆卸单据编号*/
  assemblyDocNumber: {
    type: String,
    default: '',
  },
});

const loading = ref(false);
/** 当前登录用户的员工信息 */
const staffData = ref<StaffInfoType>();
/** 拆卸单数据 */
const data = ref<DisassemblyApi.AssemblyDocDetail>();
/** 领料码 */
const execCode = ref<number>();
/** 出库单数据 */
const outBoundDoc = ref();
/** 入库单数据 */
const inBoundDoc = ref();
/** 出库取消单数据 */
const outCancelDoc = ref();
/** 入库取消单数据 */
const inCancelDoc = ref();
/** 当前单据状态 */
const docStatus = ref('');

/** 拆卸单据状态图片 dictKey*/
const docStatusIconDict: { [key: string]: any } = {
  /** 已完成 */
  finish: {
    name: 'yiwancheng',
    color: 'text-lime-500',
  },
  /** 审核驳回 */
  reject: {
    name: 'shenhebutongguo',
    color: 'text-red-500',
  },
  /** 已关闭 */
  closed: {
    name: 'yiguanbi',
    color: 'text-slate-500 !text-gray-300',
  },
};

/** 出入库单据状态Tag类型*/
const boundDocStatusDict: { [key: string]: any } = {
  /** 待出库\待入库 */
  pending: 'warning',
  /** 取消审核中 */
  cancelAudit: 'warning',
  /** 已出库 */
  collected: 'success',
  /** 已入库 */
  stocked: 'success',
  /** 已关闭 */
  closed: 'info',
};

/** 出入库取消单据状态Tag类型*/
const inOutCancelDocStatusDict: { [key: string]: any } = {
  /** 待提交 */
  pending: 'warning',
  /** 待审核 */
  checking: 'primary',
  /** 审核通过 */
  passed: 'success',
  /** 审核驳回 */
  reject: 'danger',
};

onMounted(() => {
  if (props.assemblyDocId || props.assemblyDocNumber) {
    getData();
  }
});

/**  获取数据*/
const getData = async () => {
  try {
    loading.value = true;
    staffData.value = await getStaffInfo();
    const res = await getAssemblyDocDetail(
      props.assemblyDocId,
      props.assemblyDocNumber,
    );
    data.value = res;
    docStatus.value = res?.docStatus;
    formApi.setState({
      schema: useFormSchema(
        docStatus.value,
        staffData.value.staffId === data.value.executorUser,
        data.value.isAutoIo,
      ),
    });
    // 赋值
    formApi.setValues(res);
    // 获取出库单
    if (
      docStatus.value !== 'pending' &&
      docStatus.value !== 'checking' &&
      docStatus.value !== 'reject'
    ) {
      getOutBoundDoc();
    }
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};

/** 获取出库单数据 */
const getOutBoundDoc = async () => {
  const res = await getOutBoundDocDetailByOrigDoc(
    data.value?.assemblyDocId || '',
    data?.value?.assemblyDocNumber,
  );

  if (!res) {
    return;
  }

  outBoundDoc.value = res;
  formApi.setFieldValue('outBoundDocNumber', res.outBoundDocNumber);

  // 已完成，查询入口单据
  if (res.docStatus === 'collected') {
    getInBoundDoc();
  }
  // 查询是否有取消单
  if (res.outCancelDocList) {
    outCancelDoc.value = res.outCancelDocList[0];
    formApi.setFieldValue(
      'inOutCancelDocNumber',
      outCancelDoc.value.outCancelDocNumber,
    );
  }
};

/** 获取入库单数据 */
const getInBoundDoc = async () => {
  const res = await getInBoundDocDetailByOrigDoc(
    data.value?.assemblyDocId || '',
    data?.value?.assemblyDocNumber,
  );

  if (!res) {
    return;
  }

  inBoundDoc.value = res;
  formApi.setFieldValue('inBoundDocNumber', res.inBoundDocNumber);

  // 查询是否有取消单
  if (res.inCancelDocList) {
    inCancelDoc.value = res.inCancelDocList[0];
    formApi.setFieldValue(
      'inOutCancelDocNumber',
      inCancelDoc.value.inCancelDocNumber,
    );
  }
};

/** 拆卸信息表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: [],
  showDefaultActions: false,
  wrapperClass:
    'grid-cols-2 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-3',
});

/** 获取领料码 */
const getExecCodeData = async () => {
  try {
    execCode.value = await getExecCode(undefined, props.assemblyDocId);
  } catch {
    ElMessage.error('获取领料码失败');
  }
};

/** 复制领料码 */
const { copy } = useClipboard({
  legacy: true,
  source: execCode.value?.toString,
});

/** 模态框组件*/
const [MaterialModal, materialModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(
    () => import('./modules/MaterialModal.vue'),
  ),
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
  title: '拆卸成品详情',
});
const [OutBoundModal, outBoundModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(
    () => import('./modules/OutBoundModal.vue'),
  ),
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
  title: '出库单详情',
});
const [InBoundModal, inBoundModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(
    () => import('./modules/InBoundModal.vue'),
  ),
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
  title: '入库单详情',
});
const [OutCancelModal, outCancelModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(
    () => import('./modules/OutCancelModal.vue'),
  ),
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
  title: '取消单详情',
});
const [InCancelModal, inCancelModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(
    () => import('./modules/InCancelModal.vue'),
  ),
  showCancelButton: true,
  showConfirmButton: false,
  destroyOnClose: true,
  title: '取消单详情',
});

/** 显示物料详情信息 */
const showProductMaterial = () => {
  materialModalApi
    .setData({
      materialId: data.value?.productMaterialId,
      materialCode: data.value?.productMaterialCode,
    })
    .open();
};

/** 显示出库单详情信息 */
const showOutBoundDoc = () => {
  outBoundModalApi
    .setData({
      docStatus: outBoundDoc.value.docStatus,
      outBoundDocId: outBoundDoc.value.outBoundDocId,
      outBoundDocNumber: outBoundDoc.value.outBoundDocNumber,
    })
    .open();
};

/** 显示入库单详情信息 */
const showInBoundDoc = () => {
  inBoundModalApi
    .setData({
      inBoundDocId: inBoundDoc.value.inBoundDocId,
      inBoundDocNumber: inBoundDoc.value.inBoundDocNumber,
    })
    .open();
};

/** 显示取消单详情信息 */
const showInOutCancelDoc = () => {
  if (outCancelDoc.value) {
    outCancelModalApi
      .setData({
        inOutCancelDocId: outCancelDoc.value.outCancelDocId,
        inOutCancelDocNumber: outCancelDoc.value.outCancelDocNumber,
      })
      .open();
  } else {
    inCancelModalApi
      .setData({
        inOutCancelDocId: inCancelDoc.value.inCancelDocId,
        inOutCancelDocNumber: inCancelDoc.value.inCancelDocNumber,
      })
      .open();
  }
};
</script>

<template>
  <div class="relative" v-loading="loading">
    <!-- 模态框 -->
    <MaterialModal class="h-full w-10/12" />
    <OutBoundModal class="h-full w-10/12" />
    <InBoundModal class="h-full w-10/12" />
    <OutCancelModal class="h-full w-10/12" />
    <InCancelModal class="h-full w-10/12" />

    <IconFont
      v-if="
        docStatus === 'finish' ||
        docStatus === 'closed' ||
        docStatus === 'reject'
      "
      :name="docStatusIconDict[docStatus].name"
      :size="150"
      color="dark:bg-gray-800"
      class="absolute right-20 top-14 z-40"
      :class="docStatusIconDict[docStatus].color"
    />
    <FormCard :is-footer="false">
      <template #title>
        <span>拆卸单信息</span>
      </template>
      <Form>
        <template #productMaterialName>
          <ElLink type="primary" @click="showProductMaterial">
            <span>{{ data?.productMaterialName }}</span>
            <span v-if="data?.productMaterialCode">
              （{{ data.productMaterialCode }}）
            </span>
          </ElLink>
        </template>
        <template #quantity="slotProps">
          <span>{{ slotProps.modelValue ?? '/' }}</span>
          <span class="ml-2">
            <ProductViewPopover :assembly-doc-detail="data" />
          </span>
        </template>
        <template #execCode>
          <div v-if="execCode" class="flex">
            <span>
              <b>{{ execCode }}</b>
            </span>
            <ElButton
              link
              type="primary"
              @click="
                copy(execCode.toString()).then(() => {
                  ElMessage.success('复制成功');
                })
              "
              class="ml-2"
            >
              点击复制
            </ElButton>
          </div>
          <ElButton v-else link type="primary" @click="getExecCodeData">
            点击查看
          </ElButton>
        </template>
        <template #outBoundDocNumber="slotProps">
          <ElLink type="primary" @click="showOutBoundDoc">
            <span>{{ slotProps.modelValue ?? '/' }}</span>
          </ElLink>
          <ElTag
            v-if="outBoundDoc"
            class="ml-2"
            size="small"
            :type="boundDocStatusDict[outBoundDoc.docStatus]"
          >
            {{ outBoundDoc.docStatusLabel }}
          </ElTag>
        </template>
        <template #inBoundDocNumber="slotProps">
          <ElLink type="primary" @click="showInBoundDoc">
            <span>{{ slotProps.modelValue ?? '/' }}</span>
          </ElLink>
          <ElTag
            v-if="inBoundDoc"
            class="ml-2"
            size="small"
            :type="boundDocStatusDict[inBoundDoc.docStatus]"
          >
            {{ inBoundDoc.docStatusLabel }}
          </ElTag>
        </template>
        <template #inOutCancelDocNumber="slotProps">
          <ElLink type="primary" @click="showInOutCancelDoc">
            <span>{{ slotProps.modelValue ?? '/' }}</span>
          </ElLink>
          <div v-if="inCancelDoc">
            <ElTag
              class="ml-2"
              size="small"
              :type="inOutCancelDocStatusDict[inCancelDoc.docStatus]"
            >
              {{ inCancelDoc.docStatusLabel }}
            </ElTag>
          </div>
          <div v-if="outCancelDoc">
            <ElTag
              class="ml-2"
              size="small"
              :type="inOutCancelDocStatusDict[outCancelDoc.docStatus]"
            >
              {{ outCancelDoc.docStatusLabel }}
            </ElTag>
          </div>
        </template>
        <template #docStatusLabel="slotProps">
          <ElTag size="small" :type="docStatusDict[docStatus]">
            {{ slotProps.modelValue ?? '/' }}
          </ElTag>
        </template>
        <template #documentProcess>
          <ElScrollbar>
            <StepProgress
              :doc-number="
                props.assemblyDocNumber || data?.assemblyDocNumber || ''
              "
              class="min-w-[750px] pt-[10px]"
            />
          </ElScrollbar>
        </template>
      </Form>
    </FormCard>
  </div>
</template>
