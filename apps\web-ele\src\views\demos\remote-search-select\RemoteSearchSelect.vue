<script setup lang="ts">
import { ref } from 'vue';

import { getUserPage } from '#/api';
import RemoteSearchSelect from '#/components/remote-search-select/Index.vue';

// 定义用户对象的类型
interface User {
  userId: string;
  nickname: string;
}

// 用户搜索示例
// 指定 selectedUserData 为 User 类型的数组、null 或者单个 User 对象
const selectedUserData = ref<null | User | User[]>(null);
const selectedUser = ref<null | User>(null);

const fetchUsers = async ({
  keyword,
  pageNum,
  pageSize,
}: {
  keyword: string;
  pageNum: number;
  pageSize: number;
}) => {
  return await getUserPage({ nickname: keyword, pageNum, pageSize });
};

const handleUserChange = (value: number, item: User) => {
  selectedUser.value = item;
  console.error('用户选择变化:', value, item);
};

// 这里假设要初始化 selectedUserData 为一个用户对象
selectedUserData.value = { userId: '1913917294142132225', nickname: '测s' };
</script>

<template>
  <RemoteSearchSelect
    :model-value="selectedUserData"
    :fetch-method="fetchUsers"
    placeholder="请输入用户名搜索"
    value-key="userId"
    label-key="nickname"
    :multiple="true"
    @change="handleUserChange"
  />
</template>
