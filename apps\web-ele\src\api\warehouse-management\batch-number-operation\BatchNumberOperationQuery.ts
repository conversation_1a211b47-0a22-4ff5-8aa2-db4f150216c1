import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace BatchNumberOperationQueryApi {
  export interface BatchNumberOperationQueryPageParams {
    batchnumDocIdList: string;
    batchnumDocNumberList: string;
    docCode: string;
    submitUserList: string;
    closeUserList: string;
    executorUserList: string;
    materialIdList: string;
    materialCodeList: string;
    materialName: string;
    warehouseIdList: string;
    locationIdList: string;
    batchNumber: string;
    submitStartTime: string;
    submitEndTime: string;
    closeStartTime: string;
    closeEndTime: string;
    finishStartTime: string;
    finishEndTime: string;
    docStatusList: string;
    pageNum: number;
    pageSize: number;
  }

  export interface BatchNumberOperationQueryRecord {
    /* 批次号处理单据ID */
    batchnumDocId: string;

    /* 批次号合并单据编号 */
    batchnumDocNumber: string;

    /* 批次号处理类型值，枚举WmBatchNumHandleTypeEnums */
    docCode: string;

    /* 批次号处理类型标签，枚举WmBatchNumHandleTypeEnums */
    docCodeLabel: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 关闭人ID */
    closeUser: string;

    /* 关闭人姓名 */
    closeUserName: string;

    /* 执行人ID */
    executorUser: string;

    /* 执行人姓名 */
    executorUserName: string;

    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料别名 */
    materialAlias: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 规格型号 */
    materialSpecs: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 所属仓库ID */
    warehouseId: string;

    /* 所属仓库编号 */
    warehouseCode: string;

    /* 所属仓库名称 */
    warehouseName: string;

    /* 所属库位ID */
    locationId: string;

    /* 所属库位编号 */
    locationCode: string;

    /* 所属库位名称 */
    locationName: string;

    /* 合并后的批次号 */
    mergeBatchNumber: string;

    /* 合并拆分批次数量 */
    mergeQuantity: number;

    /* 合并拆分批次均价（单价） */
    mergeUnitPrice: number;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: string;

    /* 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: string;

    /* 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: string;

    /* 单据审核状态 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例ID */
    processInstanceId: string;

    /* 单据状态值，字典publicDocStatus */
    docStatus: string;

    /* 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;
  }

  export interface GetBatchnumDocDetailParams {
    batchnumDocId?: string;
    batchnumDocNumber?: string;
    isQueryItem?: boolean;
  }

  export interface BatchnumItem {
    /* 批次号处理单据ID */
    batchnumDocId: string;

    /* 批次号子项ID */
    batchnumItemId: string;

    /* 子项批次号 */
    itemBatchNumber: string;

    /* 子项数量 */
    itemQuantity: number;

    /* 子项均价 */
    itemUnitPrice: number;

    /* 所属库位编号 */
    locationCode: string;

    /* 所属库位ID */
    locationId: string;

    /* 所属库位名称 */
    locationName: string;
  }

  export interface GetBatchnumDocDetailResponse {
    /* 批次号拆分单据ID */
    batchnumDocId: string;

    /* 批次号处理单据编号 */
    batchnumDocNumber: string;

    /* 批次号处理类型值，枚举WmBatchNumHandleTypeEnums */
    docCode: string;

    /* 批次号处理类型标签，枚举WmBatchNumHandleTypeEnums */
    docCodeLabel: string;

    /* 创建人ID */
    createUser: string;

    /* 创建人姓名 */
    createUserName: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 关闭人ID */
    closeUser: string;

    /* 关闭人姓名 */
    closeUserName: string;

    /* 执行人ID */
    executorUser: string;

    /* 执行人姓名 */
    executorUserName: string;

    /* 是否提交 */
    isSubmit: boolean;

    /* 创建时间，时间格式：yyyy-MM-dd HH:mm */
    createTime: Record<string, unknown>;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: Record<string, unknown>;

    /* 关闭时间，时间格式：yyyy-MM-dd HH:mm */
    closeTime: Record<string, unknown>;

    /* 完成时间，时间格式：yyyy-MM-dd HH:mm */
    finishTime: Record<string, unknown>;

    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料别名 */
    materialAlias: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 规格型号 */
    materialSpecs: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 所属仓库ID */
    warehouseId: string;

    /* 所属仓库编号 */
    warehouseCode: string;

    /* 所属仓库名称 */
    warehouseName: string;

    /* 所属库位ID */
    locationId: string;

    /* 所属库位编号 */
    locationCode: string;

    /* 所属库位名称 */
    locationName: string;

    /* 合并拆分批次号 */
    mergeBatchNumber: string;

    /* 合并拆分批次数量 */
    mergeQuantity: number;

    /* 合并拆分批次均价 */
    mergeUnitPrice: number;

    /* 审核状态，写入当前流程所在节点 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例ID */
    processInstanceId: string;

    /* 单据状态，字典publicDocStatus */
    docStatus: string;

    /* 单据状态标签，字典publicDocStatus */
    docStatusLabel: string;

    /* 备注(原因) */
    remark: string;

    /* 附件流水号 */
    serialNumber: number;

    /* 批次号子项列表 */
    batchnumItemList: BatchnumItem[];

    /* 取消原因选项列表 */
    remarkOptionList: {
      /* 选项ID */
      optionId: string;
      /* 选项名称 */
      optionName: string;
    }[];
  }
}

/**
 * 查询批次号处理单据分页列表
 */
export async function getBatchnumDocPage(
  params: BatchNumberOperationQueryApi.BatchNumberOperationQueryPageParams,
) {
  return requestClient.post<
    Array<BatchNumberOperationQueryApi.BatchNumberOperationQueryRecord>
  >(`${warehousePath}/wm/batchnum/getBatchnumDocPage`, { ...params });
}

/** 获取批次号处理单据详细信息 */
export async function getBatchnumDocDetail(
  params: BatchNumberOperationQueryApi.GetBatchnumDocDetailParams,
) {
  return requestClient.get<BatchNumberOperationQueryApi.GetBatchnumDocDetailResponse>(
    `${warehousePath}/wm/batchnum/getBatchnumDocDetail`,
    { params },
  );
}

/** 导出批次号处理单据列表*/
export async function exportBatchnumDocPage(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/batchnum/exportBatchnumDocPage`,
    {
      ...params,
    },
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
