<script></script>
<script setup lang="ts">
import type { OnActionClickParams, VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import {
  delTransferDoc,
  getMyTransferDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      modifyEndTime?: string;
      modifyStartTime?: string;
      transferDocNumberList?: string;
      warehouseIdList?: string;
    }>,
    default: () => ({}),
  },
});

const transferDocId = ref<string>('');
const transferDocNumber = ref<string>('');
const docStatus = ref<string>('');

const modifyTime = ref({
  // 开始时间
  modifyStartTime: props.params?.modifyStartTime || '',
  // 结束时间
  modifyEndTime: props.params?.modifyEndTime || '',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      modifyTime.value = {
        modifyStartTime: '',
        modifyEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? false : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? false
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(onActionClick),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.modifyStartTime = modifyTime.value.modifyStartTime;
          params.modifyEndTime = modifyTime.value.modifyEndTime;

          if (params.warehouseIdList) {
            params.warehouseIdList = params.warehouseIdList.join(',');
          }

          return await getMyTransferDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'transferDocId',
    },
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    transferDocNumberList:
      props.params?.transferDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    modifyStartTime: props.params?.modifyStartTime || '',
    modifyEndTime: props.params?.modifyEndTime || '',
  });
});

const [FormModal, formModalApi] = useVbenModal({
  confirmText: '新增调拨',
  destroyOnClose: true,
  onBeforeClose: () => {
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

function onTransferLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onTransferSuccess() {
  formModalApi.close();
  gridApi.query();
}

const onView = (row: RowType) => {
  transferDocId.value = row.transferDocId;
  transferDocNumber.value = row.transferDocNumber;
  docStatus.value = row.docStatus;
  formModalApi
    .setState({
      title: '调拨详情',
    })
    .open();
};

const onDelete = async (row: RowType) => {
  await ElMessageBox.confirm('确定删除单据吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        await delTransferDoc(row.transferDocId);
        ElMessage.success('删除成功');
        gridApi.query();
      } catch {
        ElMessage.error('删除失败');
      }
    })
    .catch(() => {});
};

function onActionClick(e: OnActionClickParams<RowType>) {
  switch (e.code) {
    case 'del': {
      onDelete(e.row);
      break;
    }
    case 'view': {
      onView(e.row);
      break;
    }
  }
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        :transfer-doc-id="transferDocId"
        :transfer-doc-number="transferDocNumber"
        :doc-status="docStatus"
        @handle-cancel="formModalApi.close()"
        @save-success="onTransferSuccess"
        @submit-success="onTransferSuccess"
        @delete-success="onTransferSuccess"
        @exec-success="onTransferSuccess"
        @transfer-loading="onTransferLoading"
      />
    </FormModal>
    <Grid>
      <template #form-modifyTime>
        <ElDatePicker
          v-model="modifyTime.modifyStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, modifyTime.modifyEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="modifyTime.modifyEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                modifyTime.modifyStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>
    </Grid>
  </Page>
</template>
