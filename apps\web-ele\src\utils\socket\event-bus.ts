// socket/event-bus.ts（补充类型定义）
type EventCallback = (...args: any[]) => void;

interface EventBus {
  on: (eventName: string, callback: EventCallback) => void;
  off: (eventName: string, callback: EventCallback) => void;
  emit: (eventName: string, ...args: any[]) => void;
  // 提供获取剩余回调数的方法（替代直接访问 events）
  getListenerCount: (eventName: string) => number;
}

const eventBus: EventBus = (() => {
  const events: Record<string, EventCallback[]> = {};

  return {
    on(eventName, callback) {
      if (!events[eventName]) {
        events[eventName] = [];
      }
      events[eventName].push(callback);
    },
    off(eventName, callback) {
      if (!events[eventName]) return;
      events[eventName] = events[eventName].filter((cb) => cb !== callback);
    },
    emit(eventName, ...args) {
      if (!events[eventName]) return;
      events[eventName].forEach((callback) => callback(...args));
    },
    // 新增：获取事件的回调数量
    getListenerCount(eventName) {
      return events[eventName]?.length || 0;
    },
  };
})();

export default eventBus;
