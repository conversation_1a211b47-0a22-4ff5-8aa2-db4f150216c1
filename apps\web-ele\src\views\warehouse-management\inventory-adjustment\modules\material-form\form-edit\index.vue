<script setup lang="ts">
import type { OnActionClickParams } from '@girant/adapter';

import type { materialConfig } from '#/api/warehouse-management';

import { onMounted, ref } from 'vue';

// @ts-ignore
import { DynamicTable } from '@girant-web/dynamic-table-component';
import { ElButton, ElInputNumber, ElMessage, ElSelectV2 } from 'element-plus';

import {
  generateBatchNumber,
  getEnableWarehouseListByMaterial,
  getInvcAdjustDoc,
  getInventoryList,
  getLocationList,
  getMaterialConfig,
  getWarehouseList,
} from '#/api/warehouse-management';
import FormCard from '#/components/form-card/Index.vue';
import IconFont from '#/components/IconFont/IconFont.vue';

import { useGridOptions } from './data';

const props = defineProps({
  /** 库存调整单据id */
  invcAdjustDocId: {
    type: String,
    default: '',
  },
  /** 库存调整单据编号 */
  invcAdjustDocNumber: {
    type: String,
    default: '',
  },
});
const loading = ref(false);
/** 仓库加载 */
const loadingWarehouse = ref(false);
/** 库位加载 */
const loadingLocation = ref(false);
/** 批次号加载 */
const loadingBatchNumber = ref(false);

/** 表格ref */
const gridTable = ref<DynamicTable>();

interface ListItem {
  data?: any;
  value: string;
  label: string;
}
const locationIdRef = ref();
/** 仓库下拉列表 */
const options = ref<ListItem[]>([]);
/** 库位下拉列表 */
const locationOptions = ref<ListItem[]>([]);
/** 批次号下拉列表 */
const batchNumberOptions = ref<ListItem[]>([]);
/** 物料配置信息 */
const materialConfigInfo = ref<materialConfig.materialConfig>();
/** 仓库的提示 */
const warehouseTip = ref('请选择');
/** 库位的提示 */
const locationTip = ref('请选择');
/** 操作 */
function onActionClick(e: OnActionClickParams) {
  switch (e.code) {
    case 'delete': {
      gridTable.value.removeRow(e.row);
      break;
    }
  }
}
/** 获取仓库 */
const fetchWarehouse = async (params: any) => {
  try {
    options.value = [];
    loadingWarehouse.value = true;
    params = {
      materialId: params.materialId,
      docTypeCode: 'WM0090',
    };
    const result = await getEnableWarehouseListByMaterial(params);
    // 转换数据格式
    options.value =
      result.warehouseList?.map((item) => ({
        value: item?.warehouseId,
        label: item?.warehouseName,
      })) || [];
  } catch {
    ElMessage.error('获取仓库失败');
  } finally {
    loadingWarehouse.value = false;
  }
};

/** 获取库位 */
const fetchLocation = async (row: any) => {
  try {
    locationOptions.value = [];
    if (!row.warehouseId) {
      return;
    }
    loadingLocation.value = true;
    const result = await getLocationList({
      warehouseId: row.warehouseId,
      isLock: false,
      isEnable: true,
    });
    // 转换数据格式
    locationOptions.value =
      result?.map((item) => ({
        value: item.locationId,
        label: item.locationName,
      })) || [];
  } catch (error) {
    console.error(error);
    ElMessage.error('获取库位失败');
  } finally {
    loadingLocation.value = false;
  }
};
/** 获取批次号列表 */
const fetchInventoryList = async (row: any) => {
  try {
    if (batchNumberOptions.value.length > 0) {
      // 清空批次号下拉列表
      batchNumberOptions.value = [];
    }
    if (!row.locationId) {
      return;
    }
    loadingBatchNumber.value = true;
    const result = await getInventoryList({
      warehouseIdList: row.warehouseId,
      locationIdList: row.locationId,
      materialIdList: row.materialId?.materialId,
    });
    batchNumberOptions.value =
      result.map((item) => ({
        value: item.batchNumber,
        label: item.batchNumber,
        data: item,
      })) || [];
  } catch (error) {
    console.error('加载选项失败:', error);
    return [];
  } finally {
    loadingBatchNumber.value = false;
  }
};

/** 物料选择事件 */
const onMaterialClick = async (row: any, materialId: string) => {
  try {
    if (materialId) {
      // 获取物料配置信息
      materialConfigInfo.value = await getMaterialConfig(materialId);
      // 加载仓库列表
      await fetchWarehouse({ materialId });
      // 判断仓库列表中是否有默认仓库，如果没有则清空仓库
      if (
        !options.value.some(
          (item) => item.value === materialConfigInfo.value?.warehouseId,
        )
      ) {
        materialConfigInfo.value.warehouseId = '';
      }
      // 赋值 仓库
      row.warehouseId = materialConfigInfo.value?.warehouseId || undefined;

      // 加载库位
      await fetchLocation(row);
      // 判断库位列表中是否有默认库位，如果没有则清空库位
      if (
        !locationOptions.value.some(
          (item) => item.value === materialConfigInfo.value?.locationId,
        )
      ) {
        materialConfigInfo.value.locationId = '';
      }
      // 赋值 库位
      row.locationId = materialConfigInfo.value?.locationId || undefined;
      // 获取批次号列表
      await fetchInventoryList(row);
    }
  } catch (error) {
    console.error(error);
    ElMessage.error('获取仓库失败');
  }
};

/** 生成批次号 并赋值给当前行的批次号*/
const generateBatchNumberForMaterialId = async (
  row: any,
  materialId: string,
) => {
  try {
    const batchNumber = await generateBatchNumber(materialId);
    row.batchNumber = batchNumber;
    return batchNumber;
  } catch {
    return '';
  }
};

/** 选择批次号后赋值库存数量 */
const batchNumberChange = (row: any) => {
  // 找到当前批次号的数据
  const currentData = batchNumberOptions.value?.find(
    (item) => item.value === row.batchNumber,
  );
  // 赋值
  const [, gridApi] = gridTable.value.getGridTableIns();
  gridApi.grid.setRow(row, {
    inventory: currentData?.data?.inventory || 0,
    quantity: 0,
  });
};
/** 选择仓库后清空库位和批次号的值 */
const warehouseChange = (row: any) => {
  const [, gridApi] = gridTable.value.getGridTableIns();
  gridApi.grid.setRow(row, {
    locationId: '',
    batchNumber: '',
  });
};
/** 选择库位后清空批次号的值 */
const locationChange = (row: any) => {
  const [, gridApi] = gridTable.value.getGridTableIns();
  gridApi.grid.setRow(row, {
    batchNumber: '',
  });
};
/** 编辑表单表格*/
const gridOptions = useGridOptions(onActionClick, onMaterialClick);

/** 校验 */
const validateForm = async () => {
  const isValid = await gridTable.value.tableValidate();
  if (isValid) {
    return false;
  }
  return true;
};

/** 获取表单数据 */
const getFormData = async () => {
  const data = await gridTable.value.getTableFullData();
  // 提取出需要的字段
  const result = data.map((item: any) => ({
    materialId: item.materialId?.materialId,
    warehouseId: item.warehouseId,
    locationId: item.locationId,
    batchNumber: item.batchNumber,
    quantity: item.quantity,
  }));
  return result;
};
/** 获取所有仓库 */
const fetchAllWarehouse = async () => {
  try {
    const result = await getWarehouseList();
    // 转换数据格式
    options.value =
      result?.map((item) => ({
        value: item?.warehouseId,
        label: item?.warehouseName,
      })) || [];
  } catch {
    ElMessage.error('获取仓库失败');
  }
};
/** 获取所有库位 */
const fetchAllLocation = async () => {
  try {
    const result = await getLocationList();
    // 转换数据格式
    locationOptions.value =
      result?.map((item) => ({
        value: item.locationId,
        label: item.locationName,
      })) || [];
  } catch (error) {
    console.error(error);
  }
};
/** 根据库存调整单据id获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInvcAdjustDoc(
      props.invcAdjustDocId,
      props.invcAdjustDocNumber,
      true,
    );
    // 处理数据
    data.invcAdjustItemList?.forEach((item: any) => {
      item.materialId = {
        materialId: item.materialId,
        materialName: item.materialName,
      };
    });
    // 获取所有仓库
    await fetchAllWarehouse();
    // 获取所有库位
    await fetchAllLocation();
    // 赋值
    await gridTable.value.setTableData(data.invcAdjustItemList);
  } catch (error) {
    console.error(error);
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
onMounted(() => {
  if (props.invcAdjustDocId || props.invcAdjustDocNumber) {
    getData();
  }
});
defineExpose({
  getFormData,
  validateForm,
});
</script>
<template>
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>物料信息</span>
    </template>
    <DynamicTable ref="gridTable" :grid-options="gridOptions" class="border">
      <template #warehouseId="{ row }">
        <ElSelectV2
          v-model="row.warehouseId"
          :placeholder="warehouseTip"
          clearable
          filterable
          :options="options"
          :loading="loadingWarehouse"
          :disabled="!row.materialId"
          @change="warehouseChange(row)"
          @focus="fetchWarehouse({ materialId: row.materialId.materialId })"
        />
      </template>
      <template #locationId="{ row }">
        <ElSelectV2
          ref="locationIdRef"
          v-model="row.locationId"
          :placeholder="locationTip"
          clearable
          filterable
          :loading="loadingLocation"
          :disabled="!row.warehouseId"
          :options="locationOptions"
          @change="locationChange(row)"
          @focus="fetchLocation(row)"
        />
      </template>
      <template #batchNumber="{ row }">
        <ElSelectV2
          v-model="row.batchNumber"
          placeholder="请选择批次号"
          clearable
          filterable
          :loading="loadingBatchNumber"
          :options="batchNumberOptions"
          :disabled="!row.locationId"
          @focus="fetchInventoryList(row)"
          @change="batchNumberChange(row)"
        >
          <template #prefix>
            <el-tooltip
              content="点击创建批次号"
              effect="light"
              placement="top-start"
              :show-after="300"
            >
              <ElButton
                link
                size="small"
                :disabled="!row.locationId"
                @click.stop="
                  generateBatchNumberForMaterialId(
                    row,
                    row.materialId.materialId,
                  )
                "
              >
                <IconFont name="bianji" class="iconfont" />
              </ElButton>
            </el-tooltip>
          </template>
        </ElSelectV2>
      </template>
      <template #inventory="{ row }">
        <span v-if="row.locationId">{{ row.inventory || 0 }} </span>
      </template>
      <template #quantity="{ row }">
        <ElInputNumber
          :disabled="!row.locationId"
          v-model="row.quantity"
          :min="-row.inventory"
        />
      </template>
      <template #inventoryAfter="{ row }">
        <span v-if="row.locationId">
          {{ row.inventory + (row.quantity || 0) }}
        </span>
      </template>
    </DynamicTable>
  </FormCard>
</template>
<style scoped>
:deep(.vxe-grid--bottom-wrapper > div:first-child) {
  display: flex;
  justify-content: center;
  margin-top: 0;
}
</style>
