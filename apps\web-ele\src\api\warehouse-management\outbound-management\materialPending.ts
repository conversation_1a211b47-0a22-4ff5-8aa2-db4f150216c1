// import type { Recordable } from '@vben/types';

import { requestClient } from '#/api/request';

import { warehousePath } from '../../path';

export namespace MaterialPendingApi {
  export interface MaterialPendingPageParams {
    /** 出库单据号列表，多个用英文逗号分隔 */
    outBoundDocNumberList?: string[];
    /** 最近修改时间开始，时间格式：yyyy-MM-dd HH:mm */
    modifyTimeStart?: string;
    /** 最近修改时间结束，时间格式：yyyy-MM-dd HH:mm */
    modifyTimeEnd?: string;
    /** 当前页，默认第1页 */
    pageNum?: number;
    /** 分页数，默认每一页默认10条 */
    pageSize?: number;
  }

  export interface MaterialPendingRecord {
    /* 备料单据ID */
    prepDocId: string;

    /* 备料单据编号 */
    prepDocNumber: string;

    /* 出库单据ID */
    outBoundDocId: string;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 创建人id */
    createUser: string;

    /* 创建人姓名 */
    createUserName: string;

    /* 最后修改时间 */
    modifyTime: Record<string, unknown>;

    /* 备料单据状态，字典wmPreparationDocStatus */
    docStatus: string;

    /* 备料单据状态名称，字典wmPreparationDocStatus */
    docStatusLabel: string;
  }

  export interface PrepDocDetailParams {
    /** 备料单据ID */
    prepDocId: string;
    /** 备料单据编号 */
    prepDocNumber?: string;
    /** 是否查询子项，默认不查询，true-查询，false-不查询 */
    isQueryItem?: boolean;
  }

  export interface PrepDocDetailByOutDocParams {
    /** 出库单据ID */
    outBoundDocId: string;
    /** 出库单据编号 */
    outBoundDocNumber?: string;
    /** 是否查询子项，默认不查询，true-查询，false-不查询 */
    isQueryItem?: boolean;
  }

  /** 公共备料单据子项参数 */
  export interface CommonItem {
    /* 批次号 */
    batchNumber?: string;

    /* 原库位ID */
    oldLocationId?: string;

    /* 备料库位ID */
    targetLocationId?: string;

    /* 备料数量 */
    transferQuantity?: number;

    /* 仓库ID */
    warehouseId?: string;
  }

  export interface ItemList extends CommonItem {
    /* 仓库编号 */
    warehouseCode: string;

    /* 仓库名称 */
    warehouseName: string;

    /* 原库位编号 */
    oldLocationCode: string;

    /* 原库位名称 */
    oldLocationName: string;

    /* 目标库位编号 */
    targetLocationCode: string;

    /* 目标库位名称 */
    targetLocationName: string;
  }

  export interface PrepItemList {
    /* 物料ID */
    materialId: string;

    /* 物料编号 */
    materialCode: string;

    /* 物料名称 */
    materialName: string;

    /* 物料图片ID */
    pictureFileId: string;

    /* 基本单位值，字典baseMaterialUnit */
    baseUnit: string;

    /* 基本单位标签，字典baseMaterialUnit */
    baseUnitLabel: string;

    /* 物料属性值，字典baseMaterialAttribute */
    materialAttribute: string;

    /* 物料属性标签，字典baseMaterialAttribute */
    materialAttributeLabel: string;

    /* 物料大类值，字典baseMaterialType */
    materialType: string;

    /* 物料大类标签，字典baseMaterialType */
    materialTypeLabel: string;

    /* 物料细类 */
    materialCategory: string;

    /* 物料细类名称 */
    materialCategoryName: string;

    /* 当前单据该物料申请数量 */
    transferQuantitySum: number;

    /* 型号规格 */
    materialSpecs: string;

    /* 是否标准物料，true-标准物料, false-非标准物料 */
    isStandard: boolean;

    /* 备料单据子项列表 */
    itemList: ItemList[];
  }

  export interface PrepDocDetail {
    /* 备料单据ID */
    prepDocId: string;

    /* 备料单据编号 */
    prepDocNumber: string;

    /* 出库单据ID */
    outBoundDocId: string;

    /* 出库单据编号 */
    outBoundDocNumber: string;

    /* 源单据类型标识 */
    origDocTypeCode: string;

    /* 源单据类型名称 */
    origDocTypeName: string;

    /* 源单据编号 */
    origDocNumber: string;

    /* 领料人ID */
    materialUser: string;

    /* 领料人姓名 */
    materialUserName: string;

    /* 领料人部门ID */
    materialUserDeptId: string;

    /* 领料人部门名称 */
    materialUserDeptName: string;

    /* 提交人ID */
    submitUser: string;

    /* 提交人姓名 */
    submitUserName: string;

    /* 提交人部门ID */
    submitUserDeptId: string;

    /* 提交人部门姓名 */
    submitUserDeptName: string;

    /* 提交时间，时间格式：yyyy-MM-dd HH:mm */
    submitTime: Record<string, unknown>;

    /* 执行人ID */
    collectorUser: string;

    /* 执行人姓名 */
    collectorUserName: string;

    /* 执行人部门ID */
    collectorUserDeptId: string;

    /* 执行人部门名称 */
    collectorUserDeptName: string;

    /* 执行时间 */
    executorTime: Record<string, unknown>;

    /* 单据审核状态 */
    auditStatus: string;

    /* 审核状态名称，写入当前流程所在节点 */
    auditStatusName: string;

    /* 审核流程实例ID */
    processInstanceId: string;

    /* 备料单据状态，字典wmPreparationDocStatus */
    docStatus: string;

    /* 备料单据状态名称，字典wmPreparationDocStatus */
    docStatusLabel: string;

    /* 备注 */
    remark: string;

    /* 附件流水号 */
    serialNumber: string;

    /* 备料子项列表 */
    prepItemList: PrepItemList[];
  }

  /** 备料单据子项参数 */
  export interface PrepDocPrepItem extends CommonItem {
    /* 物料id */
    materialId?: string;
  }

  /** 备料单据参数 */
  export interface PrepDocParams {
    /* 备料单据ID */
    prepDocId?: string;

    /* 出库单据ID */
    outBoundDocId?: string;

    /* 备注 */
    remark?: string;

    /* 附件流水号 */
    serialNumber?: string;

    /* 备料单据子项列表 */
    prepItemList?: PrepDocPrepItem[];
  }
}

/**
 * 查询待我提交的备料单据分页列表
 */
export async function getPrepMyDraftDocPage(
  params: MaterialPendingApi.MaterialPendingPageParams,
) {
  return requestClient.post<MaterialPendingApi.MaterialPendingRecord>(
    `${warehousePath}/wm/prep/getMyDraftDocPage`,
    {
      ...params,
    },
  );
}

/**
 * 获取备料单据详细信息
 */
export function getPrepDocDetail(
  params: MaterialPendingApi.PrepDocDetailParams,
) {
  const { prepDocId, prepDocNumber, isQueryItem = false } = params;
  return requestClient.get<MaterialPendingApi.PrepDocDetail>(
    `${warehousePath}/wm/prep/getPrepDocDetail?prepDocId=${prepDocId}&prepDocNumber=${prepDocNumber}&isQueryItem=${isQueryItem}`,
  );
}

/**
 * 根据出库单据获取备料单据详细信息
 */
export function getPrepDocDetailByOutDoc(
  params: MaterialPendingApi.PrepDocDetailByOutDocParams,
) {
  const { outBoundDocId, outBoundDocNumber, isQueryItem = false } = params;
  return requestClient.get<MaterialPendingApi.PrepDocDetail>(
    `${warehousePath}/wm/prep/getPrepDocDetailByOutDoc?outBoundDocId=${outBoundDocId}&outBoundDocNumber=${outBoundDocNumber}&isQueryItem=${isQueryItem}`,
  );
}

/**
 * 暂存备料单据
 */
export async function saveOrModPrepDoc(
  params: MaterialPendingApi.PrepDocParams,
) {
  return requestClient.post(
    `${warehousePath}/wm/prep/saveOrModPrepDoc`,
    {
      ...params,
    },
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/**
 * 提交备料单据
 */
export async function submitPrepDoc(params: MaterialPendingApi.PrepDocParams) {
  return requestClient.post(
    `${warehousePath}/wm/prep/submitPrepDoc`,
    {
      ...params,
    },
    {
      headers: {
        'Content-Type-json': 'application/json',
      },
    },
  );
}

/**
 * 删除待我提交的备料单
 */
export async function delPrepDoc(prepDocId: string) {
  return requestClient.get(`${warehousePath}/wm/prep/delPrepDoc/${prepDocId}`);
}
