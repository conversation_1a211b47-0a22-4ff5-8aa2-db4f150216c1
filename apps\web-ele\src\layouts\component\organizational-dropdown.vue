<script lang="ts" setup>
import { ref } from 'vue';

interface Props {
  orgList?: Array<{ orgId: string; orgName: string }>;
  currentOrgName: '';
}
defineOptions({
  name: 'OrganizationalDropdown',
});
const props = withDefaults(defineProps<Props>(), {
  orgList: () => [],
  currentOrgName: '',
});

const orgName = ref('');
orgName.value = props.currentOrgName;

const handleCommand = (command: string) => {
  // 组织切换
  orgName.value = command;
};
</script>
<template>
  <div class="flex flex-wrap items-center">
    <el-dropdown @command="handleCommand">
      <span class="el-dropdown-link">
        {{ currentOrgName }}
        <el-icon class="el-icon--right"><ArrowDown /></el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="org in orgList"
            :key="org.orgId"
            :command="org.orgName"
          >
            {{ org.orgName }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>
