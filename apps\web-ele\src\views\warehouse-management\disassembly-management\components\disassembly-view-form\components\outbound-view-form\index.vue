<script setup lang="ts">
import MaterialForm from '#/views/warehouse-management/outbound-management/outbound-already/modules/materials-info/index.vue';
import CompletedForm from '#/views/warehouse-management/outbound-management/outbound-already/modules/outbound-info/index.vue';
import ColseForm from '#/views/warehouse-management/outbound-management/outbound-documents/modules/outbound-info/index.vue';
import PendingForm from '#/views/warehouse-management/outbound-management/outbound-pending/modules/outbound-info/index.vue';

const props = defineProps({
  outBoundDocId: {
    type: String,
    default: '',
  },
  outBoundDocNumber: {
    type: String,
    default: '',
  },
  docStatus: {
    type: String,
    default: '',
  },
});
</script>

<template>
  <PendingForm
    v-if="props.docStatus === 'pending'"
    :out-bound-doc-id="props.outBoundDocId"
    :out-bound-doc-number="props.outBoundDocNumber"
    :is-external-view="true"
  />

  <CompletedForm
    v-if="props.docStatus === 'collected'"
    :out-bound-doc-id="props.outBoundDocId"
    :out-bound-doc-number="props.outBoundDocNumber"
  />

  <ColseForm
    v-if="props.docStatus === 'cancelAudit' || props.docStatus === 'closed'"
    :out-bound-doc-id="props.outBoundDocId"
    :out-bound-doc-number="props.outBoundDocNumber"
  />

  <MaterialForm
    :out-bound-doc-id="props.outBoundDocId"
    :out-bound-doc-number="props.outBoundDocNumber"
  />
</template>
