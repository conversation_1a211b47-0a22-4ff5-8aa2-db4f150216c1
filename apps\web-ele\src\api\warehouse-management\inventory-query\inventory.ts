import type { Recordable } from '@vben/types';

import { warehousePath } from '#/api/path';
import { requestClient } from '#/api/request';

/** 库存查询返回类型 */
export namespace InventoryQueryApi {
  /** 库存信息和库存明细共有数据类型 */
  export interface InventoryBaseData {
    /**	库存ID */
    inventoryId: string;
    /** 仓库ID */
    warehouseId: string;
    /**	仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /**	物料ID */
    materialId: string;
    /**	物料编号 */
    materialCode: string;
    /**	物料名称 */
    materialName: string;
    /**	物料图片ID */
    pictureFileId: string;
    /** 规格型号 */
    materialSpecs: string;
    /** 物料属性 */
    materialAttribute: string;
    /** 物料属性名称 字典baseMaterialAttribute*/
    materialAttributeLabel: string;
    /** 物料大类 */
    materialType: string;
    /** 物料大类名称 字典baseMaterialType*/
    materialTypeLabel: string;
    /** 基本单位 字典baseMaterialUnit*/
    baseUnit: string;
    /** 基本单位名称 字典baseMaterialUnit*/
    baseUnitLabel: string;
    /** 是否标准物料:true-标准物料, false-非标准物料 */
    isStandard: boolean;
    /** 物料细类 */
    materialCategory: string;
    /** 库存量 */
    inventory: number;
  }
  /** 库存信息数据 */
  export interface InventoryData extends InventoryBaseData {
    /** 可用量 */
    availableInventory: number;
    /** 锁库量 */
    blockQuantity: number;
    /** 最近入库时间 */
    lastInTime: string;
    /** 最近出库时间 */
    lastOutTime: string;
    /** 是否参与呆滞分析 */
    isJoinObsoleteAnalysis: boolean;
    /** 是否呆滞 */
    isObsoleteAnalysis: boolean;
    /** 呆滞期(天) */
    obsoletePeriod: number;
    /** 呆滞天数 */
    obsoleteDay: number;
    /** 是否参与安全库存预警 */
    isJoinSafetyStockWarn: boolean;
    /** 是否安全库存预警*/
    isSafetyStockWarn: boolean;
    /** 安全库存 */
    safetyInventory: number;
    /** 库存明细列表 */
    invcDetailList: {
      /** 批次号 */
      batchNumber: string;
      /** 库存量 */
      inventory: number;
      /** 最近入库时间 */
      lastInTime: string;
      /** 最近出库时间 */
      lastOutTime: string;
      /** 库位编号 */
      locationCode: string;
      /** 库为ID */
      locationId: string;
      /** 库位名称 */
      locationName: string;
      /** 均价(单价) */
      unitPrice: number;
    }[];
  }
  /** 库存明细详细信息 */
  export interface InvcDetail extends InventoryBaseData {
    /** 库存明细ID */
    invcDetailId: string;
    /** 库位编号 */
    locationCode: string;
    /** 库位名称 */
    locationName: string;
    /* 批次号 */
    batchNumber: string;
    /**	影响单据类型标识 */
    docTypeCode: string;
    /**	影响单据编号 */
    docNumber: string;
    /**	修改时间 */
    modifyTime: string;
  }
  /** 库存明细详细数据 */
  export interface InventoryDetailData extends InventoryBaseData {
    /** 库存明细ID */
    invcDetailId: string;
    /** 库位编号 */
    locationCode: string;
    /** 库位名称 */
    locationName: string;
    /* 批次号 */
    batchNumber: string;
    /** 均价(单价) */
    unitPrice: number;
    /** 最近入库时间 */
    lastInTime: string;
    /** 最近出库时间 */
    lastOutTime: string;
  }
  /** 库存明细 根据仓库ID和物料ID查询库存明细*/
  export interface InventoryDetailQuery extends InventoryBaseData {
    /**	可用量 */
    availableInventory: number;
    /**	均价(单价)，默认不可见 */
    unitPrice: number;
    invcDetailList: {
      [key: string]: any;
      /** 批次号 */
      batchNumber: string;
      /** 最后入库时间*/
      lastInTime: string;
      /** 最后出库时间 */
      lastOutTime: string;
      /** 库位编号*/
      locationCode: string;
      /** 库位ID */
      locationId: string;
      /** 库位名称 */
      locationName: string;
      /** 均价(单价)，默认不可见 */
      unitPrice: number;
    }[];
  }
  /** 呆滞库存数据*/
  export interface ObsoleteInventory extends InventoryBaseData {
    /** 可用量 */
    availableInventory: number;
    /** 最近入库时间 */
    lastInTime: string;
    /** 最近出库时间 */
    lastOutTime: string;
    /** 呆滞期(天) */
    obsoletePeriod: number;
  }
  /** 安全库存预警数据*/
  export interface SafetyStockWarn extends InventoryBaseData {
    /** 可用量 */
    availableInventory: number;
    /** 最近入库时间 */
    lastInTime: string;
    /** 最近出库时间 */
    lastOutTime: string;
    /** 安全库存 */
    safetyInventory: number;
  }
  /** 分页基本 */
  export interface PageBase {
    [key: string]: any;
    records: Array<any>;
    total: string;
    pageSize: string;
    pageNum: string;
    pages: string;
  }
  /* 库存分页列表*/
  export interface InventoryPage extends PageBase {
    records: InventoryData[];
  }
  /** 库存明细分页列表 */
  export interface InventoryDetailPage extends PageBase {
    records: InventoryDetailData[];
  }
  /** 呆滞库存分页列表*/
  export interface ObsoleteInventoryPage extends PageBase {
    records: ObsoleteInventory[];
  }
  /** 安全库存预警分页列表 */
  export interface SafetyStockWarnPage extends PageBase {
    records: SafetyStockWarn[];
  }
  /** 仓库列表 */
  export interface WarehouseList {
    /**	仓库ID */
    warehouseId: string;
    /**	仓库名称 */
    warehouseName: string;
    /** 仓库编码 */
    warehouseCode: string;
    /** 库存量 */
    inventory: number;
    /** 可用量 */
    availableInventory: number;
  }
  /** 查询单个物料可使用的仓库列表 */
  export interface WarehouseListForMaterial {
    /** 物料ID */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 默认仓库 */
    mainWarehouseId: string;
    /** 默认库位ID */
    mainLocationId: string;
    /** 可用仓库列表 */
    warehouseList: WarehouseList[];
  }
  /** 库存明细列表 详细 */
  export interface InventoryDetailList extends InventoryBaseData {
    /**	库存明细ID */
    invcDetailId: string;
    /** 批次号 */
    batchNumber: string;
    /** 均价(单价) */
    unitPrice: number;
    /** 最近入库时间，时间格式：yyyy-MM-dd HH:mm */
    lastInTime: string;
    /** 最近出库时间，时间格式：yyyy-MM-dd HH:mm */
    lastOutTime: string;
  }

  export interface ImportInventoryRes {
    /* */
    success: boolean;

    /* */
    code: number;

    /* */
    data: {
      /* 不可用或不存在的库位编号列表 */
      disableLocationCodeList: string[];

      /* 不可用或不存在的物料编号列表 */
      disableMaterialCodeList: string[];

      /* 库存错误对应行列表 */
      invcErrorRowList: string[];

      /* 库存明细重复行列表，若物料、库位、批次号已存在库存，且isOverwrite为false时，则记录在此列表。 */
      repeatInvcRowList: string[];

      /* 重复行列表，若物料、库位、批次号为唯一在excel中存在重复，则记录在此列表。 */
      repeatRowList: string[];

      /* 单价错误对应行列表 */
      unitPriceErrorRowList: string[];
    };

    /* */
    msg: string;
  }

  export interface InventoryChangeItem {
    /** 关联单据ID */
    docId: string;
    /** 关联单据编号 */
    docNumber: string;
    /** 关联单据标识 */
    docCode: string;
    /** 关联单据明细子项ID列表 */
    docItemIdList: string[];
    /** 物料ID */
    materialId: string;
    /** 物料编号 */
    materialCode: string;
    /** 物料名称 */
    materialName: string;
    /** 仓库ID */
    warehouseId: string;
    /** 仓库编号 */
    warehouseCode: string;
    /** 仓库名称 */
    warehouseName: string;
    /** 原库存量 */
    oldInventory: number;
    /** 原可用量 */
    oldAvailableInventory: number;
    /** 新库存量 */
    newInventory: number;
    /** 新可用量 */
    newAvailableInventory: number;
    /** 变动时间 */
    createTime: string;
  }
}

/** 导入库存列表*/
export async function importInventory({
  data,
  isOverwrite,
}: {
  data: { file: Blob | File };
  isOverwrite: boolean;
}) {
  return requestClient.upload<InventoryQueryApi.ImportInventoryRes>(
    `${warehousePath}/wm/inventory/importInventory`,
    {
      ...data,
      isOverwrite,
    },
  );
}
/** 查询库存分页列表 */
export async function getInventoryPage(params: Recordable<any>) {
  return requestClient.post<InventoryQueryApi.InventoryPage>(
    `${warehousePath}/wm/inventory/getInventoryPage`,
    params,
  );
}

/** 导出库存信息模板 */
export async function exportInventoryTemplate() {
  return requestClient.get(`${warehousePath}/wm/inventory/download`, {
    responseReturn: 'raw',
    responseType: 'blob', // 设置响应类型为 blob
  });
}

/** 导出库存列表 */
export async function exportInventory(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inventory/exportInventory`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 查询库存明细分页列表 */
export async function getInventoryPageDetail(params: Recordable<any>) {
  return requestClient.post<InventoryQueryApi.InventoryDetailPage>(
    `${warehousePath}/wm/inventory/detail/getInventoryPage`,
    params,
  );
}
/** 导出库存明细列表 */
export async function exportInvcDetail(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inventory/detail/exportInvcDetail`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 查询库存变动记录列表 */
export async function getInventoryChangeList(params: Recordable<any>) {
  return requestClient.post<InventoryQueryApi.InventoryChangeItem[]>(
    `${warehousePath}/wm/inventory/change/getInventoryChangeList`,
    params,
  );
}

/** 根据仓库和物料查询库存详情 */
export async function getInvcByWarehouseIdAndMaterialId(
  warehouseId: string,
  materialId: string,
  isInvcDetailList: boolean = false,
  isObsoleteAnalysis: boolean = true,
  isSafetyStockWarn: boolean = true,
) {
  return requestClient.get<InventoryQueryApi.InventoryData>(
    `${warehousePath}/wm/inventory/getInventoryDetail`,
    {
      params: {
        warehouseId,
        materialId,
        isInvcDetailList,
        isObsoleteAnalysis,
        isSafetyStockWarn,
      },
    },
  );
}
/** 导入库存信息模板 */
export async function importInventoryTemplate(data: { file: Blob | File }) {
  return requestClient.upload(
    `${warehousePath}/wm/inventory/importInventoryTemplate`,
    data,
  );
}
/** 获取库存明细详细信息 */
export async function getInvcDetail(invcDetailId: string) {
  return requestClient.get<InventoryQueryApi.InventoryBaseData>(
    `${warehousePath}/wm/inventory/detail/getInvcDetail/${invcDetailId}`,
  );
}

/** 根据仓库物料查询库存明细 */
export async function getInvcDetailByMaterIdAndWareId(
  materialId: string,
  warehouseId: string,
) {
  return requestClient.get<InventoryQueryApi.InventoryDetailQuery>(
    `${warehousePath}/wm/inventory/getInvcDetailByMaterIdAndWareId`,
    {
      params: {
        materialId,
        warehouseId,
      },
    },
  );
}

/** 查询呆滞库存分页列表 */
export async function getObsoleteMaterialPage(params: Recordable<any>) {
  return requestClient.post<InventoryQueryApi.ObsoleteInventoryPage>(
    `${warehousePath}/wm/inventory/getObsoleteMaterialPage`,
    params,
  );
}
/** 查询安全库存预警分页列表 */
export async function getSafetyStockWarnPage(params: Recordable<any>) {
  return requestClient.post<InventoryQueryApi.SafetyStockWarnPage>(
    `${warehousePath}/wm/inventory/getSafetyStockWarnPage`,
    params,
  );
}
/** 导出安全库存预警列表 */
export async function exportSafetyStockWarn(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inventory/exportSafetyStockWarn`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}
/** 导出呆滞库存列表 */
export async function exportObsoleteMaterial(params: Recordable<any>) {
  return requestClient.post(
    `${warehousePath}/wm/inventory/exportObsoleteMaterial`,
    params,
    {
      responseReturn: 'raw',
      responseType: 'blob', // 设置响应类型为 blob
    },
  );
}

/** 查询单个物料可用的仓库列表和对应库存 */
export async function getEnableWarehouseListByMaterial(
  params: Recordable<any>,
) {
  return requestClient.post<InventoryQueryApi.WarehouseListForMaterial>(
    `${warehousePath}/wm/inventory/getEnableWarehouseListByMaterial`,
    params,
  );
}

/** 查询单个物料可使用的仓库列表*/
export async function getActiveWarehouseListByMaterial(
  params: Recordable<any>,
) {
  return requestClient.post<InventoryQueryApi.WarehouseListForMaterial>(
    `${warehousePath}/wm/inventory/getActiveWarehouseListByMaterial`,
    params,
  );
}

/** 查询库存明细列表*/
export async function getInventoryList(params: Recordable<any>) {
  return requestClient.post<InventoryQueryApi.InventoryDetailList[]>(
    `${warehousePath}/wm/inventory/detail/getInventoryList`,
    params,
  );
}
