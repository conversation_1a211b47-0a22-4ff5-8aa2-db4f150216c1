<script setup lang="ts">
import { computed } from 'vue';

import { ElPopover } from 'element-plus';

const props = defineProps({
  placement: {
    type: String,
    default: 'bottom',
  },
  width: {
    type: [String, Number],
    default: '150px',
  },
  icon: {
    type: String,
    default: 'tishi',
  },
  iconSize: {
    type: [Number, String],
    default: 20,
  },
});

const emit = defineEmits(['show', 'hide']);

const computedWidth = computed(() => {
  if (typeof props.width === 'number') {
    return `${props.width}px`;
  }
  return props.width;
});

const computedIconSize = computed(() => {
  if (typeof props.iconSize === 'number') {
    return `${props.iconSize}px`;
  }
  return props.iconSize;
});

/** 显示时触发 */
const show = () => {
  emit('show');
};

/** 隐藏时触发 */
const hide = () => {
  emit('hide');
};
</script>

<template>
  <div class="cursor-pointer">
    <ElPopover
      loading="true"
      trigger="click"
      :placement="props.placement"
      :width="computedWidth"
      @show="show"
      @hide="hide"
    >
      <slot></slot>
      <template #reference>
        <slot name="reference">
          <IconFont :size="computedIconSize" :name="props.icon" />
        </slot>
      </template>
    </ElPopover>
  </div>
</template>
