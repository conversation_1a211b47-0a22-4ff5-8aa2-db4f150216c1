import type { VxeTableGridOptions } from '@girant/adapter';

/** 库存明细 */
export function useColumns(): VxeTableGridOptions['columns'] {
  return [
    { title: '序号', type: 'seq', width: 50 },
    {
      title: '仓库名称',
      field: 'warehouseName',
      minWidth: 100,
    },
    {
      title: '库位名称',
      field: 'locationName',
      minWidth: 100,
    },
    {
      title: '批次号',
      field: 'batchNumber',
      minWidth: 100,
    },
    {
      title: '单位',
      field: 'baseUnitLabel',
      minWidth: 65,
    },
    {
      title: '库存量',
      field: 'inventory',
      minWidth: 65,
    },
    {
      title: '均价/￥',
      field: 'unitPrice',
      minWidth: 65,
    },
  ];
}
