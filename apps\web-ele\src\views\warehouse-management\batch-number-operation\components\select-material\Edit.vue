<script setup lang="ts">
import type { VbenFormSchema } from '@girant/adapter';

import type {
  InventoryQueryApi,
  materialConfig,
} from '#/api/warehouse-management';

import { h, onMounted, ref } from 'vue';

import { isEmpty } from '@vben/utils';

import { UploadFiles } from '@girant-web/upload-files-component';
import { useVbenForm } from '@girant/adapter';
import { ElMessage } from 'element-plus';

import { getEnumByName } from '#/api';
import {
  getActiveWarehouseListByMaterial,
  getBatchnumDocDetail,
} from '#/api/warehouse-management';
import MaterialSelect from '#/components/material-select/Index.vue';
import RemarkOptionSelect from '#/components/remark-option-select/index.vue';

const props = defineProps({
  batchnumDocId: {
    type: String,
    default: '',
  },
  batchnumDocNumber: {
    type: String,
    default: '',
  },
});

const emit = defineEmits([
  'materialIdChange',
  'warehouseIdChange',
  'docCodeChange',
]);

const loading = ref(false);

const batchnumDocDetail = ref<any>();

/** 物料详情*/
const materialDetail = ref<materialConfig.materialDetail>();

const warehouseListData = ref<InventoryQueryApi.WarehouseListForMaterial>();
/** 获取仓库列表*/
const fetchWarehouse = async (materialId: string) => {
  if (materialId === materialDetail.value?.materialId) {
    return;
  }
  if (!materialId) {
    warehouseListData.value = undefined;
    return;
  }
  const resData = await getActiveWarehouseListByMaterial({
    materialId,
  });
  warehouseListData.value = resData;
  formApi.updateSchema([
    {
      componentProps: {
        options:
          resData?.warehouseList?.map((item) => ({
            label: `${item.warehouseName}-库存量${item.inventory}`,
            value: item.warehouseId,
            disabled: item.inventory === 0,
          })) || [],
      },
      fieldName: 'warehouseId',
    },
  ]);
  return resData;
};

const currentDocCode = ref<string>('WM0050');

const schema: VbenFormSchema[] = [
  {
    component: 'RadioGroup',
    componentProps: {
      options: [], // 初始为空
      onChange: async (value: any) => {
        emit('docCodeChange', value);
        currentDocCode.value = value;
        formApi.updateSchema([
          {
            label: value === 'WM0050' ? '合并原因' : '拆分原因',
            fieldName: 'remarkOptionList',
          },
          {
            label: value === 'WM0050' ? '合并原因说明' : '拆分原因说明',
            fieldName: 'remark',
          },
        ]);
      },
    },
    defaultValue: currentDocCode.value,
    dependencies: {
      async componentProps() {
        const data = await getEnumByName('WmBatchNumHandleTypeEnums');
        return {
          options: data.map((item: any) => ({
            label: item.enumLabel,
            value: item.enumValue,
          })),
        };
      },
      triggerFields: [''],
    },
    fieldName: 'docCode',
    formItemClass: 'col-span-2',
    label: '操作类型',
    rules: 'required',
  },

  {
    component: h(MaterialSelect, {
      valueKey: 'materialId',
    }),
    componentProps: (_: any, formApi: any) => {
      return {
        onChange: async (materialId: string, materialDetail: any) => {
          if (!materialId) {
            formApi.setValues(
              {
                materialId: '',
                materialSpecs: '',
                baseUnitLabel: '',
                warehouseId: '',
              },
              true,
            );
            emit('materialIdChange', '');
            emit('warehouseIdChange', '');
            return;
          }
          formApi.setFieldValue('materialId', materialDetail);
          emit('materialIdChange', materialId);
          materialDetail.value = materialDetail;

          const { materialSpecs, baseUnitLabel } = materialDetail;

          formApi.setValues({
            materialSpecs,
            baseUnitLabel,
            warehouseId: '',
          });

          await fetchWarehouse(materialId);
        },
      };
    },

    fieldName: 'materialId',
    label: '物料',
    rules: 'required',
    formItemClass: 'col-span-2',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue);
    },
    fieldName: 'baseUnitLabel',
    label: '基本单位',
    formItemClass: 'col-span-1',
  },

  {
    component: (prop: any) => {
      return h('span', {}, prop.modelValue);
    },
    fieldName: 'materialSpecs',
    label: '规格型号',
    formItemClass: 'col-span-full',
  },

  {
    component: 'Select',
    fieldName: 'warehouseId',
    label: '所属仓库',
    rules: 'selectRequired',
    formItemClass: 'col-span-2',
    componentProps: {
      options: [],
      onChange: async (value: any) => {
        emit('warehouseIdChange', value);
      },
    },
    dependencies: {
      triggerFields: ['materialId'],
      componentProps: (values: any) => {
        return {
          disabled: !values.materialId,
        };
      },
    },
  },

  {
    component: 'Input',
    fieldName: 'remarkOptionList',
    label: '合并原因',
    formItemClass: 'col-span-full',
    rules: 'selectRequired',
  },

  {
    component: 'Textarea',
    componentProps: {
      autosize: { minRows: 3 },
      maxlength: 500,
      placeholder: '请输入',
      showWordLimit: true,
    },
    fieldName: 'remark',
    formItemClass: 'col-span-full',
    label: '合并原因说明',
  },
  {
    component: h(UploadFiles, {
      mode: 'editMode',
      showOperateRegion: false,
      tableProps: {
        maxHeight: '300',
      },
    }),
    modelPropName: 'serialNumber', // 绑定serialNumber进行回显
    fieldName: 'serialNumber',
    formItemClass: 'col-span-full',
    label: '附件',
  },
];

/** 表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' }, labelWidth: 100 },
  schema,
  showDefaultActions: false,
  wrapperClass: 'grid-cols-3',
});

const validateForm = async () => {
  const res = await formApi.validate();
  return res.valid;
};

const getMaterialSelForm = async (): Promise<null | Record<string, any>> => {
  const validateRes = await validateForm();

  if (!validateRes) {
    throw new Error('请检查表单数据');
  }

  const formData = await formApi.getValues();

  const formatFromData = {
    ...formData,
    materialId: formData.materialId.materialId,
  };
  return formatFromData;
};

const getBatchnumDocDetailHandle = async () => {
  try {
    loading.value = true;
    const res = await getBatchnumDocDetail({
      batchnumDocId: props.batchnumDocId,
      batchnumDocNumber: props.batchnumDocNumber,
    });

    const formatRes = {
      ...res,
      materialId: {
        materialId: res.materialId,
        materialName: res.materialName,
      },
      remarkOptionList:
        res?.remarkOptionList?.map((item: any) => item.optionId) || [],
    };

    batchnumDocDetail.value = formatRes;
    return formatRes;
  } catch {
    ElMessage.error('获取批次号处理单据详情失败');
    return null;
  } finally {
    loading.value = false;
  }
};

onMounted(async () => {
  if (!isEmpty(props.batchnumDocId) || !isEmpty(props.batchnumDocNumber)) {
    const res = await getBatchnumDocDetailHandle();
    if (!res) {
      return;
    }

    fetchWarehouse(res.materialId.materialId);

    emit('materialIdChange', res.materialId.materialId);
    emit('docCodeChange', res.docCode);
    emit('warehouseIdChange', res.warehouseId);

    formApi.setValues(res);
  }
});

const handleRemarkOptionListChange = (_: any, isDescRequired: boolean) => {
  formApi.updateSchema([
    {
      rules: isDescRequired ? 'required' : '',
      fieldName: 'remark',
    },
  ]);
};

defineExpose({
  formApi,
  getMaterialSelForm,
});
</script>

<template>
  <Form v-loading="loading">
    <template #remarkOptionList="{ modelValue }">
      <RemarkOptionSelect
        :model-value="modelValue"
        :doc-code="currentDocCode"
        doc-field-code="remark"
        @update:model-value="
          (optionIds) => {
            formApi.setFieldValue('remarkOptionList', optionIds);
          }
        "
        @change="handleRemarkOptionListChange"
      />
    </template>
  </Form>
</template>
