import { defineConfig } from '@vben/vite-config';

import ElementPlus from 'unplugin-element-plus/vite';

const BASE_HOST = 'http://*************:81'; // 测试环境
// const BASE_HOST = 'http://*************:9000'; // 开发环境
// const BASE_HOST = 'http://**************:9005'; // 善煜环境

export default defineConfig(async () => {
  return {
    application: {},
    vite: {
      plugins: [
        ElementPlus({
          format: 'esm',
        }),
      ],
      server: {
        proxy: {
          '/auth/v1': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/auth\/v1/, '/auth/view/v1'),
            target: BASE_HOST,
          },
          '/base-data/v1': {
            changeOrigin: true,
            rewrite: (path) =>
              path.replace(/^\/base-data\/v1/, '/base-data/view/v1'),
            target: BASE_HOST,
          },
          '/dict/v1': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/dict\/v1/, '/dict/view/v1'),
            target: BASE_HOST,
          },
          '/file-manage/view/v1': {
            changeOrigin: true,
            rewrite: (path) =>
              path.replace(/^\/file-manage\/view\/v1/, '/file-manage/view/v1'),
            target: BASE_HOST,
          },
          '/rule/v1': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/rule\/v1/, '/rule/view/v1'),
            target: BASE_HOST,
          },
          '/system/v1': {
            changeOrigin: true,
            rewrite: (path) => path.replace(/^\/system\/v1/, '/system/view/v1'),
            target: BASE_HOST,
          },
          '/warehouse/v1': {
            changeOrigin: true,
            rewrite: (path) =>
              path.replace(/^\/warehouse\/v1/, '/warehouse/view/v1'),
            target: BASE_HOST,
          },
          '/workflow/v1': {
            changeOrigin: true,
            rewrite: (path) =>
              path.replace(/^\/workflow\/v1/, '/workflow/view/v1'),
            target: BASE_HOST,
          },
          '/message/v1': {
            changeOrigin: true,
            rewrite: (path) =>
              path.replace(/^\/message\/v1/, '/message/view/v1'),
            target: BASE_HOST,
          },
        },
      },
    },
  };
});
