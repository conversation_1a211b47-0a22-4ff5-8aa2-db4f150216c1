<script lang="ts" setup>
import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElButton, ElMessage, ElMessageBox, ElScrollbar } from 'element-plus';

import { closeAssemblyDoc, execAssemblyDoc, getAssemblyDocDetail } from '#/api';
import ApprovalTimeline from '#/components/approval-timeline/Index.vue';
import FormCard from '#/components/form-card/Index.vue';
import DisassemblyFormView from '#/views/warehouse-management/disassembly-management/components/disassembly-view-form/index.vue';
import MaterialFormView from '#/views/warehouse-management/disassembly-management/components/material-view-form/index.vue';

const loading = ref(false);
/** 共享数据 */
const sharedData = ref();

const [Modal, modalApi] = useVbenModal({
  showConfirmButton: false,
  cancelText: '关闭',
  closeOnClickModal: false,
  onCancel() {
    modalApi.close();
  },
  onOpenChange(isOpen: boolean) {
    if (isOpen) {
      sharedData.value = modalApi.getData<Record<string, any>>();
      initModal();
    }
  },
});

/** 初始化 */
const initModal = async () => {
  loading.value = true;
  try {
    // 如果没有审核流程，发送请求获取
    if (!sharedData.value.processInstanceId) {
      const res = await getAssemblyDocDetail(
        sharedData.value.assemblyDocId,
        sharedData.value.assemblyDocNumber,
      );
      sharedData.value.processInstanceId = res?.processInstanceId;
    }
  } catch (error) {
    console.error(error);
  } finally {
    loading.value = false;
  }
};

/** 刷新列表 */
const refreshList = () => {
  sharedData.value.refreshList();
  modalApi.close();
};

/** 公共弹窗 */
const dialog = async (content: string, title: string) => {
  try {
    await ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    });
    return true;
  } catch {
    return false;
  }
};

/** 自动完成出入库 */
const autoExecute = async () => {
  try {
    if (await dialog('确定执行吗？', '提示')) {
      await execAssemblyDoc(sharedData.value.assemblyDocId);
      ElMessage.success('执行成功');
      refreshList();
    }
  } catch {
    ElMessage.error('执行失败');
  }
};

/** 关闭拆卸单据 */
const closeDoc = async () => {
  try {
    if (await dialog('确定取消吗？', '提示')) {
      await closeAssemblyDoc(sharedData.value.assemblyDocId);
      ElMessage.success('取消成功');
      refreshList();
    }
  } catch {
    ElMessage.error('取消失败');
  }
};
</script>
<template>
  <Modal>
    <div v-loading="loading">
      <DisassemblyFormView
        :assembly-doc-id="sharedData.assemblyDocId"
        :assembly-doc-number="sharedData.assemblyDocNumber"
      />

      <MaterialFormView
        :assembly-doc-id="sharedData.assemblyDocId"
        :assembly-doc-number="sharedData.assemblyDocNumber"
      />

      <FormCard :is-footer="false" v-if="sharedData.processInstanceId">
        <template #title>
          <span>审核流程</span>
        </template>
        <ElScrollbar>
          <ApprovalTimeline
            :process-instance-id="sharedData.processInstanceId"
          />
        </ElScrollbar>
      </FormCard>
    </div>

    <template #prepend-footer>
      <ElButton
        v-access:code="['wm:assembly:split:exec']"
        v-if="sharedData.isShowAutoIo"
        @click="autoExecute"
        type="primary"
      >
        完成拆卸
      </ElButton>
      <ElButton
        v-access:code="['wm:assembly:split:close']"
        v-if="sharedData.isShowClose"
        @click="closeDoc"
        type="danger"
      >
        取消单据
      </ElButton>
    </template>
  </Modal>
</template>
