import type { RouteRecordStringComponent } from '@vben/types';

import { requestClient } from '#/api/request';

import { systemPath } from '../path';
/**
 * 获取用户所有菜单
 */
export async function getAllMenusApi() {
  return requestClient.get<RouteRecordStringComponent[]>(
    `${systemPath}/perms/mi/getPerms`,
  );
}

export namespace SystemMenuApi {
  /** 徽标颜色集合 */
  export const BadgeVariants = [
    'default',
    'destructive',
    'primary',
    'success',
    'warning',
  ] as const;
  /** 徽标类型集合 */
  export const BadgeTypes = ['dot', 'normal'] as const;
  /** 菜单类型集合 */
  export const MenuTypes = [
    'catalog',
    'menu',
    'embedded',
    'link',
    'button',
  ] as const;
  /** 系统菜单 */
  export interface SystemMenu {
    [key: string]: any;
  }
}
/**
 * 获取菜单数据列表
 */
export async function getMenuList() {
  return requestClient.get<Array<SystemMenuApi.SystemMenu>>(
    `${systemPath}/perms/getAllMenuTree`,
  );
}
