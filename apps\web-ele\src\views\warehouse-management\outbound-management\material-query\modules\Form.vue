<script lang="ts" setup>
import type { MaterialPendingApi } from '#/api/warehouse-management/index';

import { onMounted, ref } from 'vue';

import { ElMessage, ElMessageBox } from 'element-plus';

import {
  closePrepDoc,
  execPrepDoc,
  getPrepDocDetail,
  rollbackPrepDoc,
} from '#/api/warehouse-management/index';

import TargetLocationId from '../../components/location-item/form-view/index.vue';
import OutboundInfo from '../../material-pending/modules/outbound-info/index.vue';
import PrepDocInfo from './prepDoc-info/index.vue';
import PrepMaterialsInfo from './prepMaterials-info/index.vue';

const props = defineProps({
  // 备料单ID
  prepDocId: {
    type: String,
    default: '',
  },
  // 备料单号
  prepDocNumber: {
    type: String,
    default: '',
  },
  // 出库单ID
  outBoundDocId: {
    type: String,
    default: '',
  },
  // 出库单号
  outBoundDocNumber: {
    type: String,
    default: '',
  },
});

const emits = defineEmits(['boundSuccess', 'boundLoading', 'handleCancel']);

const prepDocDetail = ref<MaterialPendingApi.PrepDocDetail>(
  {} as MaterialPendingApi.PrepDocDetail,
);

/** 获取数据 */
const getPrepDocDetailHandle = async () => {
  try {
    const prepDocDetailRes = await getPrepDocDetail({
      prepDocId: props.prepDocId,
      isQueryItem: true,
    });
    prepDocDetail.value = prepDocDetailRes;
    return prepDocDetailRes;
  } catch {
    ElMessage.error('获取备料单据失败');
    return {} as MaterialPendingApi.PrepDocDetail;
  }
};

onMounted(async () => {
  if (props.prepDocId) {
    const data = await getPrepDocDetailHandle();
    prepDocDetail.value = data;
  }
});

/** 执行备料单 */
const submitAllForm = async () => {
  const formData = prepDocDetail.value.prepItemList.flatMap((item) => {
    return item.itemList.map((subItem) => {
      const {
        warehouseId,
        oldLocationId,
        targetLocationId,
        batchNumber,
        transferQuantity,
      } = subItem;
      return {
        warehouseId,
        oldLocationId,
        targetLocationId,
        batchNumber,
        transferQuantity,
        materialId: item.materialId,
      };
    });
  });

  const mergedData = {
    prepDocId: prepDocDetail.value.prepDocId,
    prepItemList: formData,
  };

  try {
    // 正在提交
    emits('boundLoading', true);
    await execPrepDoc(
      mergedData as unknown as MaterialPendingApi.PrepDocParams,
    );
    ElMessage.success('备料成功');
    emits('boundLoading', false);
    emits('boundSuccess');
  } catch {
    emits('boundLoading', false);
  }
};

/** 关闭备料单据 */
const closePrepDocHandle = async () => {
  if (
    await ElMessageBox.confirm('确定关闭备料单据吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    await closePrepDoc(props.prepDocId);
    emits('boundSuccess');
  }
};

/** 备料单据返仓 */
const rollbackPrepDocHandle = async () => {
  if (
    await ElMessageBox.confirm('确定返仓备料单据吗？', '提示', {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
  ) {
    await rollbackPrepDoc(props.prepDocId);
    emits('boundSuccess');
  }
};

const handleCancel = () => {
  emits('handleCancel');
};

/** 暴露方法 */
defineExpose({
  submitAllForm,
});
</script>

<template>
  <div class="relative mb-12 h-full">
    <PrepDocInfo
      :prep-doc-id="props.prepDocId"
      :prep-doc-number="props.prepDocNumber"
    />

    <TargetLocationId
      :prep-doc-id="props.prepDocId"
      :prep-doc-number="props.prepDocNumber"
    />

    <OutboundInfo
      :out-bound-doc-id="props.outBoundDocId"
      :out-bound-doc-number="props.outBoundDocNumber"
    />

    <PrepMaterialsInfo
      :prep-doc-id="props.prepDocId"
      :prep-doc-number="props.prepDocNumber"
    />

    <div
      class="fixed bottom-0 right-0 z-10 h-[55px] w-full rounded-b-lg border-t bg-white p-4 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="submitAllForm"
        v-if="prepDocDetail.docStatus === 'passed'"
        v-access:code="'wm:outbound:prep:exec'"
      >
        执行
      </ElButton>
      <ElButton
        type="primary"
        @click="rollbackPrepDocHandle"
        v-if="prepDocDetail.docStatus === 'prepared'"
        v-access:code="'wm:outbound:prep:rollback'"
      >
        返仓
      </ElButton>
      <ElButton
        type="danger"
        @click="closePrepDocHandle"
        v-if="prepDocDetail.docStatus === 'passed'"
        v-access:code="'wm:outbound:prep:close'"
      >
        取消备料
      </ElButton>
    </div>
  </div>
</template>
