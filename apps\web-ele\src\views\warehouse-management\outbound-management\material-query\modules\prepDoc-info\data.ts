import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '-');
      },
      fieldName: 'prepDocNumber',
      label: '备料单号',
    },
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '-');
      },
      fieldName: 'submitUserName',
      label: '申请人',
    },

    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '-');
      },
      fieldName: 'submitTime',
      label: '提交时间',
    },

    {
      component: 'Input',
      fieldName: 'docProcess',
      label: '单据流程',
      formItemClass: 'col-span-full',
    },
  ];
}
