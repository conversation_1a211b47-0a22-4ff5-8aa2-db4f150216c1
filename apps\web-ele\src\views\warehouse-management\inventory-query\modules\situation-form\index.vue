<script setup lang="ts">
import type { InventoryQueryApi } from '#/api';

import { defineAsyncComponent, onMounted, ref } from 'vue';

import { useAccess } from '@vben/access';
import { useVbenModal } from '@vben/common-ui';

import { useVbenForm } from '@girant/adapter';
import { ElAlert, ElButton, ElIcon, ElMessage } from 'element-plus';

import { getInvcByWarehouseIdAndMaterialId } from '#/api';
import FormCard from '#/components/form-card/Index.vue';

import { useFormSchema } from './data';

const props = defineProps({
  /** 物料id */
  materialId: {
    type: String,
    default: '',
  },
  /** 仓库id*/
  warehouseId: {
    type: String,
    default: '',
  },
});
const { hasAccessByCodes } = useAccess();
const loading = ref(false);
/** 库存详情 */
const inventoryData = ref<InventoryQueryApi.InventoryData>();
/** 模态框组件 锁库*/
const [FormModal, formModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(() => import('./LockModal.vue')),
  destroyOnClose: true,
});
/** 模态框组件 解锁*/
const [UnlockFormModal, unlockformModalApi] = useVbenModal({
  connectedComponent: defineAsyncComponent(
    () => import('./UnlockFormModal.vue'),
  ),
  destroyOnClose: true,
});
/** 库存情况表单 */
const [Form, formApi] = useVbenForm({
  commonConfig: { componentProps: { class: 'w-full' } },
  schema: useFormSchema(),
  showDefaultActions: false,
  wrapperClass: 'grid-cols-2 xl:grid-cols-3',
});

/** 获取数据 */
const getData = async () => {
  try {
    loading.value = true;
    const data = await getInvcByWarehouseIdAndMaterialId(
      props.warehouseId,
      props.materialId,
    );
    inventoryData.value = data;
    // 设置表单数据
    formApi.setValues(data);
  } catch {
    ElMessage.error('获取数据失败');
  } finally {
    loading.value = false;
  }
};
/** 锁库存 */
const lockInventory = () => {
  formModalApi
    .setData({
      inventoryData,
      refreshForm: () => {
        formModalApi.close();
        getData();
      },
    })
    .open();
};
/** 解锁库存 */
const unlockInventario = () => {
  unlockformModalApi
    .setState({
      title: '解锁列表',
    })
    .setData({
      materialId: props.materialId,
      warehouseId: props.warehouseId,
      refreshForm: () => {
        unlockformModalApi.close();
        getData();
      },
    })
    .open();
};
/** 申请采购 */
const purchase = () => {
  ElMessage.success('申请采购 待开发');
};
onMounted(() => {
  if (props.materialId && props.warehouseId) {
    getData();
  }
});
defineExpose({
  FormModal,
  formModalApi,
  Form,
  formApi,
});
</script>
<template>
  <FormModal class="h-full w-10/12" />
  <UnlockFormModal class="h-full w-10/12" />
  <FormCard :is-footer="false" v-loading="loading">
    <template #title>
      <span>库存情况</span>
    </template>
    <Form>
      <template #availableInventory="row">
        {{ row.value }}
        <ElButton
          v-if="row.value > 0"
          class="ml-[10px]"
          link
          type="primary"
          @click="lockInventory()"
          v-access:code="'wm:inventory:lock:lock'"
        >
          锁库存
        </ElButton>
      </template>
      <template #blockQuantity="row">
        <ElButton
          link
          type="primary"
          @click="unlockInventario()"
          v-access:code="'wm:inventory:lock:unlock'"
        >
          <span class="text-[16px] underline">
            {{ row.value }}
          </span>
        </ElButton>
        <span
          class="text-[16px]"
          v-if="!hasAccessByCodes(['wm:inventory:lock:unlock'])"
        >
          {{ row.value }}
        </span>
      </template>
      <template #safetyInventory="row">
        <span v-if="!row.value"> 暂无配置 </span>
        <div v-else class="flex flex-nowrap items-center">
          <span>{{ row.value }}</span>
          <span
            v-if="!inventoryData?.isJoinSafetyStockWarn"
            class="ml-[10px] text-[14px]"
          >
            (不做库存预警)
          </span>
          <span class="ml-[10px]" v-if="inventoryData?.isSafetyStockWarn">
            <ElAlert type="error" show-icon :closable="false">
              <template #icon>
                <ElIcon :size="16">
                  <WarningFilled />
                </ElIcon>
              </template>
              <template #title>
                当前可用量是{{
                  inventoryData?.availableInventory || 0
                }},低于安全库存！
              </template>
            </ElAlert>
          </span>
          <ElButton class="ml-[14px]" link type="primary" @click="purchase">
            申请采购
          </ElButton>
        </div>
      </template>
      <template #obsoletePeriod="row">
        <span v-if="!row.value"> 暂无配置 </span>
        <div v-else class="flex flex-nowrap items-center">
          <span>{{ row.value }}天</span>
          <span
            class="ml-[10px] text-[14px]"
            v-if="!inventoryData?.isJoinObsoleteAnalysis"
          >
            (不做呆滞分析)
          </span>
          <span class="ml-[10px]" v-if="inventoryData?.isObsoleteAnalysis">
            <ElAlert type="warning" show-icon :closable="false">
              <template #icon>
                <ElIcon :size="16">
                  <WarnTriangleFilled />
                </ElIcon>
              </template>
              <template #title>
                已呆滞{{ inventoryData?.obsoleteDay || 0 }}天
              </template>
            </ElAlert>
          </span>
        </div>
      </template>
    </Form>
  </FormCard>
</template>
