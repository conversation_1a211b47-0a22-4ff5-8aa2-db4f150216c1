import type { UserInfo } from '@vben/types';

import { requestClient } from '#/api/request';

import { baseDataPath, systemPath } from '../path';

/**
 * 获取当前登录的用户信息
 */
export async function getUserInfoApi() {
  return requestClient.get<UserInfo>(`${systemPath}/user/mi/getUserInfo`);
}

/**
 * 获取当前登录用户关联的客户信息
 */
export async function getCustomerinfo() {
  return requestClient.get(`${baseDataPath}/base/customer/mi/getCustomerInfo`);
}

/**
 * 获取当前登录用户关联的供应商信息
 */
export async function getSupplierinfo() {
  return requestClient.get(`${baseDataPath}/base/supplier/mi/getSupplierInfo`);
}

/**
 * 获取当前登录用户关联的员工信息
 */
export async function getStaffInfo() {
  return requestClient.get(`${baseDataPath}/base/staff/mi/getStaffInfo`);
}

/**
 * 修改我的用户信息
 */
export async function updateUserInfo(data: any) {
  return requestClient.post(`${systemPath}/user/mod/mi/user`, data);
}
