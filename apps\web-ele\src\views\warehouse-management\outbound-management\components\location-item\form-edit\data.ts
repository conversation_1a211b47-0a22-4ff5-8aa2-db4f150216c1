import type { VbenFormSchema } from '@girant/adapter';

import { h } from 'vue';

import { z } from '@girant/adapter';

/** 表单 */
export function useFormSchema(): VbenFormSchema[] {
  return [
    {
      component: (props: any) => {
        return h('div', null, props.modelValue || '/');
      },
      fieldName: 'warehouseName',
      label: '仓库',
    },
    {
      component: 'Input',
      fieldName: 'warehouseId',
      label: '仓库ID',
      formItemClass: 'hidden',
    },
    {
      component: 'Input',
      fieldName: 'warehouseCode',
      label: '仓库编号',
      formItemClass: 'hidden',
    },

    {
      component: 'Select',
      componentProps: {
        clearable: true,
        allowClear: true,
        filterOption: true,
        options: [],
        placeholder: '请选择',
        showSearch: true,
      },
      fieldName: 'locationId',
      label: '库位',
      rules: z.string().min(1, '请选择库位'),
    },
  ];
}
