import { requestClient } from '#/api/request';

import { systemPath } from '../path';

/** @description 获取所有菜单树*/
export async function getAllMenuTree() {
  return requestClient.get(`${systemPath}/perms/getAllMenuTree`);
}

/** @description 删除菜单*/
export async function delMenu(menuId: string) {
  return requestClient.get(`${systemPath}/perms/delMenu/${menuId}`);
}

/** @description 删除按钮*/
export async function delBtn(permissionId: string) {
  return requestClient.get(`${systemPath}/perms/delBtn/${permissionId}`);
}
