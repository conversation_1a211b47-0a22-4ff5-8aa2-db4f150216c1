import { getPeriodTime } from '#/api';

interface PeriodTimeData {
  timeStart: string;
  timeEnd: string;
}

/**
 * 获取周期时间数据
 * @param periodType 区间计算类型（字典）,00-近,01-本,02-周期
 * @param periodUnit 时间区间单位（字典）,00-日,10-周,20-月,23-季度,25-半年,30-年
 * @param interval  时间周期间隔数,区分正负,正数表示未来,负数表示过去
 * @returns Promise，解析为接口返回的数据
 */

export function fetchPeriodTime(
  periodType: string,
  periodUnit: string,
  interval: string,
): Promise<PeriodTimeData> {
  return getPeriodTime({ periodType, periodUnit, interval })
    .then((response) => {
      if (!response) {
        throw new Error('接口返回数据为空');
      }
      return response;
    })
    .catch((error) => {
      console.error('获取周期时间出错:', error);
      throw error; // 重新抛出错误，保持Promise链的拒绝状态
    });
}
