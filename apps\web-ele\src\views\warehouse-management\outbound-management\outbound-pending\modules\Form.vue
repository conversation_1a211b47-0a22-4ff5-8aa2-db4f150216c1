<script lang="ts" setup>
import type { OutboundPendingApi } from '#/api/warehouse-management/index';

import { ref } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { ElMessage, ElMessageBox } from 'element-plus';

import { execOutBoundDoc, reconfirm } from '#/api/warehouse-management/index';

import MoreInfo from '../../components/more-info/form-edit/index.vue';
import ConfirmationMethod from './confirmation-method/index.vue';
import MaterialsInfo from './materials-info/index.vue';
import OutboundInfo from './outbound-info/index.vue';

const props = defineProps({
  outBoundDocId: {
    type: String,
    default: '',
  },
  outBoundDocNumber: {
    type: String,
    default: '',
  },
});

const emits = defineEmits([
  'boundSuccess',
  'boundLoading',
  'openPrepModal',
  'handleCancel',
]);

// 二次确认的密码
const confirmPassword = ref('');

const finalCollectValue = ref('');
const finalCollectorConfirmMethod = ref('');

const materialsInfoRef = ref<InstanceType<typeof MaterialsInfo>>();
const moreInfoRef = ref<InstanceType<typeof MoreInfo>>();
const confirmationMethodRef = ref<InstanceType<typeof ConfirmationMethod>>();

/** 二次确认弹窗 */
const [ConfirmModal, confirmModalApi] = useVbenModal({
  confirmText: '确认',
  onBeforeClose: () => {
    confirmPassword.value = '';
    return true;
  },
  destroyOnClose: true,
  onConfirm: () => {
    confirmSubmit();
  },
  centered: true,
  closeOnClickModal: false,
  fullscreenButton: false,
  showCancelButton: true,
  zIndex: 2001,
});

/** 领料确认方式弹窗 */
const [ExecuteModal, executeModalApi] = useVbenModal({
  // confirmText: '确认出库',
  onBeforeClose: () => {
    return true;
  },
  destroyOnClose: true,
  onConfirm: () => {
    getCollectValueAndCollectorConfirmMethod();
  },
  centered: true,
  footer: false,
  closeOnClickModal: false,
  fullscreenButton: false,
  showCancelButton: true,
  zIndex: 2000,
});

const lastOutboundInfo = ref<
  Array<{
    baseUnitLabel: string;
    entryQuantitySum: number;
    materialId: string;
    materialName: string;
    quantitySum: number;
  }>
>([]);

/** 点击确认出库 */
const handleSubmit = async () => {
  ElMessageBox.confirm('确认执行出库吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      const subData = await materialsInfoRef.value?.getSubData();
      if (!subData || subData.length === 0) {
        return;
      }

      const lastOutboundInfoData =
        (await materialsInfoRef.value?.getLastOutboundInfo()) || [];
      lastOutboundInfo.value = lastOutboundInfoData;
      executeModalApi.open();
    })
    .catch(() => {});
};

/** 确认出库 */
const handleConfirmSubmit = () => {
  getCollectValueAndCollectorConfirmMethod();
};

/** 获取领料码、领料方式 */
const getCollectValueAndCollectorConfirmMethod = () => {
  const { collectValueData, collectorConfirmMethodData } =
    confirmationMethodRef.value?.getConfirmData() || {};

  if (!collectValueData || !collectorConfirmMethodData) {
    ElMessage.error('请选择领料方式和领料码');
    return;
  }

  finalCollectValue.value = collectValueData;
  finalCollectorConfirmMethod.value = collectorConfirmMethodData;

  submitAllForm();
};

/** 执行出库 */
const submitAllForm = async () => {
  executeModalApi.setState({ loading: true });
  const materialsInfoData = await materialsInfoRef.value?.getSubData();
  if (!materialsInfoData || materialsInfoData.length === 0) {
    ElMessage.error('请检查出库明细数据');
    return;
  }
  const moreInfoData = await moreInfoRef.value?.getFormData();

  const mergeData = {
    ...moreInfoData,
    collectorConfirmMethod: finalCollectorConfirmMethod.value,
    collectValue: finalCollectValue.value,
    outBoundDocId: props.outBoundDocId,
    actualItemList: materialsInfoData,
  };

  try {
    // 正在提交
    emits('boundLoading', true);
    await execOutBoundDoc(
      mergeData as unknown as OutboundPendingApi.ExecOutboundParams,
    );
    ElMessage.success('出库成功');
    executeModalApi.close();
    emits('boundLoading', false);
    emits('boundSuccess');
    finalCollectValue.value = '';
    finalCollectorConfirmMethod.value = '';
  } catch (error: any) {
    executeModalApi.setState({ loading: false });
    emits('boundLoading', false);
    if (error.code === 10_004_032) {
      confirmModalApi.open();
    } else {
      finalCollectValue.value = '';
      finalCollectorConfirmMethod.value = '';
    }
  }
};

/** 二次确认 */
const confirmSubmit = async () => {
  // 判断密码不能为空，不能是空格
  if (!confirmPassword.value || confirmPassword.value.trim() === '') {
    ElMessage.error('密码不能为空');
    return;
  }

  try {
    confirmModalApi.setState({ loading: true });
    await reconfirm(confirmPassword.value);
    submitAllForm();
    confirmModalApi.close();
  } catch {
    confirmModalApi.setState({ loading: false });
  }
};

/** 备料单据返仓 */
const rollbackPrepDocHandle = () => {
  emits('boundLoading', true);
};

/** 备料单据返仓成功 */
const rollbackSuccessHandle = () => {
  emits('boundLoading', false);
  emits('boundSuccess');
};

/** 打开备料单据 */
const openPrepModal = () => {
  emits('openPrepModal', props.outBoundDocId, props.outBoundDocNumber);
};

const handleCancel = () => {
  emits('handleCancel');
};

/** 暴露方法 */
defineExpose({
  submitAllForm,
});
</script>

<template>
  <ElButton @click="submitAllForm"> 确认出库 </ElButton>
  <div class="relative h-full">
    <ConfirmModal title="出库前需要二次确认">
      请输入当前账号密码：
      <el-input
        v-model="confirmPassword"
        placeholder="请输入密码"
        class="mt-2 w-full"
        type="password"
        show-password
        @keyup.enter="confirmSubmit"
      />
    </ConfirmModal>

    <ExecuteModal title="提示">
      <ConfirmationMethod
        ref="confirmationMethodRef"
        :out-bound-doc-id="props.outBoundDocId"
        :last-outbound-info="lastOutboundInfo"
        @submit-confirmation="handleConfirmSubmit"
        @cancel-confirmation="executeModalApi.close()"
      />
    </ExecuteModal>

    <div class="pb-10">
      <OutboundInfo
        :out-bound-doc-id="props.outBoundDocId"
        :out-bound-doc-number="props.outBoundDocNumber"
        @open-prep-modal="openPrepModal"
        @rollback-prep-doc="rollbackPrepDocHandle"
        @rollback-success="rollbackSuccessHandle"
      />
      <MaterialsInfo
        ref="materialsInfoRef"
        :out-bound-doc-id="props.outBoundDocId"
        :out-bound-doc-number="props.outBoundDocNumber"
      />
      <MoreInfo ref="moreInfoRef" />
    </div>

    <div
      class="fixed bottom-0 right-0 z-10 h-[50px] w-full rounded-b-lg border-t bg-white p-2 text-right"
    >
      <ElButton type="info" @click="handleCancel"> 取消 </ElButton>
      <ElButton
        type="primary"
        @click="handleSubmit"
        v-access:code="'wm:outbound:exec'"
      >
        确认出库
      </ElButton>
    </div>
  </div>
</template>
