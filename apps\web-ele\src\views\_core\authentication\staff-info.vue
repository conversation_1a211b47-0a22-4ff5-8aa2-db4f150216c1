<script setup lang="ts">
import { h, onMounted, ref } from 'vue';

import { useUserStore } from '@vben/stores';

import { UploadPic } from '@girant-web/upload-pic-component';
import { useVbenForm, z } from '@girant/adapter';
import { ElMessage, ElMessageBox } from 'element-plus';

import { updateUserInfo } from '#/api';
import { getMenuTreeByRoleIds } from '#/api/system-settings';
import { useAuthStore } from '#/store';

const emits = defineEmits(['personalUpdateSuccess']);
const authStore = useAuthStore();
const userStore = useUserStore();
const userInfo = userStore.userInfo;

const loading = ref(false);

const handleInput = (value: string) => {
  // 过滤掉所有空格字符（包括中间空格）
  return value.replaceAll(/\s+/g, '');
};

/** 确认框 */
const confirm = (content: string, title: string) => {
  return new Promise((resolve) => {
    ElMessageBox.confirm(content, title, {
      cancelButtonText: '取消',
      confirmButtonText: '确定',
      type: 'warning',
    })
      .then(() => {
        resolve(true);
      })
      .catch(() => {});
  });
};

const onSubmit = async (values: Record<string, any>) => {
  try {
    await confirm(`确定保存吗？`, `提示`);
    values.fixedMenuIds = values.openMenuIds;
    loading.value = true;
    await updateUserInfo(values);
    ElMessage.success('保存成功');

    await authStore.fetchUserInfo();
    emits('personalUpdateSuccess');
  } catch (error: any) {
    ElMessage.error(error.message || '保存失败');
  } finally {
    loading.value = false;
  }
};

const [Form, FormApi] = useVbenForm({
  handleSubmit: onSubmit,
  schema: [
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      dependencies: {
        trigger(values: any) {
          if (values.username) {
            values.username = handleInput(values.username);
          }
          return values.username;
        },
        triggerFields: ['username'],
      },
      fieldName: 'username',
      label: '账号',
      rules: z
        .string()
        .min(3, { message: '最少输入3个字符' })
        .max(64, { message: '最多输入64个字符' }),
    },
    {
      component: 'Input',
      componentProps: {
        clearable: true,
      },
      fieldName: 'nickname',
      label: '昵称',
      rules: z
        .string()
        .min(1, { message: '请输入昵称' })
        .max(64, { message: '最多输入64个字符' }),
    },
    {
      component: h(UploadPic),
      modelPropName: 'imgId',
      fieldName: 'avatar',
      formItemClass: 'col-span-full',
      label: '头像',
    },
    {
      component: 'ApiTreeSelect',
      componentProps: {
        allowClear: true,
        showSearch: true,
        treeNodeFilterProp: 'label',
        placeholder: '请选择',
        multiple: true,
        afterFetch: (data: any) => {
          function convertMenuData(menu: any) {
            return {
              label: menu.menuName,
              value: menu.menuId,
              children: menu.children
                ? menu.children.map((child: any) => convertMenuData(child))
                : [],
            };
          }
          // 执行转换
          const convertedData = data.map((dept: any) => convertMenuData(dept));
          return convertedData;
        },
        api: () => {
          return getMenuTreeByRoleIds({
            clientType: '01',
            roleIds: userInfo?.roleIdList,
          });
        },
      },
      fieldName: 'openMenuIds',
      formItemClass: 'col-span-full',
      label: '默认打开菜单',
    },
    {
      component: 'Textarea',
      componentProps: {
        maxlength: 1000,
        placeholder: '请输入',
        showWordLimit: true,
      },
      dependencies: {
        rules(values: any) {
          if (values.remark) {
            return z.string().max(1000, { message: '最多输入1000个字符' });
          }
          return null;
        },
        triggerFields: ['remark'],
      },
      fieldName: 'remark',
      formItemClass: 'col-span-full items-start',
      label: '个人简介',
    },
  ],
  showDefaultActions: false,
  wrapperClass: 'sm:grid-cols-1  md:grid-cols-2',
});

onMounted(() => {
  FormApi.setValues(userStore.userInfo ?? {});
});

defineExpose({
  FormApi,
});
</script>
<template>
  <Form v-loading="loading" />
</template>
