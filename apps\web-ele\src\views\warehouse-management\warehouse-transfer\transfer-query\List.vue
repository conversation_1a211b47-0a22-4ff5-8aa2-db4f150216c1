<script></script>
<script setup lang="ts">
import type { VxeTableGridOptions } from '@girant/adapter';

import type { PropType } from 'vue';

import type { RowType } from './data';

import { onMounted, ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { downloadFileFromResponse, isEmpty } from '@vben/utils';

import { useVbenVxeGrid } from '@girant/adapter';
import { ElMessage, ElMessageBox, ElTooltip } from 'element-plus';

import {
  closeTransferDoc,
  execTransferDoc,
  exportTransferDoc,
  getTransferDocPage,
} from '#/api/warehouse-management';
import { isAfter, isBefore } from '#/utils/dateUtils';

import { useColumns, useGridFormSchema } from './data';
import Form from './modules/Form.vue';

const props = defineProps({
  attr: {
    type: Object,
    default: () => ({}),
  },
  params: {
    type: Object as PropType<{
      docStatusList?: string;
      executorUserList?: string;
      finishEndTime?: string;
      finishStartTime?: string;
      submitEndTime?: string;
      submitStartTime?: string;
      submitUserList?: string;
      transferDocNumberList?: string;
      warehouseIdList?: string;
    }>,
    default: () => ({}),
  },
});

const transferDocId = ref<string>('');
const transferDocNumber = ref<string>('');
const docStatus = ref<string>('');

const isView = ref<boolean>(false);

/** 提交时间 */
const submitTime = ref({
  // 开始时间
  submitStartTime: props.params?.submitStartTime || '',
  // 结束时间
  submitEndTime: props.params?.submitEndTime || '',
});

const finishTime = ref({
  // 开始时间
  finishStartTime: props.params?.finishStartTime || '',
  // 结束时间
  finishEndTime: props.params?.finishEndTime || '',
});

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions: {
    handleReset: async () => {
      // 重置表单
      await gridApi.formApi.resetForm();
      // 处理重置不了的字段
      submitTime.value = {
        submitStartTime: '',
        submitEndTime: '',
      };

      gridApi.query();
    },
    commonConfig: {
      // 所有表单项
      componentProps: {
        class: 'w-full',
      },
      labelClass: 'min-w-[75px]',
    },
    schema: useGridFormSchema(),
    collapsed: isEmpty(props.attr?.collapsed) ? true : props.attr?.collapsed,
    showCollapseButton: isEmpty(props.attr?.showCollapseButton)
      ? true
      : props.attr?.showCollapseButton,
    collapsedRows: props.attr?.collapsedRows || 2,
    wrapperClass:
      props.attr?.wrapperClass ||
      'grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-4',
  },
  gridOptions: {
    border: true,
    checkboxConfig: {
      highlight: true,
    },
    columns: useColumns(),
    height: 'auto',
    keepSource: true,
    proxyConfig: {
      ajax: {
        query: async ({ page }) => {
          const formValues = await gridApi.formApi.getValues();
          const params: any = {
            ...formValues,
            pageNum: page.currentPage,
            pageSize: page.pageSize,
          };

          params.submitStartTime = submitTime.value.submitStartTime;
          params.submitEndTime = submitTime.value.submitEndTime;

          if (params.origDocTypeCodeList) {
            params.origDocTypeCodeList = params.origDocTypeCodeList.join(',');
          }

          if (params.applyUserList) {
            params.applyUserList = params.applyUserList.join(',');
          }

          if (params.docStatusList) {
            params.docStatusList = params.docStatusList.join(',');
          }

          return await getTransferDocPage(params);
        },
      },
      response: {
        result: 'records',
        total: 'total',
      },
      showActiveMsg: true,
      showResponseMsg: false,
    },
    rowConfig: {
      keyField: 'transferDocId',
    },
    // showOverflow: false,
    toolbarConfig: {
      custom: true,
      refresh: { code: 'query' },
    },
  } as VxeTableGridOptions<any>,
});

onMounted(() => {
  gridApi.formApi.setValues({
    transferDocNumberList:
      props.params?.transferDocNumberList?.split(',') || [],
    warehouseIdList: props.params?.warehouseIdList?.split(',') || [],
    submitUserList: props.params?.submitUserList?.split(',') || [],
    executorUserList: props.params?.executorUserList?.split(',') || [],
    submitStartTime: props.params?.submitStartTime || '',
    submitEndTime: props.params?.submitEndTime || '',
    finishStartTime: props.params?.finishStartTime || '',
    finishEndTime: props.params?.finishEndTime || '',
    docStatusList: props.params?.docStatusList?.split(',') || [],
  });
});

const [FormModal, formModalApi] = useVbenModal({
  confirmText: '新增调拨',
  destroyOnClose: true,
  onBeforeClose: () => {
    return true;
  },
  showCancelButton: true,
  closeOnClickModal: false,
  footer: false,
});

const onCreate = () => {
  transferDocId.value = '';
  transferDocNumber.value = '';
  isView.value = false;

  formModalApi
    .setState({
      title: '新增调拨',
    })
    .open();
};

const onView = (row: RowType) => {
  transferDocId.value = row.transferDocId;
  transferDocNumber.value = row.transferDocNumber;
  docStatus.value = row.docStatus;
  isView.value = true;
  formModalApi
    .setState({
      title: '调拨详情',
    })
    .open();
};

// const onEdit = (row: RowType) => {
//   transferDocId.value = row.transferDocId;
//   transferDocNumber.value = row.transferDocNumber;
//   docStatus.value = row.docStatus;
//   isView.value = false;
//   formModalApi.open();
// };

const execHandle = async (row: RowType) => {
  await ElMessageBox.confirm('确定确认调拨吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        await execTransferDoc(row.transferDocId);
        ElMessage.success('执行成功');
        gridApi.query();
      } catch {
        ElMessage.error('执行失败');
      }
    })
    .catch(() => {});
};

const closeHandle = async (row: RowType) => {
  await ElMessageBox.confirm('确定取消单据吗？', '提示', {
    cancelButtonText: '取消',
    confirmButtonText: '确定',
    type: 'warning',
  })
    .then(async () => {
      try {
        await closeTransferDoc(row.transferDocId);
        ElMessage.success('取消成功');
        gridApi.query();
      } catch {
        ElMessage.error('取消失败');
      }
    })
    .catch(() => {});
};

/** 数据导出 */
async function exportTransferDocHandle() {
  try {
    const formApi = gridApi.formApi;
    const formValues = await formApi.getValues();
    formValues.finishStartTime = finishTime.value.finishStartTime;
    formValues.finishEndTime = finishTime.value.finishEndTime;
    formValues.submitStartTime = submitTime.value.submitStartTime;
    formValues.submitEndTime = submitTime.value.submitEndTime;
    const response = await exportTransferDoc(formValues);
    downloadFileFromResponse(response);
    ElMessage.success('数据导出成功');
  } catch {
    ElMessage.error('数据导出失败');
  }
}

function onTransferLoading(loading: boolean) {
  formModalApi.setState({ loading });
}

function onTransferSuccess() {
  formModalApi.close();
  gridApi.query();
}
</script>

<template>
  <Page auto-content-height>
    <FormModal class="w-8/12">
      <Form
        :transfer-doc-id="transferDocId"
        :transfer-doc-number="transferDocNumber"
        :doc-status="docStatus"
        :is-view="isView"
        @submit-success="onTransferSuccess"
        @transfer-loading="onTransferLoading"
        @save-success="onTransferSuccess"
        @close-success="onTransferSuccess"
        @exec-success="onTransferSuccess"
        @handle-cancel="formModalApi.close()"
      />
    </FormModal>
    <Grid>
      <template #form-submitTime>
        <ElDatePicker
          v-model="submitTime.submitStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, submitTime.submitEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="submitTime.submitEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                submitTime.submitStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #form-finishTime>
        <ElDatePicker
          v-model="finishTime.finishStartTime"
          type="datetime"
          placeholder="开始日期"
          :disabled-date="
            (time: Date) =>
              isAfter(time, finishTime.finishEndTime || new Date('2099-12-31'))
          "
          value-format="YYYY-MM-DD HH:mm"
          format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          class="!w-full"
        />
        <span class="px-[10px] text-[16px]">-</span>
        <ElDatePicker
          v-model="finishTime.finishEndTime"
          type="datetime"
          placeholder="结束日期"
          :disabled-date="
            (time: Date) =>
              isBefore(
                time,
                finishTime.finishStartTime || new Date('1900-01-01'),
              )
          "
          value-format="YYYY-MM-DD HH:mm"
          time-format="HH:mm"
          format="YYYY-MM-DD HH:mm"
          :default-time="new Date(2000, 1, 1, 23, 59)"
          class="!w-full"
        />
      </template>

      <template #toolbar-actions>
        <ElButton
          type="primary"
          @click="onCreate"
          v-access:code="'wm:stock:transfer:submit'"
        >
          新增
        </ElButton>
      </template>

      <template #toolbar-tools>
        <ElTooltip
          class="box-item"
          effect="light"
          content="导出数据"
          placement="top-start"
        >
          <ElButton
            circle
            @click="exportTransferDocHandle"
            v-access:code="'wm:stock:transfer:export'"
          >
            <template #icon><span class="iconfont">&#xe670;</span></template>
          </ElButton>
        </ElTooltip>
      </template>

      <template #operation="{ row }">
        <ElButton type="info" size="small" link @click="onView(row)">
          查看
        </ElButton>
        <ElButton
          v-if="row.docStatus === 'passed'"
          size="small"
          link
          type="primary"
          @click="execHandle(row)"
          v-access:code="'wm:stock:transfer:exec'"
        >
          确认调拨
        </ElButton>
        <ElButton
          v-if="row.docStatus === 'passed'"
          size="small"
          link
          type="danger"
          @click="closeHandle(row)"
          v-access:code="'wm:stock:transfer:close'"
        >
          取消单据
        </ElButton>
      </template>
    </Grid>
  </Page>
</template>
